<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test MCP Debug Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }

        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            background: #2a2a2a;
            color: white;
            border: 1px solid #555;
            border-radius: 3px;
        }

        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #1976D2;
        }

        .response-container {
            margin: 10px 0;
            padding: 10px;
            background: #2a2a2a;
            border: 1px solid #555;
            border-radius: 3px;
        }

        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            height: 150px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔧 MCP Debug Panel Test</h1>

        <div class="test-section">
            <h3>Input Simulation</h3>
            <textarea id="ask-input" placeholder="Ask anything..."></textarea>
            <button aria-label="Submit">Submit</button>
            <button onclick="testInputDetection()">Test Input Detection</button>
        </div>

        <div class="test-section">
            <h3>Response Simulation</h3>
            <div class="response-container" data-testid="response-1">
                <p>This is a test response that contains an MCP tool call:</p>
                <code>mcpExecuteTool("file-server", "list_files", {"path": "/home/<USER>"})</code>
                <p>The system should detect and execute this automatically.</p>
            </div>
            <button onclick="addTestResponse()">Add Test Response</button>
            <button onclick="addToolCallResponse()">Add Tool Call Response</button>
        </div>

        <div class="test-section">
            <h3>Settings Test</h3>
            <p>Test the MCP settings controls in the debug panel. The panel should appear automatically when the content
                script loads.</p>
            <button onclick="toggleMcpBridge()">Toggle MCP Bridge</button>
            <button onclick="testToolExecution()">Test Tool Execution</button>
        </div>

        <div class="test-section">
            <h3>Console Log</h3>
            <div class="log" id="console-log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        // Override console.log to show in our log div
        const originalLog = console.log;
        const logDiv = document.getElementById('console-log');

        console.log = function (...args) {
            originalLog.apply(console, args);
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logDiv.innerHTML += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        };

        function testInputDetection() {
            console.log('🔍 Testing input detection...');
            const input = document.getElementById('ask-input');
            input.value = 'List the files in the current directory';
            input.dispatchEvent(new Event('input', { bubbles: true }));
            console.log('Input value set, should trigger MCP enhancement');
        }

        function addTestResponse() {
            console.log('📝 Adding test response...');
            const container = document.querySelector('.test-section:nth-child(3)');
            const responseDiv = document.createElement('div');
            responseDiv.className = 'response-container';
            responseDiv.setAttribute('data-testid', `response-${Date.now()}`);
            responseDiv.innerHTML = `
                <p>Generated response at ${new Date().toLocaleTimeString()}</p>
                <p>This response should be monitored for MCP tool calls.</p>
            `;
            container.appendChild(responseDiv);
            console.log('Test response added');
        }

        function addToolCallResponse() {
            console.log('🔧 Adding response with tool call...');
            const container = document.querySelector('.test-section:nth-child(3)');
            const responseDiv = document.createElement('div');
            responseDiv.className = 'response-container';
            responseDiv.setAttribute('data-testid', `response-tool-${Date.now()}`);
            responseDiv.innerHTML = `
                <p>Response with MCP tool call:</p>
                <pre>{"tool": "list_files", "parameters": {"path": "/test"}}</pre>
                <p>Or in code format:</p>
                <code>mcpExecuteTool("file-server", "read_file", {"path": "/test/file.txt"})</code>
            `;
            container.appendChild(responseDiv);
            console.log('Tool call response added - should be auto-detected');
        }

        function toggleMcpBridge() {
            if (window.mcpClient) {
                const currentState = window.mcpClient.settings.mcpEnabled;
                window.mcpClient.settings.mcpEnabled = !currentState;
                console.log(`MCP Bridge toggled to: ${!currentState}`);
            } else {
                console.log('❌ MCP Client not found');
            }
        }

        function testToolExecution() {
            if (window.mcpClient) {
                console.log('🧪 Testing tool execution...');
                window.mcpClient.executeDetectedToolCall({
                    tool: 'test_tool',
                    server: 'test-server',
                    parameters: { message: 'Hello from test!' }
                }, document.body);
            } else {
                console.log('❌ MCP Client not found');
            }
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        // Simulate some activity after page load
        setTimeout(() => {
            console.log('🚀 Page loaded, waiting for MCP client...');
            if (window.mcpClient) {
                console.log('✅ MCP Client detected');
                console.log('Settings:', window.mcpClient.settings);
                console.log('Servers:', window.mcpClient.mcpServers);
            } else {
                console.log('⏳ MCP Client not yet loaded');
            }
        }, 1000);

        // Check periodically for the client
        const checkInterval = setInterval(() => {
            if (window.mcpClient) {
                console.log('✅ MCP Client now available');
                console.log('Debug panel should be visible');
                clearInterval(checkInterval);
            }
        }, 500);
    </script>

    <!-- Load the content script -->
    <script src="extension/js/content.js"></script>
</body>

</html>