/* Modern Settings Page Styles - Perplexity Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.settings-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
    border-right: 1px solid #2a2a2a;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
}

.sidebar-header {
    padding: 24px 20px;
    text-align: center;
    border-bottom: 1px solid #2a2a2a;
}

.sidebar .logo {
    font-size: 32px;
    margin-bottom: 8px;
}

.sidebar .title {
    font-size: 16px;
    font-weight: 600;
    color: #20b2aa;
}

.nav-menu {
    list-style: none;
    padding: 16px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(32, 178, 170, 0.1);
    border-left-color: #20b2aa;
}

.nav-item.active {
    background: rgba(32, 178, 170, 0.15);
    border-left-color: #20b2aa;
    color: #20b2aa;
}

.nav-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 0;
    background: #0a0a0a;
}

.content-header {
    padding: 32px 40px 24px;
    border-bottom: 1px solid #2a2a2a;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 50;
}

.content-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Settings Sections */
.settings-section {
    display: none;
    padding: 40px;
    max-width: 800px;
}

.settings-section.active {
    display: block;
}

.settings-group {
    margin-bottom: 48px;
    background: rgba(26, 26, 26, 0.6);
    border: 1px solid #2a2a2a;
    border-radius: 12px;
    padding: 32px;
    backdrop-filter: blur(10px);
}

.settings-group h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffffff;
    border-bottom: 2px solid #20b2aa;
    padding-bottom: 8px;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.group-header h3 {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 0;
    border-bottom: 1px solid #2a2a2a;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    flex: 1;
    margin-right: 20px;
}

.setting-label label {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    display: block;
    margin-bottom: 4px;
}

.setting-description {
    font-size: 14px;
    color: #999999;
    line-height: 1.4;
}

.setting-control {
    min-width: 200px;
    text-align: right;
}

/* Form Controls */
.input, .select {
    width: 100%;
    padding: 12px 16px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    transition: all 0.2s ease;
}

.input:focus, .select:focus {
    outline: none;
    border-color: #20b2aa;
    box-shadow: 0 0 0 2px rgba(32, 178, 170, 0.2);
}

.select {
    cursor: pointer;
}

/* Toggle Switch */
.toggle {
    position: relative;
    display: inline-block;
    width: 52px;
    height: 28px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333333;
    transition: 0.3s;
    border-radius: 28px;
    border: 1px solid #2a2a2a;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: #666666;
    transition: 0.3s;
    border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
    background-color: #20b2aa;
    border-color: #20b2aa;
}

.toggle input:checked + .toggle-slider:before {
    transform: translateX(24px);
    background-color: #ffffff;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn.primary {
    background: linear-gradient(135deg, #20b2aa, #1a9a92);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(32, 178, 170, 0.3);
}

.btn.primary:hover {
    background: linear-gradient(135deg, #1a9a92, #158a82);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(32, 178, 170, 0.4);
}

.btn.secondary {
    background: #2a2a2a;
    color: #ffffff;
    border: 1px solid #404040;
}

.btn.secondary:hover {
    background: #333333;
    border-color: #20b2aa;
}

.btn.danger {
    background: #dc3545;
    color: #ffffff;
}

.btn.danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.button-group {
    display: flex;
    gap: 8px;
}

/* Servers List */
.servers-list {
    space-y: 16px;
}

.server-item {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.server-item:hover {
    border-color: #20b2aa;
    background: rgba(32, 178, 170, 0.05);
}

.server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.server-name {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.server-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
}

.status-dot.disconnected {
    background: #dc3545;
}

.server-info {
    color: #999999;
    font-size: 14px;
    margin-bottom: 16px;
}

.server-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tool-tag {
    background: rgba(32, 178, 170, 0.1);
    color: #20b2aa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    border: 1px solid rgba(32, 178, 170, 0.2);
}

/* Marketplace */
.marketplace-search {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
}

.marketplace-categories {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 8px 16px;
    background: #2a2a2a;
    color: #ffffff;
    border: 1px solid #404040;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.category-btn:hover, .category-btn.active {
    background: #20b2aa;
    border-color: #20b2aa;
}

.marketplace-items {
    display: grid;
    gap: 20px;
}

.marketplace-item {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.2s ease;
}

.marketplace-item:hover {
    border-color: #20b2aa;
    background: rgba(32, 178, 170, 0.05);
}

.item-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(32, 178, 170, 0.1);
    border-radius: 12px;
}

.item-info {
    flex: 1;
}

.item-info h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ffffff;
}

.item-info p {
    color: #999999;
    margin-bottom: 8px;
}

.item-tag {
    background: rgba(32, 178, 170, 0.1);
    color: #20b2aa;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* About Section */
.about-info {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
}

.about-item {
    padding: 8px 0;
    border-bottom: 1px solid #2a2a2a;
    font-size: 14px;
}

.about-item:last-child {
    border-bottom: none;
}

.about-links {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.link-btn {
    color: #20b2aa;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 0;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

.link-btn:hover {
    border-bottom-color: #20b2aa;
}

/* Loading States */
.loading {
    text-align: center;
    padding: 40px;
    color: #999999;
    font-style: italic;
}

/* Description Text */
.description {
    color: #999999;
    margin-bottom: 24px;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
    }
    
    .content-header {
        padding: 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .settings-section {
        padding: 20px;
    }
    
    .settings-group {
        padding: 20px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .setting-control {
        width: 100%;
        text-align: left;
    }
}

@media (max-width: 600px) {
    .sidebar {
        width: 60px;
    }
    
    .sidebar .title,
    .nav-item span {
        display: none;
    }
    
    .nav-item {
        justify-content: center;
        padding: 12px;
    }
    
    .main-content {
        margin-left: 60px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-section.active {
    animation: fadeIn 0.3s ease;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #20b2aa;
}
