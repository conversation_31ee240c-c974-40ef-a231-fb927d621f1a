gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var da,ha,la,pa,ta,va,Da,Ea;da=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ha=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
la=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.na=la(this);pa=function(a,b){if(b)a:{var c=_.na;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ha(c,a,{configurable:!0,writable:!0,value:b})}};
pa("Symbol",function(a){if(a)return a;var b=function(f,h){this.l2=f;ha(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.l2};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
pa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.na[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ha(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(da(this))}})}return a});ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.ua=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")va=Object.setPrototypeOf;else{var wa;a:{var xa={a:!0},ya={};try{ya.__proto__=xa;wa=ya.a;break a}catch(a){}wa=!1}va=wa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.za=va;
_.Aa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:da(a)};throw Error("b`"+String(a));};Da=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ea=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Da(d,e)&&(a[e]=d[e])}return a};pa("Object.assign",function(a){return a||Ea});
pa("globalThis",function(a){return a||_.na});pa("Reflect.setPrototypeOf",function(a){return a?a:_.za?function(b,c){try{return(0,_.za)(b,c),!0}catch(d){return!1}}:null});
pa("Promise",function(a){function b(){this.Af=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.zP=function(h){if(this.Af==null){this.Af=[];var k=this;this.AP(function(){k.V8()})}this.Af.push(h)};var d=_.na.setTimeout;b.prototype.AP=function(h){d(h,0)};b.prototype.V8=function(){for(;this.Af&&this.Af.length;){var h=this.Af;this.Af=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Yp(m)}}}this.Af=null};b.prototype.Yp=function(h){this.AP(function(){throw h;
})};var e=function(h){this.Ca=0;this.qf=void 0;this.Fr=[];this.aW=!1;var k=this.BF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.BF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.dfa),reject:h(this.qK)}};e.prototype.dfa=function(h){if(h===this)this.qK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.Iga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.cfa(h):this.XS(h)}};e.prototype.cfa=function(h){var k=void 0;try{k=h.then}catch(l){this.qK(l);return}typeof k=="function"?this.Jga(k,h):this.XS(h)};e.prototype.qK=function(h){this.Y_(2,h)};e.prototype.XS=function(h){this.Y_(1,h)};e.prototype.Y_=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.qf=k;this.Ca===2&&this.sfa();this.W8()};e.prototype.sfa=function(){var h=this;d(function(){if(h.pda()){var k=_.na.console;typeof k!=="undefined"&&k.error(h.qf)}},
1)};e.prototype.pda=function(){if(this.aW)return!1;var h=_.na.CustomEvent,k=_.na.Event,l=_.na.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.na.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.qf;return l(h)};e.prototype.W8=function(){if(this.Fr!=null){for(var h=0;h<this.Fr.length;++h)f.zP(this.Fr[h]);
this.Fr=null}};var f=new b;e.prototype.Iga=function(h){var k=this.BF();h.wy(k.resolve,k.reject)};e.prototype.Jga=function(h,k){var l=this.BF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,r){return typeof q=="function"?function(w){try{m(q(w))}catch(u){n(u)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.wy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.wy=function(h,k){function l(){switch(m.Ca){case 1:h(m.qf);
break;case 2:k(m.qf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Fr==null?f.zP(l):this.Fr.push(l);this.aW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.Aa(h),n=m.next();!n.done;n=m.next())c(n.value).wy(k,l)})};e.all=function(h){var k=_.Aa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(w){return function(u){q[w]=u;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(l.value).wy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ia=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
pa("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});pa("Object.setPrototypeOf",function(a){return a||_.za});pa("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
pa("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Da(l,f)){var m=new b;ha(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.Aa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Da(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Da(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
pa("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.Aa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.Aa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.Ve?m.Ve.value=l:(m.Ve={next:this[1],Lk:this[1].Lk,head:this[1],key:k,value:l},m.list.push(m.Ve),this[1].Lk.next=m.Ve,this[1].Lk=m.Ve,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.Ve&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.Ve.Lk.next=k.Ve.next,k.Ve.next.Lk=
k.Ve.Lk,k.Ve.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Lk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).Ve};c.prototype.get=function(k){return(k=d(this,k).Ve)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Da(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,Ve:p}}return{id:m,list:n,index:-1,Ve:void 0}},e=function(k,l){var m=k[1];return ta(function(){if(m){for(;m.head!=k[1];)m=m.Lk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Lk=k.next=k.head=k},h=0;return c});
pa("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.Aa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.Aa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ka=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};pa("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});
pa("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});pa("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ia(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
pa("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});pa("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push([d,b[d]]);return c}});
pa("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});pa("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ma=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{aV:e,SD:f}}return{aV:-1,SD:void 0}};pa("Array.prototype.find",function(a){return a?a:function(b,c){return Ma(this,b,c).SD}});pa("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});
pa("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});pa("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
pa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});pa("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});
pa("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});pa("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push(b[d]);return c}});
pa("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});pa("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});pa("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});pa("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
pa("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});pa("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});pa("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
pa("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});pa("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
pa("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var Na=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
pa("Array.prototype.at",function(a){return a?a:Na});var Pa=function(a){return a?a:Na};pa("Int8Array.prototype.at",Pa);pa("Uint8Array.prototype.at",Pa);pa("Uint8ClampedArray.prototype.at",Pa);pa("Int16Array.prototype.at",Pa);pa("Uint16Array.prototype.at",Pa);pa("Int32Array.prototype.at",Pa);pa("Uint32Array.prototype.at",Pa);pa("Float32Array.prototype.at",Pa);pa("Float64Array.prototype.at",Pa);pa("String.prototype.at",function(a){return a?a:Na});
pa("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ma(this,b,c).aV}});_.Ta={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Va=_.Va||{};_.Xa=this||self;_.$a=_.Xa._F_toggles||[];_.ab="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.t=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.eb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.mt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.gb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var ob;_.jb=function(a){return function(){return _.hb[a].apply(this,arguments)}};_.lb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.lb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.rZ=!0};ob=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.lb.call(this,c+a[d])};_.hb=[];_.eb(_.lb,Error);_.lb.prototype.name="CustomError";_.eb(ob,_.lb);ob.prototype.name="AssertionError";
var xb,yb,zb;_.pb=function(a,b){return _.hb[a]=b};_.rb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.vb=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.ua)(b.prototype);a.prototype.constructor=a;if(_.za)(0,_.za)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.wb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};xb=function(a){var b=_.wb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.z.apply(null,arguments)};_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Ab=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Bb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Gb=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Ib=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Jb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Nb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ob=!!(_.$a[0]&4096),Pb=!!(_.$a[0]&8192),Qb=!!(_.$a[0]&16),Rb=!!(_.$a[0]>>15&1);_.Sb=Ob?Pb:xb(610401301);_.Ub=Ob?Qb:xb(**********);_.Vb=Ob?Rb:xb(651175828);_.Wb=function(a){_.Wb[" "](a);return a};_.Wb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,pc,Ac,Lc,Zc,id;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.NY;throw Error("j");};_.hc=function(a){return new _.gc(a)};_.jc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.ic(a)};_.kc=function(a){if(a instanceof _.ic)return a.OY;throw Error("j");};_.mc=function(a){return a instanceof _.lc};_.nc=function(a){if(_.mc(a))return a.QY;throw Error("j");};pc=function(a){return new _.oc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.rc=function(a){if(qc.test(a))return a};
_.sc=function(a){return a instanceof _.lc?_.nc(a):_.rc(a)};_.tc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.msa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.rea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.nsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.vc=function(a){var b=_.uc.apply(1,arguments);if(b.length===0)return _.jc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.jc(c)};_.wc=function(a,b){return a.lastIndexOf(b,0)==0};_.xc=function(a){return/^[\s\xa0]*$/.test(a)};_.yc=function(a,b){return a.indexOf(b)!=-1};
_.Dc=function(a,b){var c=0;a=(0,_.zc)(String(a)).split(".");b=(0,_.zc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Ac(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Ac(f[2].length==0,h[2].length==0)||Ac(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Ac=function(a,b){return a<b?-1:a>b?1:0};_.Ec=function(a,b){b=_.sc(b);b!==void 0&&(a.href=b)};_.Fc=function(a,b,c,d){b=_.sc(b);return b!==void 0?a.open(b,c,d):null};_.Gc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Hc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Jc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Lc=function(a){if(!_.Sb||!_.Kc)return!1;for(var b=0;b<_.Kc.brands.length;b++){var c=_.Kc.brands[b].brand;if(c&&_.yc(c,a))return!0}return!1};_.Mc=function(a){return _.yc(_.Jc(),a)};_.Nc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Oc=function(){return _.Sb?!!_.Kc&&_.Kc.brands.length>0:!1};_.Pc=function(){return _.Oc()?!1:_.Mc("Opera")};
_.Qc=function(){return _.Oc()?!1:_.Mc("Trident")||_.Mc("MSIE")};_.Sc=function(){return _.Oc()?!1:_.Mc("Edge")};_.Tc=function(){return _.Oc()?Lc("Microsoft Edge"):_.Mc("Edg/")};_.Uc=function(){return _.Oc()?Lc("Opera"):_.Mc("OPR")};_.Vc=function(){return _.Mc("Firefox")||_.Mc("FxiOS")};_.Wc=function(){return _.Oc()?Lc("Chromium"):(_.Mc("Chrome")||_.Mc("CriOS"))&&!_.Sc()||_.Mc("Silk")};
_.Xc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.Yc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
Zc=function(){return _.Sb?!!_.Kc&&!!_.Kc.platform:!1};_.$c=function(){return Zc()?_.Kc.platform==="Android":_.Mc("Android")};_.ad=function(){return _.Mc("iPhone")&&!_.Mc("iPod")&&!_.Mc("iPad")};_.bd=function(){return _.ad()||_.Mc("iPad")||_.Mc("iPod")};_.cd=function(){return Zc()?_.Kc.platform==="macOS":_.Mc("Macintosh")};_.dd=function(){return Zc()?_.Kc.platform==="Windows":_.Mc("Windows")};_.ed=function(){return Zc()?_.Kc.platform==="Chrome OS":_.Mc("CrOS")};
_.fd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.gd=function(a){return _.fd(a,a)};_.uc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.jd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.kd=function(a){var b=_.jd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.ld=function(){return Date.now()};var md=globalThis.trustedTypes,$b=md,bc;_.dc=function(a){this.NY=a};_.dc.prototype.toString=function(){return this.NY+""};_.nd=function(){return new _.dc(md?md.emptyHTML:"")}();_.gc=function(a){this.PY=a};_.gc.prototype.toString=function(){return this.PY};_.ic=function(a){this.OY=a};_.ic.prototype.toString=function(){return this.OY+""};_.lc=function(a){this.QY=a};_.lc.prototype.toString=function(){return this.QY};_.od=new _.lc("about:invalid#zClosurez");var qc;_.oc=function(a){this.xj=a};_.pd=[pc("data"),pc("http"),pc("https"),pc("mailto"),pc("ftp"),new _.oc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.qd=function(){return typeof URL==="function"}();qc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.rd=function(a,b){this.width=a;this.height=b};_.sd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.rd.prototype;_.g.clone=function(){return new _.rd(this.width,this.height)};_.g.by=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.by()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.zc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.td=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.ud=Math.random()*2147483648|0;var vd;vd=_.Xa.navigator;_.Kc=vd?vd.userAgentData||null:null;var Nd,Od,Wd;_.xd=_.Pc();_.yd=_.Qc();_.zd=_.Mc("Edge");_.Ad=_.zd||_.yd;_.Bd=_.Mc("Gecko")&&!(_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge"))&&!(_.Mc("Trident")||_.Mc("MSIE"))&&!_.Mc("Edge");_.Cd=_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge");_.Dd=_.Cd&&_.Mc("Mobile");_.Ed=_.cd();_.Fd=_.dd();_.Gd=(Zc()?_.Kc.platform==="Linux":_.Mc("Linux"))||_.ed();_.Id=_.$c();_.Jd=_.ad();_.Kd=_.Mc("iPad");_.Ld=_.Mc("iPod");_.Md=_.bd();Nd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Pd="",Td=function(){var a=_.Jc();if(_.Bd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.zd)return/Edge\/([\d\.]+)/.exec(a);if(_.yd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/WebKit\/(\S+)/.exec(a);if(_.xd)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Td&&(Pd=Td?Td[1]:"");if(_.yd){var Ud=Nd();if(Ud!=null&&Ud>parseFloat(Pd)){Od=String(Ud);break a}}Od=Pd}_.Vd=Od;if(_.Xa.document&&_.yd){var Xd=Nd();Wd=Xd?Xd:parseInt(_.Vd,10)||void 0}else Wd=void 0;_.Yd=Wd;var de,ke,je;_.ae=function(a){return a?new _.Zd(_.$d(a)):id||(id=new _.Zd)};_.be=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.ce=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.ee=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:de.hasOwnProperty(d)?a.setAttribute(de[d],c):_.wc(d,"aria-")||_.wc(d,"data-")?a.setAttribute(d,c):a[d]=c})};de={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.ge=function(a){return _.fe(a||window)};
_.fe=function(a){a=a.document;a=_.he(a)?a.documentElement:a.body;return new _.rd(a.clientWidth,a.clientHeight)};_.ie=function(a){return a?a.defaultView:window};_.le=function(a,b){var c=b[1],d=je(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.ee(d,c));b.length>2&&ke(a,d,b,2);return d};
ke=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.kd(f)||_.vb(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.vb(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Bb(h?_.Yb(f):f,e)}}};_.me=function(a){return je(document,a)};
je=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.he=function(a){return a.compatMode=="CSS1Compat"};_.ne=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.oe=function(a,b){ke(_.$d(a),a,arguments,1)};_.pe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.qe=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.re=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.se=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.te=function(a){return _.vb(a)&&a.nodeType==1};
_.ue=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.$d=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.ve=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.pe(a),a.appendChild(_.$d(a).createTextNode(String(b)))};_.Zd=function(a){this.Bc=a||_.Xa.document||document};_.g=_.Zd.prototype;_.g.Ha=_.ae;_.g.uL=_.jb(0);_.g.ub=function(){return this.Bc};_.g.O=_.jb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Bc).getElementsByTagName(String(a))};
_.g.uH=_.jb(2);_.g.wa=function(a,b,c){return _.le(this.Bc,arguments)};_.g.createElement=function(a){return je(this.Bc,a)};_.g.createTextNode=function(a){return this.Bc.createTextNode(String(a))};_.g.getWindow=function(){return this.Bc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.oe;_.g.canHaveChildren=_.ne;_.g.ne=_.pe;_.g.xV=_.qe;_.g.removeNode=_.re;_.g.EG=_.se;_.g.isElement=_.te;_.g.contains=_.ue;_.g.XG=_.$d;_.g.vj=_.jb(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.we=function(a){return a===null?"null":a===void 0?"undefined":a};_.xe=window;_.ye=document;_.ze=_.xe.location;_.Ae=/\[native code\]/;_.Be=function(a,b,c){return a[b]=a[b]||c};_.Ce=function(){var a;if((a=Object.create)&&_.Ae.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.De=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Ee=function(a,b){a=a||{};for(var c in a)_.De(a,c)&&(b[c]=a[c])};_.Fe=_.Be(_.xe,"gapi",{});_.Ge=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.He=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ie=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Je=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Le=function(a,b,c){_.Ke(a,b,c,"add","at")};_.Ke=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Me={};_.Me=_.Be(_.xe,"___jsl",_.Ce());_.Be(_.Me,"I",0);_.Be(_.Me,"hel",10);var Ne,Pe,Qe,Re,Ue,Se,Te,Ve,We;Ne=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Pe=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Qe=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Re=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Qe(a[d])&&!Qe(b[d])?Re(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Qe(b[d])?[]:{},Re(a[d],b[d])):a[d]=b[d])};
Ue=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Ne("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Se())if(e=Te(c),d.push(25),typeof e===
"object")return e;return{}}};Se=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Te=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Ve=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Re(c,b);a.push(c)};
We=function(a){Pe(!0);var b=window.___gcfg,c=Ne("cu"),d=window.___gu;b&&b!==d&&(Ve(c,b),window.___gu=b);b=Ne("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Ne("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Ue(f,h))&&b.push(f));a&&Ve(c,a);d=Ne("cd");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);d=Ne("ci");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);a=0;for(b=c.length;a<b;++a)Re(Pe(),c[a],!0)};_.Xe=function(a,b){var c=Pe();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Ye=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;We(c)};var Ze=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Be(_.Me,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Ze&&Ze();We();_.t("gapi.config.get",_.Xe);_.t("gapi.config.update",_.Ye);
_.$e=function(a){a=_.we(a);return _.ec(a)};
_.af=_.af||{};
_.af=_.af||{};_.af.Hv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.af.lB=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.bf=function(e){a(2,e)};_.cf=function(e){a(3,e)};_.df=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.af=_.af||{};_.af.i7=function(a){var b=window;typeof b.addEventListener!="undefined"?b.addEventListener("mousemove",a,!1):typeof b.attachEvent!="undefined"?b.attachEvent("onmousemove",a):_.bf("cannot attachBrowserEvent: mousemove")};_.af.Jea=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.bf("cannot removeBrowserEvent: mousemove")};
_.af=_.af||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.af.escape=function(c,d){if(c){if(typeof c==="string")return _.af.aG(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.af.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.af.aG(e)]=_.af.escape(c[e],!0));return d}}return c};_.af.aG=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.af.ota=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.af=_.af||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.af.Rg=a;a()})();_.t("gadgets.util.getUrlParameters",_.af.Rg);
var ff,gf,hf,jf,lf,nf,of,pf,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Df,Gf,Hf,If,Jf,Kf,Lf,Mf,Nf,Of,Pf,Sf,Tf;hf=void 0;jf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};lf=function(a){return Object.prototype.toString.call(a)};nf=lf(0);of=lf(new Date(0));pf=lf(!0);qf=lf("");rf=lf({});sf=lf([]);
tf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=lf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==sf||a.constructor!==Array&&a.constructor!==Object)&&(e!==rf||a.constructor!==Array&&a.constructor!==Object)&&e!==qf&&e!==nf&&e!==pf&&e!==of))return tf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===nf)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===pf)b[b.length]=String(!!Number(a));else{if(e===of)return tf(a.toISOString.call(a),c);if(e===sf&&lf(a.length)===nf){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=tf(a[f],c)||"null";b[b.length]="]"}else if(e==qf&&lf(a.length)===nf){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=tf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=tf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};uf=/[\0-\x07\x0b\x0e-\x1f]/;
vf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;wf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;yf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;zf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Af=/[ \t\n\r]+/g;Bf=/[^"]:/;Df=/""/g;Gf=/true|false|null/g;Hf=/00/;If=/[\{]([^0\}]|0[^:])/;Jf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Kf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(uf.test(a)||vf.test(a)||wf.test(a)||xf.test(a))return!1;var b=a.replace(yf,'""');b=b.replace(zf,"0");b=b.replace(Af,"");if(Bf.test(b))return!1;b=b.replace(Df,"0");b=b.replace(Gf,"0");if(Hf.test(b)||If.test(b)||Jf.test(b)||Kf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=hf?[jf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((ff===void 0||hf===void 0||gf!==a)&&gf!==-1){ff=hf=!1;gf=-1;try{try{hf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&jf("true")===!0&&jf('[{"a":3}]')[0].a===3}catch(b){}ff=hf&&!jf("[00]")&&!jf('"\u0007"')&&!jf('"\\0"')&&!jf('"\\v"')}finally{gf=a}}};_.Qf=function(a){if(gf===-1)return!1;Pf();return(ff?jf:Of)(a)};
_.Rf=function(a){if(gf!==-1)return Pf(),hf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):tf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
var Mg=function(){this.blockSize=-1},Ng=function(){this.blockSize=-1;this.blockSize=64;this.Rc=[];this.YE=[];this.E6=[];this.OB=[];this.OB[0]=128;for(var a=1;a<this.blockSize;++a)this.OB[a]=0;this.ED=this.fr=0;this.reset()};_.eb(Ng,Mg);Ng.prototype.reset=function(){this.Rc[0]=1732584193;this.Rc[1]=4023233417;this.Rc[2]=2562383102;this.Rc[3]=271733878;this.Rc[4]=3285377520;this.ED=this.fr=0};
var Og=function(a,b,c){c||(c=0);var d=a.E6;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.Rc[0];c=a.Rc[1];e=a.Rc[2];for(var f=a.Rc[3],h=a.Rc[4],k,l,m=0;m<80;m++)m<40?m<20?(k=f^c&(e^f),l=1518500249):(k=c^e^f,l=1859775393):m<60?(k=c&e|f&(c|e),l=2400959708):(k=c^
e^f,l=3395469782),k=(b<<5|b>>>27)+k+h+l+d[m]&4294967295,h=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=k;a.Rc[0]=a.Rc[0]+b&4294967295;a.Rc[1]=a.Rc[1]+c&4294967295;a.Rc[2]=a.Rc[2]+e&4294967295;a.Rc[3]=a.Rc[3]+f&4294967295;a.Rc[4]=a.Rc[4]+h&4294967295};
Ng.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.YE,f=this.fr;d<b;){if(f==0)for(;d<=c;)Og(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Og(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Og(this,e);f=0;break}}this.fr=f;this.ED+=b}};
Ng.prototype.digest=function(){var a=[],b=this.ED*8;this.fr<56?this.update(this.OB,56-this.fr):this.update(this.OB,this.blockSize-(this.fr-56));for(var c=this.blockSize-1;c>=56;c--)this.YE[c]=b&255,b/=256;Og(this,this.YE);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Rc[c]>>d&255,++b;return a};_.Pg=function(){this.bN=new Ng};_.g=_.Pg.prototype;_.g.reset=function(){this.bN.reset()};_.g.C1=function(a){this.bN.update(a)};_.g.ZQ=function(){return this.bN.digest()};_.g.ux=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.C1(b)};_.g.Si=function(){for(var a=this.ZQ(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
_.di=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.ei=function(a){var b=_.di();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.fi=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.gi=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};var hi;hi=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.ii=function(a){var b=_.ei("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Ge(a,"authuser")||null,b==null&&(b=(b=a.match(hi))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
_.vi=function(){if(!_.Xa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Xa.addEventListener("test",c,b);_.Xa.removeEventListener("test",c,b)}catch(d){}return a}();
var wi=function(){var a=_.Me.ms||_.Me.u;if(a)return(new URL(a)).origin};var Di=function(a){this.KS=a;this.count=this.count=0};Di.prototype.rb=function(a,b){a?this.count+=a:this.count++;this.KS&&(b===void 0||b)&&this.KS()};Di.prototype.get=function(){return this.count};Di.prototype.reset=function(){this.count=0};var Fi,Ii;Fi=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Ny=new Map;this.RE=!1;var c=wi();c&&(this.url=c+"/js/gen_204",c=_.ei("gen204logger")||{},this.ju=c.interval,this.LS=c.rate,this.RE=c.cqa,a&&this.url&&Ei(this),document.addEventListener("visibilitychange",this.flush),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))};_.Gi=function(){Fi.NW||(Fi.NW=new Fi);return Fi.NW};
Ii=function(a){var b=_.Me.dm||[];if(b&&b.length!==0){b=_.Aa(b);for(var c=b.next();!c.done;c=b.next())_.Hi(a,c.value).rb(1,!1);delete _.Me.dm;a.flush()}};_.Hi=function(a,b){a.Ny.has(b)||a.Ny.set(b,new Di(a.RE?void 0:function(){a.flush()}));return a.Ny.get(b)};
Fi.prototype.flush=function(){var a=this;if(this.url&&this.LS){Ii(this);for(var b="",c=_.Aa(this.Ny),d=c.next();!d.done;d=c.next()){var e=_.Aa(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}if(b!==""&&Math.random()<this.LS){try{var h=AbortSignal.timeout(3E4)}catch(k){h=void 0}fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:h}).catch(function(){}).finally(function(){Ei(a)})}}};
Fi.prototype.setInterval=function(a){this.ju=a};var Ei=function(a){a.ju&&a.RE&&setTimeout(function(){a.flush()},a.ju)};var Ki,Ji,Qi,Ri,Li,Oi,Mi,Si,Ni;_.Pi=function(){_.Hi(_.Gi(),50).rb();if(Ji){var a=new _.xe.Uint32Array(1);Ki.getRandomValues(a);a=Number("0."+a[0])}else a=Li,a+=parseInt(Mi.substr(0,20),16),Mi=Ni(Mi),a/=Oi+1.2089258196146292E24;return a};Ki=_.xe.crypto;Ji=!1;Qi=0;Ri=0;Li=1;Oi=0;Mi="";Si=function(a){a=a||_.xe.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Li=Li*b%Oi;Qi>0&&++Ri==Qi&&_.Ke(_.xe,"mousemove",Si,"remove","de")};
Ni=function(a){var b=new _.Pg;b.ux(a);return b.Si()};Ji=!!Ki&&typeof Ki.getRandomValues=="function";Ji||(Oi=(screen.width*screen.width+screen.height)*1E6,Mi=Ni(_.ye.cookie+"|"+_.ye.location+"|"+(new Date).getTime()+"|"+Math.random()),Qi=_.ei("random/maxObserveMousemove")||0,Qi!=0&&_.Le(_.xe,"mousemove",Si));
var Ll,Ml,Nl,Ol,Pl,Ql,Rl,Sl,Tl,Ul,Xl,Yl,bm,cm,dm,em,fm,gm,hm,im;_.Kl=function(a,b){if(!a)throw Error(b||"");};Ll=/&/g;Ml=/</g;Nl=/>/g;Ol=/"/g;Pl=/'/g;Ql=function(a){return String(a).replace(Ll,"&amp;").replace(Ml,"&lt;").replace(Nl,"&gt;").replace(Ol,"&quot;").replace(Pl,"&#39;")};Rl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Sl=/%([a-f]|[0-9a-fA-F][a-f])/g;Tl=/^(https?|ftp|file|chrome-extension):$/i;
Ul=function(a){a=String(a);a=a.replace(Rl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Ie,function(e){return e.replace(/%/g,"%25")}).replace(Sl,function(e){return e.toUpperCase()});a=a.match(_.He)||[];var b=_.Ce(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Tl);b.mt=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Xi=a[7]?[d(a[7])]:[];return b};Xl=function(a){return a.mt+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Xi.length>0?"#"+a.Xi.join("&"):"")};Yl=function(a,b){var c=[];if(a)for(var d in a)if(_.De(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Zl=function(a,b,c,d){a=Ul(a);a.query.push.apply(a.query,Yl(b,d));a.Xi.push.apply(a.Xi,Yl(c,d));return Xl(a)};
_.$l=function(a,b){var c=Ul(b);b=c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Je,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Ul(b);b=c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));_.Ec(a,new _.lc(_.we(b)));e.appendChild(a);_.Hc(e,_.ec(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Ul(b+d);b=
c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));return b};_.am=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;cm=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};dm=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
em=function(){var a=_.ei("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(dm))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};fm=function(){var a=_.Me.onl;if(!a){a=_.Ce();_.Me.onl=a;var b=_.Ce();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};gm=function(a,b){b=b.onload;return typeof b==="function"?(fm().a(a,b),b):null};
hm=function(a){_.Kl(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};im=function(a){fm().r(a)};var km,lm,pm;_.jm={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};km={allowtransparency:!0,onload:!0};lm=0;_.mm=function(a,b){var c=0;do var d=b.id||["I",lm++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.Kl(c<5,"Error creating iframe id");return d};_.nm=function(a,b){return a?b+"/"+a:""};
_.om=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.Ee(d.queryParams||{},e);_.Ee(d.fragmentParams||{},f);var h=d.pfname;var k=_.Ce();_.ei("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Ge(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Ge(a.location.href,"_gfid","")||_.Ge(a.location.href,"id",""),h=_.nm(h,_.Ge(a.location.href,"pfname","")));h||(c=_.Qf(_.Ge(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.nm(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Rf(k),k=h);h=_.Ge(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Pi()*1E8)),k.rpctoken=h);d.rpctoken=h;_.Ee(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.Ce();(h=_.Ge(k,"_bsh",_.Me.bsh))&&(a._bsh=h);(k=_.Me.dpo?_.Me.h:_.Ge(k,"jsh",_.Me.h))&&(a.jsh=k);d.hintInFragment?_.Ee(a,f):_.Ee(a,e);return _.Zl(b,e,f,d.paramsSerializer)};
pm=function(a){_.Kl(!a||_.am.test(a),"Illegal url for new iframe - "+a)};
_.qm=function(a,b,c,d,e){pm(c.src);var f,h=gm(d,c),k=h?hm(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+Ql(String(c.frameborder))+'" scrolling="'+Ql(String(c.scrolling))+'" '+k+' name="'+Ql(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.ae(a).createElement("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},im(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.Ee(a,f.style):km[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||cm(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var rm,um;rm=/^:[\w]+$/;_.sm=/:([a-zA-Z_]+):/g;_.tm=function(){var a=_.ii()||"0",b=em();var c=_.ii()||a;var d=em(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.ei("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.ei("iframes/:socialhost:"),h=_.ei("iframes/:im_socialhost:");return bm={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};um=function(a,b){return _.tm()[b]||""};
_.vm=function(a){return _.$l(_.ye,a.replace(_.sm,um))};_.wm=function(a){var b=a;rm.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.ei(b),_.Kl(!!b,"Unknown iframe url config for - "+a));return _.vm(b)};
_.xm=function(a,b,c){c=c||{};var d=c.attributes||{};_.Kl(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.wm(a);d=b.ownerDocument||_.ye;var e=_.mm(d,c);a=_.om(d,a,e,c);var f=c,h=_.Ce();_.Ee(_.jm,h);_.Ee(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Ul(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.qm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.qm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Xl(c);_.Kl(_.am.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.sc(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.qm(d,b,h,e,f);return b};
_.ef=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Ye(a());return{register:function(b,c,d){d&&d(_.Xe())},get:function(b){return _.Xe(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Ye(b)},init:function(){}}}();_.t("gadgets.config.register",_.ef.register);_.t("gadgets.config.get",_.ef.get);_.t("gadgets.config.init",_.ef.init);_.t("gadgets.config.update",_.ef.update);
_.t("gadgets.json.stringify",_.Rf);_.t("gadgets.json.parse",_.Qf);
_.af=_.af||{};(function(){var a=[];_.af.rsa=function(b){a.push(b)};_.af.Fsa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
var Uf=function(){this.Eg=window.console};Uf.prototype.log=function(a){this.Eg&&this.Eg.log&&this.Eg.log(a)};Uf.prototype.error=function(a){this.Eg&&(this.Eg.error?this.Eg.error(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.warn=function(a){this.Eg&&(this.Eg.warn?this.Eg.warn(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Wf=function(){var a=_.ye.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Xf=function(a){if(_.Wf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.xe.addEventListener?(_.xe.addEventListener("load",c,!1),_.xe.addEventListener("DOMContentLoaded",c,!1)):_.xe.attachEvent&&(_.xe.attachEvent("onreadystatechange",function(){_.Wf()&&c.apply(this,arguments)}),_.xe.attachEvent("onload",c))}};
_.Yf=function(a,b){var c=_.Be(_.Me,"watt",_.Ce());_.Be(c,a,b)};_.Ge(_.xe.location.href,"rpctoken")&&_.Le(_.ye,"unload",function(){});var Zf=Zf||{};Zf.HZ=null;Zf.rX=null;Zf.FA=null;Zf.frameElement=null;Zf=Zf||{};
Zf.TN||(Zf.TN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Qf(f.data);if(h&&h.f){_.df();var k=_.$f.co(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.cf("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{nT:function(){return"wpm"},nca:function(){return!0},init:function(f,h){_.ef.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Ib:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.$f.co(f),m=_.$f.MO(f);l?window.setTimeout(function(){var n=_.Rf(k);_.df();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.cf("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.$f!="undefined"&&_.$f||(_.$f=window.gadgets.rpc,_.$f.config=_.$f.config,_.$f.register=_.$f.register,_.$f.unregister=_.$f.unregister,_.$f.jZ=_.$f.registerDefault,_.$f.A1=_.$f.unregisterDefault,_.$f.SS=_.$f.forceParentVerifiable,_.$f.call=_.$f.call,_.$f.Gu=_.$f.getRelayUrl,_.$f.Oj=_.$f.setRelayUrl,_.$f.OC=_.$f.setAuthToken,_.$f.Iw=_.$f.setupReceiver,_.$f.Pn=_.$f.getAuthToken,_.$f.vK=_.$f.removeReceiver,_.$f.NT=_.$f.getRelayChannel,_.$f.eZ=_.$f.receive,
_.$f.fZ=_.$f.receiveSameDomain,_.$f.getOrigin=_.$f.getOrigin,_.$f.co=_.$f.getTargetOrigin,_.$f.MO=_.$f._getTargetWin,_.$f.N6=_.$f._parseSiblingId);else{_.$f=function(){function a(I,ka){if(!T[I]){var ma=cb;ka||(ma=Oa);T[I]=ma;ka=K[I]||[];for(var Fa=0;Fa<ka.length;++Fa){var U=ka[Fa];U.t=E[I];ma.call(I,U.f,U)}K[I]=[]}}function b(){function I(){Mb=!0}Hb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",I,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
I),Hb=!0)}function c(I,ka,ma,Fa,U){E[ka]&&E[ka]===ma||(_.cf("Invalid gadgets.rpc token. "+E[ka]+" vs "+ma),qb(ka,2));U.onunload=function(){R[ka]&&!Mb&&(qb(ka,1),_.$f.vK(ka))};b();Fa=_.Qf(decodeURIComponent(Fa))}function d(I,ka){if(I&&typeof I.s==="string"&&typeof I.f==="string"&&I.a instanceof Array)if(E[I.f]&&E[I.f]!==I.t&&(_.cf("Invalid gadgets.rpc token. "+E[I.f]+" vs "+I.t),qb(I.f,2)),I.s==="__ack")window.setTimeout(function(){a(I.f,!0)},0);else{I.c&&(I.callback=function(Ga){_.$f.call(I.f,(I.g?
"legacy__":"")+"__cb",null,I.c,Ga)});if(ka){var ma=e(ka);I.origin=ka;var Fa=I.r;try{var U=e(Fa)}catch(Ga){}Fa&&U==ma||(Fa=ka);I.referer=Fa}ka=(x[I.s]||x[""]).apply(I,I.a);I.c&&typeof ka!=="undefined"&&_.$f.call(I.f,"__cb",null,I.c,ka)}}function e(I){if(!I)return"";I=I.split("#")[0].split("?")[0];I=I.toLowerCase();I.indexOf("//")==0&&(I=window.location.protocol+I);I.indexOf("://")==-1&&(I=window.location.protocol+"//"+I);var ka=I.substring(I.indexOf("://")+3),ma=ka.indexOf("/");ma!=-1&&(ka=ka.substring(0,
ma));I=I.substring(0,I.indexOf("://"));if(I!=="http"&&I!=="https"&&I!=="chrome-extension"&&I!=="file"&&I!=="android-app"&&I!=="chrome-search"&&I!=="chrome-untrusted"&&I!=="chrome"&&I!=="devtools")throw Error("l");ma="";var Fa=ka.indexOf(":");if(Fa!=-1){var U=ka.substring(Fa+1);ka=ka.substring(0,Fa);if(I==="http"&&U!=="80"||I==="https"&&U!=="443")ma=":"+U}return I+"://"+ka+ma}function f(I){if(I.charAt(0)=="/"){var ka=I.indexOf("|"),ma=ka>0?I.substring(1,ka):I.substring(1);I=ka>0?I.substring(ka+1):
null;return{id:ma,origin:I}}return null}function h(I){if(typeof I==="undefined"||I==="..")return window.parent;var ka=f(I);if(ka)return k(window.top.frames[ka.id]);I=String(I);return(ka=window.frames[I])?k(ka):(ka=document.getElementById(I))&&ka.contentWindow?ka.contentWindow:null}function k(I){return I?"postMessage"in I?I:I instanceof HTMLIFrameElement&&"contentWindow"in I&&I.contentWindow!=null&&"postMessage"in I.contentWindow?I.contentWindow:null:null}function l(I,ka){if(R[I]!==!0){typeof R[I]===
"undefined"&&(R[I]=0);var ma=h(I);I!==".."&&ma==null||cb.Ib(I,ka)!==!0?R[I]!==!0&&R[I]++<10?window.setTimeout(function(){l(I,ka)},500):(T[I]=Oa,R[I]=!0):R[I]=!0}}function m(I){(I=A[I])&&I.substring(0,1)==="/"&&(I=I.substring(1,2)==="/"?document.location.protocol+I:document.location.protocol+"//"+document.location.host+I);return I}function n(I,ka,ma){ka&&!/http(s)?:\/\/.+/.test(ka)&&(ka.indexOf("//")==0?ka=window.location.protocol+ka:ka.charAt(0)=="/"?ka=window.location.protocol+"//"+window.location.host+
ka:ka.indexOf("://")==-1&&(ka=window.location.protocol+"//"+ka));A[I]=ka;typeof ma!=="undefined"&&(D[I]=!!ma)}function p(I,ka){ka=ka||"";E[I]=String(ka);l(I,ka)}function q(I){I=(I.passReferrer||"").split(":",2);O=I[0]||"none";Y=I[1]||"origin"}function r(I){String(I.useLegacyProtocol)==="true"&&(cb=Zf.FA||Oa,cb.init(d,a))}function w(I,ka){function ma(Fa){Fa=Fa&&Fa.rpc||{};q(Fa);var U=Fa.parentRelayUrl||"";U=e(aa.parent||ka)+U;n("..",U,String(Fa.useLegacyProtocol)==="true");r(Fa);p("..",I)}!aa.parent&&
ka?ma({}):_.ef.register("rpc",null,ma)}function u(I,ka,ma){if(I==="..")w(ma||aa.rpctoken||aa.ifpctok||"",ka);else a:{var Fa=null;if(I.charAt(0)!="/"){if(!_.af)break a;Fa=document.getElementById(I);if(!Fa)throw Error("m`"+I);}Fa=Fa&&Fa.src;ka=ka||e(Fa);n(I,ka);ka=_.af.Rg(Fa);p(I,ma||ka.rpctoken)}}var x={},A={},D={},E={},N=0,H={},R={},aa={},T={},K={},O=null,Y=null,oa=window.top!==window.self,La=window.name,qb=function(){},fb=window.console,Cb=fb&&fb.log&&function(I){fb.log(I)}||function(){},Oa=function(){function I(ka){return function(){Cb(ka+
": call ignored")}}return{nT:function(){return"noop"},nca:function(){return!0},init:I("init"),Ib:I("setup"),call:I("call")}}();_.af&&(aa=_.af.Rg());var Mb=!1,Hb=!1,cb=function(){if(aa.rpctx=="rmr")return Zf.HZ;var I=typeof window.postMessage==="function"?Zf.TN:typeof window.postMessage==="object"?Zf.TN:window.ActiveXObject?Zf.rX?Zf.rX:Zf.FA:navigator.userAgent.indexOf("WebKit")>0?Zf.HZ:navigator.product==="Gecko"?Zf.frameElement:Zf.FA;I||(I=Oa);return I}();x[""]=function(){Cb("Unknown RPC service: "+
this.s)};x.__cb=function(I,ka){var ma=H[I];ma&&(delete H[I],ma.call(this,ka))};return{config:function(I){typeof I.VZ==="function"&&(qb=I.VZ)},register:function(I,ka){if(I==="__cb"||I==="__ack")throw Error("n");if(I==="")throw Error("o");x[I]=ka},unregister:function(I){if(I==="__cb"||I==="__ack")throw Error("p");if(I==="")throw Error("q");delete x[I]},jZ:function(I){x[""]=I},A1:function(){delete x[""]},SS:function(){},call:function(I,ka,ma,Fa){I=I||"..";var U="..";I===".."?U=La:I.charAt(0)=="/"&&(U=
e(window.location.href),U="/"+La+(U?"|"+U:""));++N;ma&&(H[N]=ma);var Ga={s:ka,f:U,c:ma?N:0,a:Array.prototype.slice.call(arguments,3),t:E[I],l:!!D[I]};a:if(O==="bidir"||O==="c2p"&&I===".."||O==="p2c"&&I!==".."){var Ha=window.location.href;var fa="?";if(Y==="query")fa="#";else if(Y==="hash")break a;fa=Ha.lastIndexOf(fa);fa=fa===-1?Ha.length:fa;Ha=Ha.substring(0,fa)}else Ha=null;Ha&&(Ga.r=Ha);if(I===".."||f(I)!=null||document.getElementById(I))(Ha=T[I])||f(I)===null||(Ha=cb),ka.indexOf("legacy__")===
0&&(Ha=cb,Ga.s=ka.substring(8),Ga.c=Ga.c?Ga.c:N),Ga.g=!0,Ga.r=U,Ha?(D[I]&&(Ha=Zf.FA),Ha.call(I,U,Ga)===!1&&(T[I]=Oa,cb.call(I,U,Ga))):K[I]?K[I].push(Ga):K[I]=[Ga]},Gu:m,Oj:n,OC:p,Iw:u,Pn:function(I){return E[I]},vK:function(I){delete A[I];delete D[I];delete E[I];delete R[I];delete T[I]},NT:function(){return cb.nT()},eZ:function(I,ka){I.length>4?cb.Qpa(I,d):c.apply(null,I.concat(ka))},fZ:function(I){I.a=Array.prototype.slice.call(I.a);window.setTimeout(function(){d(I)},0)},getOrigin:e,co:function(I){var ka=
null,ma=m(I);ma?ka=ma:(ma=f(I))?ka=ma.origin:I==".."?ka=aa.parent:(I=document.getElementById(I))&&I.tagName.toLowerCase()==="iframe"&&(ka=I.src);return e(ka)},init:function(){cb.init(d,a)===!1&&(cb=Oa);oa?u(".."):_.ef.register("rpc",null,function(I){I=I.rpc||{};q(I);r(I)})},MO:h,N6:f,Wha:"__ack",Wma:La||"..",gna:0,fna:1,ena:2}}();_.$f.init()};_.$f.config({VZ:function(a){throw Error("r`"+a);}});_.t("gadgets.rpc.config",_.$f.config);_.t("gadgets.rpc.register",_.$f.register);_.t("gadgets.rpc.unregister",_.$f.unregister);_.t("gadgets.rpc.registerDefault",_.$f.jZ);_.t("gadgets.rpc.unregisterDefault",_.$f.A1);_.t("gadgets.rpc.forceParentVerifiable",_.$f.SS);_.t("gadgets.rpc.call",_.$f.call);_.t("gadgets.rpc.getRelayUrl",_.$f.Gu);_.t("gadgets.rpc.setRelayUrl",_.$f.Oj);_.t("gadgets.rpc.setAuthToken",_.$f.OC);_.t("gadgets.rpc.setupReceiver",_.$f.Iw);_.t("gadgets.rpc.getAuthToken",_.$f.Pn);
_.t("gadgets.rpc.removeReceiver",_.$f.vK);_.t("gadgets.rpc.getRelayChannel",_.$f.NT);_.t("gadgets.rpc.receive",_.$f.eZ);_.t("gadgets.rpc.receiveSameDomain",_.$f.fZ);_.t("gadgets.rpc.getOrigin",_.$f.getOrigin);_.t("gadgets.rpc.getTargetOrigin",_.$f.co);
_.Qg=function(){function a(m){var n=new _.Pg;n.ux(m);return n.Si()}var b=window.crypto;if(b&&typeof b.getRandomValues=="function")return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Xe("random/maxObserveMousemove");c==null&&(c=-1);var d=0,e=Math.random(),f=1,h=(screen.width*screen.width+screen.height)*1E6,k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;c>0&&++d==c&&_.af.Jea(k)};
c!=0&&_.af.i7(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+1.2089258196146292E24)}}();_.t("shindig.random",_.Qg);
var ym;
ym=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.zm=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return ym();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
_.Eo={};window.iframer=_.Eo;
_.Ta.Ma={};_.Ta.Ma.Mi={};_.Ta.Ma.Mi.z7=function(a){try{return!!a.document}catch(b){}return!1};_.Ta.Ma.Mi.ZT=function(a){var b=a.parent;return a!=b&&_.Ta.Ma.Mi.z7(b)?_.Ta.Ma.Mi.ZT(b):a};_.Ta.Ma.Mi.sra=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Ta.Ma.Mi.Hv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var Jq,Kq,Lq,Mq,Pq,Qq,Rq,Sq,Tq,Uq,Vq,Wq,Xq;Jq=function(){_.$f.register("_noop_echo",function(){this.callback(_.Ta.w$(_.Ta.rm[this.f]))})};Kq=function(){window.setTimeout(function(){_.$f.call("..","_noop_echo",_.Ta.aea)},0)};Lq=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.$f.call.apply(_.$f,f)};d._iframe_wrapped_rpc_=!0;return d};
Mq=function(a){_.Ta.dC[a]||(_.Ta.dC[a]={},_.$f.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Ta.dC[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Ta.Pq,a)&&(h=_.Ta.Pq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Vf.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Ta.dC[a]};_.Nq=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=b.length;for(var e=0;e<d;++e){var f=b[e].indexOf("=");if(f!==-1){var h=b[e].substring(0,f);f=b[e].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.Oq=function(){return _.xe.location.origin||_.xe.location.protocol+"//"+_.xe.location.host};
Pq=function(a){_.Me.h=a};Qq=function(a){_.Me.bsh=a};Rq=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Sq=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Tq=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Sq(a[d])&&!Sq(b[d])?Tq(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Sq(b[d])?[]:{},Tq(a[d],b[d])):a[d]=b[d])};
Uq=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Vq=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
Wq=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Rq("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Uq())if(e=Vq(c),d.push(25),typeof e===
"object")return e;return{}}};Xq=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Tq(c,b);a.push(c)};
_.Yq=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.di(!0);d=window.___gcfg;b=Rq("cu");a=window.___gu;d&&d!==a&&(Xq(b,d),window.___gu=d);d=Rq("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,Rq("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=Wq(f,h))&&d.push(f));c&&Xq(b,c);a=Rq("cd");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);a=Rq("ci");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);c=0;for(d=b.length;c<d;++c)Tq(_.di(),b[c],!0)};var Zq,$q=window.location.href,ar=$q.indexOf("?"),br=$q.indexOf("#");
Zq=(br===-1?$q.substr(ar+1):[$q.substr(ar+1,br-ar-1),"&",$q.substr(br+1)].join("")).split("&");for(var cr=window.decodeURIComponent?decodeURIComponent:unescape,dr=0,er=Zq.length;dr<er;++dr){var fr=Zq[dr].indexOf("=");if(fr!==-1){Zq[dr].substring(0,fr);var gr=Zq[dr].substring(fr+1);gr=gr.replace(/\+/g," ");try{cr(gr)}catch(a){}}};if(window.ToolbarApi)hr=window.ToolbarApi,hr.Ia=window.ToolbarApi.getInstance,hr.prototype=window.ToolbarApi.prototype,_.g=hr.prototype,_.g.openWindow=hr.prototype.openWindow,_.g.oQ=hr.prototype.closeWindow,_.g.B_=hr.prototype.setOnCloseHandler,_.g.XP=hr.prototype.canClosePopup,_.g.yZ=hr.prototype.resizeWindow;else{var hr=function(){};hr.Ia=function(){!ir&&window.external&&window.external.GTB_IsToolbar&&(ir=new hr);return ir};_.g=hr.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.oQ=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.B_=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.XP=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.yZ=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var ir=null;window.ToolbarApi=hr;window.ToolbarApi.getInstance=hr.Ia};var jr=/^[-_.0-9A-Za-z]+$/,kr={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},lr={onBeforeParentOpen:"beforeparentopen"},mr={onOpen:function(a){var b=a.wc();a.eh(b.container||b.element);return a},onClose:function(a){a.remove()}},nr=function(){_.Ta.hV++;return["I",_.Ta.hV,"_",(new Date).getTime()].join("")},or,pr,qr,tr,ur,vr,wr,yr,xr;_.Ta.Vn=function(a){var b=_.Ce();_.Ee(_.jm,b);_.Ee(a,b);return b};
or=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Rf(a):a};pr=function(a){var b=_.ei("googleapis.config/elog");if(b)try{b(a)}catch(c){}};qr=function(a){a&&a.match(jr)&&_.Yq("googleapis.config/gcv",a)};_.rr=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.sr=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=Mq(h);p[k]=p[k]||{};n=_.Ta.Ma.Mi.Hv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Ta.Pq)_.Ta.Pq.hasOwnProperty(q)&&f.push(q);return f.join(",")};tr=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=Lq(f,b,c)}}return d};
ur=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.Eo&&_.Eo._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};vr=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
wr=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.ei(a);a={};_.Ee(b,a);(b=a.url)&&(a.url=_.vm(b));a.params||(a.params={});return a}return{url:_.vm(a)}};yr=function(a){function b(){}b.prototype=xr.prototype;a.prototype=new b};
xr=function(a,b,c,d,e,f,h,k){this.config=wr(a);this.openParams=this.GB=b||{};this.params=c||{};this.methods=d;this.uD=!1;zr(this,b.style);this.callbacks={};Ar(this,function(){var l;(l=this.GB.style)&&_.Ta.Rw[l]?l=_.Ta.Rw[l]:l?(_.Vf.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=mr;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Ta.Ma.Mi.Hv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&Br(this,e[q],_.Ta.Ma.Mi.Hv(m,l))}f&&Br(this,"close",f)});this.Ik=this.ac=h;this.KJ=(k||[]).slice();h&&this.KJ.unshift(h.getId())};xr.prototype.wc=function(){return this.GB};xr.prototype.YG=function(){return this.params};xr.prototype.Wz=function(){return this.methods};xr.prototype.kd=function(){return this.Ik};
var zr=function(a,b){a.uD||((b=b&&!_.Ta.Rw[b]&&_.Ta.KF[b])?(a.JF=[],b(function(){a.uD=!0;for(var c=a.JF.length,d=0;d<c;++d)a.JF[d].call(a)})):a.uD=!0)},Ar=function(a,b){a.uD?b.call(a):a.JF.push(b)};xr.prototype.ye=function(a,b){Ar(this,function(){Br(this,a,b)})};var Br=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};xr.prototype.gp=function(a,b){Ar(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
xr.prototype.fi=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Vf.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),pr(k)}return h};var Cr=function(a){return typeof a=="number"?{value:a,tG:a+"px"}:a=="100%"?{value:100,tG:"100%",YV:!0}:null};xr.prototype.send=function(a,b,c){_.Ta.XZ(this,a,b,c)};
xr.prototype.register=function(a,b){var c=this;c.ye(a,function(d){b.call(c,d)})};var Dr=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,kr,e,f,h);this.id=b.id||nr();this.qw=b.rpctoken&&String(b.rpctoken)||Math.round(_.Pi()*1E9);this.nba=vr(this.params,this.config);this.hG={};Ar(this,function(){k.fi("open");_.rr(k.hG,k)})};yr(Dr);_.g=Dr.prototype;
_.g.eh=function(a,b){if(!this.config.url)return _.Vf.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Ta.rm[c]=this;var d=_.rr(this.methods);d._ready=this.FB;d._close=this.close;d._open=this.WX;d._resizeMe=this.zZ;d._renderstart=this.QX;var e=this.nba;this.qw&&(e.rpctoken=this.qw);e._methods=_.sr(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Vf.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.wc().allowPost&&(d.allowPost=!0);this.wc().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=or;this.ji=_.xm(this.config.url,a,d);a=this.ji.getAttribute("data-postorigin")||this.ji.src;_.Ta.rm[c]=this;
_.$f.OC(this.id,this.qw);_.$f.Oj(this.id,a);return this};_.g.Ph=function(a,b){this.hG[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.ji};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.FB=function(a){var b=tr(a,this.id,"");this.Ik&&typeof this.methods._ready=="function"&&(a._methods=_.sr(b,this.Ik.getId(),this.id,this,!1),this.methods._ready(a));_.rr(a,this);_.rr(b,this);this.fi("ready",a)};
_.g.QX=function(a){this.fi("renderstart",a)};_.g.close=function(a){a=this.fi("close",a);delete _.Ta.rm[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.WX=function(a){var b=tr(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(ur(a.openParams))new Er(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new Dr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;Ar(c,function(){var e={childId:c.getId()},f=c.hG;f._toclose=c.close;e._methods=_.sr(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.zZ=function(a){if(this.fi("resize",a)===void 0&&this.ji){var b=Cr(a.width);b!=null&&(this.ji.style.width=b.tG);a=Cr(a.height);a!=null&&(this.ji.style.height=a.tG);this.ji.parentElement&&(b!=null&&b.YV||a!=null&&a.YV)&&(this.ji.parentElement.style.display="block")}};
var Er=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,lr,e,f,h);this.url=a;this.Jp=null;this.eK=nr();Ar(this,function(){k.fi("beforeparentopen");var l=_.rr(k.methods);l._onopen=k.Rda;l._ready=k.FB;l._onclose=k.Pda;k.params._methods=_.sr(l,"..",k.eK,k,!0);l={};for(var m in k.params)l[m]=or(k.params[m]);_.Eo._open({url:k.config.url,openParams:k.GB,params:l,proxyId:k.eK,openedByProxyChain:k.KJ})})};yr(Er);Er.prototype.H$=function(){return this.Jp};
Er.prototype.Rda=function(a){this.Jp=a.childId;var b=tr(a,"..",this.Jp);_.rr(b,this);this.close=b._toclose;_.Ta.rm[this.Jp]=this;this.Ik&&this.methods._onopen&&(a._methods=_.sr(b,this.Ik.getId(),this.Jp,this,!1),this.methods._onopen(a))};Er.prototype.FB=function(a){var b=String(this.Jp),c=tr(a,"..",b);_.rr(a,this);_.rr(c,this);this.fi("ready",a);this.Ik&&this.methods._ready&&(a._methods=_.sr(c,this.Ik.getId(),b,this,!1),this.methods._ready(a))};
Er.prototype.Pda=function(a){if(this.Ik&&this.methods._onclose)this.methods._onclose(a);else return a=this.fi("close",a),delete _.Ta.rm[this.Jp],a};
var Fr=function(a,b,c,d,e,f,h){xr.call(this,a,b,c,d,lr,f,h);this.id=b.id||nr();this.oha=e;d._close=this.close;this.onClosed=this.JX;this.d2=0;Ar(this,function(){this.fi("beforeparentopen");var k=_.rr(this.methods);this.params._methods=_.sr(k,"..",this.eK,this,!0);k={};k.queryParams=this.params;a=_.om(_.ye,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.XP(l))};e.B_(l,this);this.d2=l})};yr(Fr);
Fr.prototype.close=function(a){a=this.fi("close",a);this.oha.oQ(this.d2);return a};Fr.prototype.JX=function(){this.fi("close")};_.Eo.send=function(a,b,c){_.Ta.XZ(_.Eo,a,b,c)};
(function(){function a(h){return _.Ta.Rw[h]}function b(h,k){_.Ta.Rw[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.zm());var k=window&&hr&&hr.Ia();k?k.yZ(h.width||0,h.height||0):_.Eo&&_.Eo._resizeMe&&_.Eo._resizeMe(h)}function d(h){qr(h)}_.Ta.rm={};_.Ta.Rw={};_.Ta.KF={};_.Ta.hV=0;_.Ta.dC={};_.Ta.Pq={};_.Ta.QB=null;_.Ta.PB=[];_.Ta.aea=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.Eo.id)}}catch(m){}try{_.Ta.QB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Ta.PB.length;++h)_.Ta.PB[h](_.Ta.QB);_.Ta.PB=[]}catch(m){pr(m)}};_.Ta.w$=function(h){var k=h&&h.Ik,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.KJ);return l};Jq();if(window.parent!=window){var e=_.Nq();e.gcv&&qr(e.gcv);var f=e.jsh;f&&Pq(f);_.rr(tr(e,"..",""),_.Eo);_.rr(e,_.Eo);Kq()}_.Ta.wb=a;_.Ta.Fc=b;_.Ta.lga=d;_.Ta.resize=c;_.Ta.U9=function(h){return _.Ta.KF[h]};_.Ta.pL=function(h,
k){_.Ta.KF[h]=k};_.Ta.xZ=c;_.Ta.Gga=d;_.Ta.uA={};_.Ta.uA.get=a;_.Ta.uA.set=b;_.Ta.allow=function(h,k){Mq(h);_.Ta.Pq[h]=k||window[h]};_.Ta.vqa=function(h){delete _.Ta.Pq[h]};_.Ta.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&hr?hr.Ia():null;return q?new Fr(h,k,l,m,q,n,p):ur(k)?new Er(h,k,l,m,n,p):new Dr(h,k,l,m,n,p)};_.Ta.close=function(h,k){_.Eo&&_.Eo._close&&_.Eo._close(h,k)};_.Ta.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.zm());m._methods=_.sr(k||{},"..","",_.Eo,!0);_.Eo&&_.Eo._ready&&_.Eo._ready(m,l)};_.Ta.KT=function(h){_.Ta.QB?h(_.Ta.QB):_.Ta.PB.push(h)};_.Ta.Tda=function(h){return!!_.Ta.rm[h]};_.Ta.d$=function(){return["https://ssl.gstatic.com/gb/js/",_.ei("googleapis.config/gcv")].join("")};_.Ta.RY=function(h){var k={mouseover:1,mouseout:1};if(_.Eo._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.Eo._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Ta.XZ=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Ta.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Ta.y7=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Ta.KT(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.$f.call.apply(_.$f,m))})};_.Ta.Fea=function(h,k){_.$f.register(h,k)};_.Ta.sga=Pq;_.Ta.f_=
Qq;_.Ta.MW=pr;_.Ta.iV=_.Eo})();_.t("iframes.allow",_.Ta.allow);_.t("iframes.callSiblingOpener",_.Ta.y7);_.t("iframes.registerForOpenedSibling",_.Ta.Fea);_.t("iframes.close",_.Ta.close);_.t("iframes.getGoogleConnectJsUri",_.Ta.d$);_.t("iframes.getHandler",_.Ta.wb);_.t("iframes.getDeferredHandler",_.Ta.U9);_.t("iframes.getParentInfo",_.Ta.KT);_.t("iframes.iframer",_.Ta.iV);_.t("iframes.open",_.Ta.open);_.t("iframes.openedId_",_.Ta.Tda);_.t("iframes.propagate",_.Ta.RY);_.t("iframes.ready",_.Ta.ready);_.t("iframes.resize",_.Ta.resize);
_.t("iframes.setGoogleConnectJsVersion",_.Ta.lga);_.t("iframes.setBootstrapHint",_.Ta.f_);_.t("iframes.setJsHint",_.Ta.sga);_.t("iframes.setHandler",_.Ta.Fc);_.t("iframes.setDeferredHandler",_.Ta.pL);_.t("IframeBase",xr);_.t("IframeBase.prototype.addCallback",xr.prototype.ye);_.t("IframeBase.prototype.getMethods",xr.prototype.Wz);_.t("IframeBase.prototype.getOpenerIframe",xr.prototype.kd);_.t("IframeBase.prototype.getOpenParams",xr.prototype.wc);_.t("IframeBase.prototype.getParams",xr.prototype.YG);
_.t("IframeBase.prototype.removeCallback",xr.prototype.gp);_.t("Iframe",Dr);_.t("Iframe.prototype.close",Dr.prototype.close);_.t("Iframe.prototype.exposeMethod",Dr.prototype.Ph);_.t("Iframe.prototype.getId",Dr.prototype.getId);_.t("Iframe.prototype.getIframeEl",Dr.prototype.getIframeEl);_.t("Iframe.prototype.getSiteEl",Dr.prototype.getSiteEl);_.t("Iframe.prototype.openInto",Dr.prototype.eh);_.t("Iframe.prototype.remove",Dr.prototype.remove);_.t("Iframe.prototype.setSiteEl",Dr.prototype.setSiteEl);
_.t("Iframe.prototype.addCallback",Dr.prototype.ye);_.t("Iframe.prototype.getMethods",Dr.prototype.Wz);_.t("Iframe.prototype.getOpenerIframe",Dr.prototype.kd);_.t("Iframe.prototype.getOpenParams",Dr.prototype.wc);_.t("Iframe.prototype.getParams",Dr.prototype.YG);_.t("Iframe.prototype.removeCallback",Dr.prototype.gp);_.t("IframeProxy",Er);_.t("IframeProxy.prototype.getTargetIframeId",Er.prototype.H$);_.t("IframeProxy.prototype.addCallback",Er.prototype.ye);_.t("IframeProxy.prototype.getMethods",Er.prototype.Wz);
_.t("IframeProxy.prototype.getOpenerIframe",Er.prototype.kd);_.t("IframeProxy.prototype.getOpenParams",Er.prototype.wc);_.t("IframeProxy.prototype.getParams",Er.prototype.YG);_.t("IframeProxy.prototype.removeCallback",Er.prototype.gp);_.t("IframeWindow",Fr);_.t("IframeWindow.prototype.close",Fr.prototype.close);_.t("IframeWindow.prototype.onClosed",Fr.prototype.JX);_.t("iframes.util.getTopMostAccessibleWindow",_.Ta.Ma.Mi.ZT);_.t("iframes.handlers.get",_.Ta.uA.get);_.t("iframes.handlers.set",_.Ta.uA.set);
_.t("iframes.resizeMe",_.Ta.xZ);_.t("iframes.setVersionOverride",_.Ta.Gga);_.t("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Ta.CROSS_ORIGIN_IFRAMES_FILTER);_.t("IframeBase.prototype.send",xr.prototype.send);_.t("IframeBase.prototype.register",xr.prototype.register);_.t("Iframe.prototype.send",Dr.prototype.send);_.t("Iframe.prototype.register",Dr.prototype.register);_.t("IframeProxy.prototype.send",Er.prototype.send);_.t("IframeProxy.prototype.register",Er.prototype.register);
_.t("IframeWindow.prototype.send",Fr.prototype.send);_.t("IframeWindow.prototype.register",Fr.prototype.register);_.t("iframes.iframer.send",_.Ta.iV.send);
var th;_.sh=function(a){_.Xa.setTimeout(function(){throw a;},0)};th=0;_.uh=function(a){return Object.prototype.hasOwnProperty.call(a,_.ab)&&a[_.ab]||(a[_.ab]=++th)};
_.vh=function(){return _.Mc("Safari")&&!(_.Wc()||(_.Oc()?0:_.Mc("Coast"))||_.Pc()||_.Sc()||_.Tc()||_.Uc()||_.Vc()||_.Mc("Silk")||_.Mc("Android"))};_.wh=function(){return _.Mc("Android")&&!(_.Wc()||_.Vc()||_.Pc()||_.Mc("Silk"))};_.yh=_.Vc();_.zh=_.ad()||_.Mc("iPod");_.Ah=_.Mc("iPad");_.Bh=_.wh();_.Ch=_.Wc();_.Dh=_.vh()&&!_.bd();
_.Eh=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.kd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Fh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.uh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Gh=function(a){for(var b in a)return!1;return!0};
_.Hh=function(a,b){a.src=_.kc(b);(b=_.Gc("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Ih=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Jh,Kh,Mh;Jh={};Kh=null;_.Lh=_.Bd||_.Cd||!_.Dh&&typeof _.Xa.atob=="function";_.Nh=function(a,b){b===void 0&&(b=0);Mh();b=Jh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Oh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Kh[m];if(n!=null)return n;if(!_.xc(m))throw Error("w`"+m);}return l}Mh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Mh=function(){if(!Kh){Kh={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Jh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Kh[f]===void 0&&(Kh[f]=e)}}}};
_.Ph=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Qh=function(a){return a==null?"":String(a)};_.Rh=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Sh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Th=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Uh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Uh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Vh=function(a){var b=[],c;for(c in a)_.Uh(c,a[c],b);return b.join("&")};
_.Wh=function(a,b){b=_.Vh(b);return _.Th(a,b)};
_.cj=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.dj=function(){this.Lg=this.Lg;this.Qo=this.Qo};_.dj.prototype.Lg=!1;_.dj.prototype.isDisposed=function(){return this.Lg};_.dj.prototype.dispose=function(){this.Lg||(this.Lg=!0,this.ua())};_.dj.prototype[Symbol.dispose]=function(){this.dispose()};_.fj=function(a,b){_.ej(a,_.bb(_.cj,b))};_.ej=function(a,b){a.Lg?b():(a.Qo||(a.Qo=[]),a.Qo.push(b))};_.dj.prototype.ua=function(){if(this.Qo)for(;this.Qo.length;)this.Qo.shift()()};
var hj;_.gj=function(a,b){b=(0,_.sb)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.ij=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<hj.length;f++)c=hj[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};hj="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.jj=[];_.kj=[];_.lj=!1;
_.mj=function(a){_.jj[_.jj.length]=a;if(_.lj)for(var b=0;b<_.kj.length;b++)a((0,_.z)(_.kj[b].wrap,_.kj[b]))};
var nj;nj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.oj=function(a){this.src=a;this.je={};this.ox=0};_.qj=function(a,b){this.type="function"==typeof _.pj&&a instanceof _.pj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.dw=!1};_.qj.prototype.stopPropagation=function(){this.dw=!0};_.qj.prototype.preventDefault=function(){this.defaultPrevented=!0};_.rj=function(a,b){_.qj.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.XJ=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Df=null;a&&this.init(a,b)};_.eb(_.rj,_.qj);
_.rj.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.Cd||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.Cd||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.XJ=_.Ed?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.Df=a;a.defaultPrevented&&_.rj.N.preventDefault.call(this)};_.rj.prototype.stopPropagation=function(){_.rj.N.stopPropagation.call(this);this.Df.stopPropagation?this.Df.stopPropagation():this.Df.cancelBubble=!0};_.rj.prototype.preventDefault=function(){_.rj.N.preventDefault.call(this);var a=this.Df;a.preventDefault?a.preventDefault():a.returnValue=!1};_.sj="closure_listenable_"+(Math.random()*1E6|0);_.tj=function(a){return!(!a||!a[_.sj])};var uj=0;var vj=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Jf=e;this.key=++uj;this.iw=this.vy=!1},wj=function(a){a.iw=!0;a.listener=null;a.proxy=null;a.src=null;a.Jf=null};_.oj.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.je[f];a||(a=this.je[f]=[],this.ox++);var h=xj(a,b,d,e);h>-1?(b=a[h],c||(b.vy=!1)):(b=new vj(b,this.src,f,!!d,e),b.vy=c,a.push(b));return b};_.oj.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.je))return!1;var e=this.je[a];b=xj(e,b,c,d);return b>-1?(wj(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.je[a],this.ox--),!0):!1};
_.yj=function(a,b){var c=b.type;if(!(c in a.je))return!1;var d=_.gj(a.je[c],b);d&&(wj(b),a.je[c].length==0&&(delete a.je[c],a.ox--));return d};_.oj.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.je)if(!a||c==a){for(var d=this.je[c],e=0;e<d.length;e++)++b,wj(d[e]);delete this.je[c];this.ox--}return b};_.oj.prototype.Hq=function(a,b,c,d){a=this.je[a.toString()];var e=-1;a&&(e=xj(a,b,c,d));return e>-1?a[e]:null};
_.oj.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return nj(this.je,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var xj=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.iw&&f.listener==b&&f.capture==!!c&&f.Jf==d)return e}return-1};var zj,Aj,Bj,Fj,Hj,Ij,Jj,Lj;zj="closure_lm_"+(Math.random()*1E6|0);Aj={};Bj=0;_.Dj=function(a,b,c,d,e){if(d&&d.once)return _.Cj(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Dj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.na(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!1,d,e)};
Fj=function(a,b,c,d,e,f){if(!b)throw Error("B");var h=_.vb(e)?!!e.capture:!!e,k=_.Gj(a);k||(a[zj]=k=new _.oj(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=Hj();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)_.vi||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Ij(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("C");Bj++;return c};
Hj=function(){var a=Jj,b=function(c){return a.call(b.src,b.listener,c)};return b};_.Cj=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Cj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.rr(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!0,d,e)};
_.Kj=function(a){if(typeof a==="number"||!a||a.iw)return!1;var b=a.src;if(_.tj(b))return b.DN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Ij(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bj--;(c=_.Gj(b))?(_.yj(c,a),c.ox==0&&(c.src=null,b[zj]=null)):wj(a);return!0};Ij=function(a){return a in Aj?Aj[a]:Aj[a]="on"+a};
Jj=function(a,b){if(a.iw)a=!0;else{b=new _.rj(b,this);var c=a.listener,d=a.Jf||a.src;a.vy&&_.Kj(a);a=c.call(d,b)}return a};_.Gj=function(a){a=a[zj];return a instanceof _.oj?a:null};Lj="__closure_events_fn_"+(Math.random()*1E9>>>0);_.Ej=function(a){if(typeof a==="function")return a;a[Lj]||(a[Lj]=function(b){return a.handleEvent(b)});return a[Lj]};_.mj(function(a){Jj=a(Jj)});
_.Mj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Zd.prototype.O=_.pb(1,function(a){return _.be(this.Bc,a)});_.Nj=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Nj(a,b[f],c,d,e);else d=_.vb(d)?!!d.capture:!!d,c=_.Ej(c),_.tj(a)?a.Ac(b,c,d,e):a&&(a=_.Gj(a))&&(b=a.Hq(b,c,d,e))&&_.Kj(b)};_.Oj=function(){_.dj.call(this);this.rk=new _.oj(this);this.U6=this;this.PJ=null};_.eb(_.Oj,_.dj);_.Oj.prototype[_.sj]=!0;_.g=_.Oj.prototype;_.g.Yn=function(){return this.PJ};
_.g.aD=function(a){this.PJ=a};_.g.addEventListener=function(a,b,c,d){_.Dj(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.Nj(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.Yn();if(c)for(b=[];c;c=c.Yn())b.push(c);c=this.U6;var d=a.type||a;if(typeof a==="string")a=new _.qj(a,c);else if(a instanceof _.qj)a.target=a.target||c;else{var e=a;a=new _.qj(d,c);_.ij(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.dw&&f>=0;f--){var h=a.currentTarget=b[f];e=h.iu(d,!0,a)&&e}a.dw||(h=a.currentTarget=c,e=h.iu(d,!0,a)&&e,a.dw||(e=h.iu(d,!1,a)&&e));if(b)for(f=0;!a.dw&&f<b.length;f++)h=a.currentTarget=b[f],e=h.iu(d,!1,a)&&e;return e};
_.g.ua=function(){_.Oj.N.ua.call(this);this.tK();this.PJ=null};_.g.na=function(a,b,c,d){return this.rk.add(String(a),b,!1,c,d)};_.g.rr=function(a,b,c,d){return this.rk.add(String(a),b,!0,c,d)};_.g.Ac=function(a,b,c,d){return this.rk.remove(String(a),b,c,d)};_.g.DN=function(a){return _.yj(this.rk,a)};_.g.tK=function(){this.rk&&this.rk.removeAll(void 0)};
_.g.iu=function(a,b,c){a=this.rk.je[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.iw&&f.capture==b){var h=f.listener,k=f.Jf||f.src;f.vy&&this.DN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Hq=function(a,b,c,d){return this.rk.Hq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.rk.hasListener(a!==void 0?String(a):void 0,b)};
var jk;_.fk=function(a){var b={},c;for(c in a)b[c]=a[c];return b};jk=function(){for(var a;a=gk.remove();){try{a.Rh.call(a.scope)}catch(b){_.sh(b)}hk.put(a)}ik=!1};_.kk=function(a){if(!(a instanceof Array)){a=_.Aa(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};_.lk=function(){};_.mk=function(a){a.prototype.$goog_Thenable=!0};_.nk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};
_.ok=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var pk=function(a,b){this.l8=a;this.afa=b;this.AB=0;this.AA=null};pk.prototype.get=function(){if(this.AB>0){this.AB--;var a=this.AA;this.AA=a.next;a.next=null}else a=this.l8();return a};pk.prototype.put=function(a){this.afa(a);this.AB<100&&(this.AB++,a.next=this.AA,this.AA=a)};_.qk=function(a){return a};_.mj(function(a){_.qk=a});var rk=function(){this.WD=this.Os=null};rk.prototype.add=function(a,b){var c=hk.get();c.set(a,b);this.WD?this.WD.next=c:this.Os=c;this.WD=c};rk.prototype.remove=function(){var a=null;this.Os&&(a=this.Os,this.Os=this.Os.next,this.Os||(this.WD=null),a.next=null);return a};var hk=new pk(function(){return new sk},function(a){return a.reset()}),sk=function(){this.next=this.scope=this.Rh=null};sk.prototype.set=function(a,b){this.Rh=a;this.scope=b;this.next=null};
sk.prototype.reset=function(){this.next=this.scope=this.Rh=null};var tk,ik,gk,uk;ik=!1;gk=new rk;_.vk=function(a,b){tk||uk();ik||(tk(),ik=!0);gk.add(a,b)};uk=function(){var a=Promise.resolve(void 0);tk=function(){a.then(jk)}};var yk,zk,Ak,Ok,Sk,Qk,Tk;_.xk=function(a,b){this.Ca=0;this.qf=void 0;this.cq=this.Bl=this.Gb=null;this.qA=this.fG=!1;if(a!=_.lk)try{var c=this;a.call(b,function(d){wk(c,2,d)},function(d){wk(c,3,d)})}catch(d){wk(this,3,d)}};yk=function(){this.next=this.context=this.Er=this.Ov=this.xn=null;this.Ux=!1};yk.prototype.reset=function(){this.context=this.Er=this.Ov=this.xn=null;this.Ux=!1};zk=new pk(function(){return new yk},function(a){a.reset()});
Ak=function(a,b,c){var d=zk.get();d.Ov=a;d.Er=b;d.context=c;return d};_.Bk=function(a){if(a instanceof _.xk)return a;var b=new _.xk(_.lk);wk(b,2,a);return b};_.Ck=function(a){return new _.xk(function(b,c){c(a)})};_.Ek=function(a,b,c){Dk(a,b,c,null)||_.vk(_.bb(b,a))};_.Fk=function(a){return new _.xk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k,l=0;l<a.length;l++)k=a[l],_.Ek(k,_.bb(f,l),h);else b(e)})};
_.Hk=function(){var a,b,c=new _.xk(function(d,e){a=d;b=e});return new Gk(c,a,b)};_.xk.prototype.then=function(a,b,c){return Ik(this,(0,_.ok)(typeof a==="function"?a:null),(0,_.ok)(typeof b==="function"?b:null),c)};_.mk(_.xk);var Kk=function(a,b,c,d){Jk(a,Ak(b||_.lk,c||null,d))};_.xk.prototype.finally=function(a){var b=this;a=(0,_.ok)(a);return new Promise(function(c,d){Kk(b,function(e){a();c(e)},function(e){a();d(e)})})};_.xk.prototype.zD=function(a,b){return Ik(this,null,(0,_.ok)(a),b)};
_.xk.prototype.catch=_.xk.prototype.zD;_.xk.prototype.cancel=function(a){if(this.Ca==0){var b=new _.Lk(a);_.vk(function(){Mk(this,b)},this)}};
var Mk=function(a,b){if(a.Ca==0)if(a.Gb){var c=a.Gb;if(c.Bl){for(var d=0,e=null,f=null,h=c.Bl;h&&(h.Ux||(d++,h.xn==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ca==0&&d==1?Mk(c,b):(f?(d=f,d.next==c.cq&&(c.cq=d),d.next=d.next.next):Nk(c),Ok(c,e,3,b)))}a.Gb=null}else wk(a,3,b)},Jk=function(a,b){a.Bl||a.Ca!=2&&a.Ca!=3||Pk(a);a.cq?a.cq.next=b:a.Bl=b;a.cq=b},Ik=function(a,b,c,d){var e=Ak(null,null,null);e.xn=new _.xk(function(f,h){e.Ov=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Er=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.Lk?h(k):f(l)}catch(m){h(m)}}:h});e.xn.Gb=a;Jk(a,e);return e.xn};_.xk.prototype.sha=function(a){this.Ca=0;wk(this,2,a)};_.xk.prototype.tha=function(a){this.Ca=0;wk(this,3,a)};
var wk=function(a,b,c){a.Ca==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ca=1,Dk(c,a.sha,a.tha,a)||(a.qf=c,a.Ca=b,a.Gb=null,Pk(a),b!=3||c instanceof _.Lk||Qk(a,c)))},Dk=function(a,b,c,d){if(a instanceof _.xk)return Kk(a,b,c,d),!0;if(_.nk(a))return a.then(b,c,d),!0;if(_.vb(a))try{var e=a.then;if(typeof e==="function")return Rk(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Rk=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||(f=!0,
d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},Pk=function(a){a.fG||(a.fG=!0,_.vk(a.nz,a))},Nk=function(a){var b=null;a.Bl&&(b=a.Bl,a.Bl=b.next,b.next=null);a.Bl||(a.cq=null);return b};_.xk.prototype.nz=function(){for(var a;a=Nk(this);)Ok(this,a,this.Ca,this.qf);this.fG=!1};Ok=function(a,b,c,d){if(c==3&&b.Er&&!b.Ux)for(;a&&a.qA;a=a.Gb)a.qA=!1;if(b.xn)b.xn.Gb=null,Sk(b,c,d);else try{b.Ux?b.Ov.call(b.context):Sk(b,c,d)}catch(e){Tk.call(null,e)}zk.put(b)};
Sk=function(a,b,c){b==2?a.Ov.call(a.context,c):a.Er&&a.Er.call(a.context,c)};Qk=function(a,b){a.qA=!0;_.vk(function(){a.qA&&Tk.call(null,b)})};Tk=_.sh;_.Lk=function(a){_.lb.call(this,a);this.rZ=!1};_.eb(_.Lk,_.lb);_.Lk.prototype.name="cancel";var Gk=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
var bk=function(a){this.T=a};_.g=bk.prototype;_.g.value=function(){return this.T};_.g.Ne=function(a){this.T.width=a;return this};_.g.Qb=function(){return this.T.width};_.g.Td=function(a){this.T.height=a;return this};_.g.Nc=function(){return this.T.height};_.g.Ei=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.ck=function(a){this.T=a||{}};_.g=_.ck.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Ei=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Me=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.Zm=function(a){this.T.rpctoken=a;return this};_.dk=function(a,b){a.T.messageHandlers=b;return a};_.ek=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.ck.prototype;_.g.Wr=_.jb(4);_.g.getContext=function(){return this.T.context};_.g.kd=function(){return this.T.openerIframe};_.g.Vn=function(){this.T.attributes=this.T.attributes||{};return new bk(this.T.attributes)};_.g.Fz=_.jb(5);
_.to=function(a,b){a.T.where=b;return a};_.uo=function(){_.ck.apply(this,arguments)};_.y(_.uo,_.ck);
var ts,us;_.ls=function(a){if(a instanceof _.gc)return a.PY;throw Error("j");};_.ms=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.ns=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.os=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.os.prototype;_.g.clone=function(){return new _.os(this.x,this.y)};_.g.equals=function(a){return a instanceof _.os&&_.ns(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};
_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.os?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.ps=function(a){return a.scrollingElement?a.scrollingElement:!_.Cd&&_.he(a)?a.documentElement:a.body||a.documentElement};
_.qs=function(a){var b=_.ps(a);a=a.defaultView;return new _.os(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.rs=function(a,b,c,d){return _.ce(a.Bc,b,c,d)};_.ss=function(a){return _.qs(a.Bc)};ts=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};us=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.vs=function(a){return _.be(document,a)};_.g=_.ms.prototype;_.g.Qb=function(){return this.right-this.left};_.g.Nc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.ms(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.ms?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.vb(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var ys,ws,Cs,Es;_.xs=function(a,b,c){if(typeof b==="string")(b=ws(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=ws(c,d);f&&(c.style[f]=e)}};ys={};ws=function(a,b){var c=ys[b];if(!c){var d=ts(b);c=d;a.style[d]===void 0&&(d=(_.Cd?"Webkit":_.Bd?"Moz":null)+us(d),a.style[d]!==void 0&&(c=d));ys[b]=c}return c};_.zs=function(a,b){var c=a.style[ts(b)];return typeof c!=="undefined"?c:a.style[ws(a,b)]||""};
_.As=function(a,b){var c=_.$d(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Bs=function(a,b){return _.As(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Cs=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Fs=function(a,b){b=b||_.ps(document);var c=b||_.ps(document);var d=_.Ds(a),e=_.Ds(c),f=_.As(c,"borderLeftWidth");var h=_.As(c,"borderRightWidth");var k=_.As(c,"borderTopWidth"),l=_.As(c,"borderBottomWidth");h=new _.ms(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.ps(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Es(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.os(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Ds=function(a){var b=_.$d(a),c=new _.os(0,0);if(a==(b?_.$d(b):document).documentElement)return c;a=Cs(a);b=_.ss(_.ae(b));c.x=a.left+b.x;c.y=a.top+b.y;return c};_.Hs=function(a,b){var c=new _.os(0,0),d=_.ie(_.$d(a));a:{try{_.Wb(d.parent);var e=!0;break a}catch(f){}e=!1}if(!e)return c;do e=d==b?_.Ds(a):_.Gs(a),c.x+=e.x,c.y+=e.y;while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};
_.Gs=function(a){a=Cs(a);return new _.os(a.left,a.top)};_.Js=function(a,b,c){if(b instanceof _.rd)c=b.height,b=b.width;else if(c==void 0)throw Error("J");a.style.width=_.Is(b,!0);a.style.height=_.Is(c,!0)};_.Is=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};
_.Ks=function(a){var b=Es;if(_.Bs(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};Es=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.Cd&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Cs(a),new _.rd(a.right-a.left,a.bottom-a.top)):new _.rd(b,c)};_.Ls=function(a,b){a.style.display=b?"":"none"};
_.Ns=function(a){var b=_.ae(void 0),c=_.rs(b,"HEAD")[0];if(!c){var d=_.rs(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Gc("style",document))&&d.setAttribute("nonce",e);_.Ms(d,a);b.appendChild(c,d)};_.Ms=function(a,b){b=_.ls(b);_.Xa.trustedTypes?_.ve(a,b):a.innerHTML=b};_.Os=_.Bd?"MozUserSelect":_.Cd||_.zd?"WebkitUserSelect":null;
_.Ps=function(a){return"rtl"==_.Bs(a,"direction")};_.Qs=function(a,b,c,d){this.left=a;this.top=b;this.width=c;this.height=d};_.g=_.Qs.prototype;_.g.clone=function(){return new _.Qs(this.left,this.top,this.width,this.height)};_.g.intersects=function(a){return this.left<=a.left+a.width&&a.left<=this.left+this.width&&this.top<=a.top+a.height&&a.top<=this.top+this.height};
_.g.contains=function(a){return a instanceof _.os?a.x>=this.left&&a.x<=this.left+this.width&&a.y>=this.top&&a.y<=this.top+this.height:this.left<=a.left&&this.left+this.width>=a.left+a.width&&this.top<=a.top&&this.top+this.height>=a.top+a.height};_.g.distance=function(a){var b=a.x<this.left?this.left-a.x:Math.max(a.x-(this.left+this.width),0);a=a.y<this.top?this.top-a.y:Math.max(a.y-(this.top+this.height),0);return Math.sqrt(b*b+a*a)};_.g.getSize=function(){return new _.rd(this.width,this.height)};
_.g.getCenter=function(){return new _.os(this.left+this.width/2,this.top+this.height/2)};_.g.ceil=function(){this.left=Math.ceil(this.left);this.top=Math.ceil(this.top);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.g.floor=function(){this.left=Math.floor(this.left);this.top=Math.floor(this.top);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.g.round=function(){this.left=Math.round(this.left);this.top=Math.round(this.top);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.top+=a.y):(this.left+=a,typeof b==="number"&&(this.top+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.width*=a;this.top*=b;this.height*=b;return this};_.Rs=function(a){return _.Bs(a,"position")};
_.Ss=function(a,b,c){if(b instanceof _.os){var d=b.x;b=b.y}else d=b,b=c;a.style.left=_.Is(d,!1);a.style.top=_.Is(b,!1)};_.Ts=function(a,b){a=a.style;"opacity"in a?a.opacity=b:"MozOpacity"in a?a.MozOpacity=b:"filter"in a&&(a.filter=b===""?"":"alpha(opacity="+Number(b)*100+")")};_.Us=function(){if(_.Fd){var a=/Windows NT ([0-9.]+)/;return(a=a.exec(_.Jc()))?a[1]:"0"}return _.Ed?(a=/1[0|1][_.][0-9_.]+/,(a=a.exec(_.Jc()))?a[0].replace(/_/g,"."):"10"):_.Id?(a=/Android\s+([^\);]+)(\)|;)/,(a=a.exec(_.Jc()))?a[1]:""):_.Jd||_.Kd||_.Ld?(a=/(?:iPhone|CPU)\s+OS\s+(\S+)/,(a=a.exec(_.Jc()))?a[1].replace(/_/g,"."):""):""}();var Vs;Vs=function(a){return(a=a.exec(_.Jc()))?a[1]:""};_.Ws=function(){if(_.yh)return Vs(/Firefox\/([0-9.]+)/);if(_.yd||_.zd||_.xd)return _.Vd;if(_.Ch){if(_.bd()||_.cd()){var a=Vs(/CriOS\/([0-9.]+)/);if(a)return a}return Vs(/Chrome\/([0-9.]+)/)}if(_.Dh&&!_.bd())return Vs(/Version\/([0-9.]+)/);if(_.zh||_.Ah){if(a=/Version\/(\S+).*Mobile\/(\S+)/.exec(_.Jc()))return a[1]+"."+a[2]}else if(_.Bh)return(a=Vs(/Android\s+([0-9.]+)/))?a:Vs(/Version\/([0-9.]+)/);return""}();
var ct;ct=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.dt=function(a,b,c,d){return Array.prototype.splice.apply(a,ct(arguments,1))};_.et=function(a,b,c){if(a!==null&&b in a)throw Error("h`"+b);a[b]=c};_.ft=function(a,b){var c=b||document;c.getElementsByClassName?a=c.getElementsByClassName(a)[0]:(c=document,a=a?(b||c).querySelector(a?"."+a:""):_.ce(c,"*",a,b)[0]||null);return a||null};
_.gt=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.it=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.jt=function(a){_.dj.call(this);this.ug=a;this.mc={}};_.eb(_.jt,_.dj);var kt=[];_.jt.prototype.na=function(a,b,c,d){return this.Dv(a,b,c,d)};
_.jt.prototype.Dv=function(a,b,c,d,e){Array.isArray(b)||(b&&(kt[0]=b.toString()),b=kt);for(var f=0;f<b.length;f++){var h=_.Dj(a,b[f],c||this.handleEvent,d||!1,e||this.ug||this);if(!h)break;this.mc[h.key]=h}return this};_.jt.prototype.rr=function(a,b,c,d){return lt(this,a,b,c,d)};var lt=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)lt(a,b,c[h],d,e,f);else{b=_.Cj(b,c,d||a.handleEvent,e,f||a.ug||a);if(!b)return a;a.mc[b.key]=b}return a};
_.jt.prototype.Ac=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.Ac(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.vb(d)?!!d.capture:!!d,e=e||this.ug||this,c=_.Ej(c),d=!!d,b=_.tj(a)?a.Hq(b,c,d,e):a?(a=_.Gj(a))?a.Hq(b,c,d,e):null:null,b&&(_.Kj(b),delete this.mc[b.key]);return this};_.jt.prototype.removeAll=function(){_.Zb(this.mc,function(a,b){this.mc.hasOwnProperty(b)&&_.Kj(a)},this);this.mc={}};_.jt.prototype.ua=function(){_.jt.N.ua.call(this);this.removeAll()};
_.jt.prototype.handleEvent=function(){throw Error("K");};
var Dt,Et,Ht;Dt=_.gd(["about:blank"]);Et=_.gd(["javascript:undefined"]);_.Ft=_.vc(Dt);_.Gt=_.kc(_.Ft).toString();Ht=_.vc(Et);_.kc(Ht);
var vu,Au;_.ru=function(a,b){var c=_.kd(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(a==null)return;a=a[d[c]]}return a};
_.su=function(a){if(!a||typeof a!=="object")return a;if(typeof a.clone==="function")return a.clone();if(typeof Map!=="undefined"&&a instanceof Map)return new Map(a);if(typeof Set!=="undefined"&&a instanceof Set)return new Set(a);if(a instanceof Date)return new Date(a.getTime());var b=Array.isArray(a)?[]:typeof ArrayBuffer!=="function"||typeof ArrayBuffer.isView!=="function"||!ArrayBuffer.isView(a)||a instanceof DataView?{}:new a.constructor(a.length),c;for(c in a)b[c]=_.su(a[c]);return b};
_.tu=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.ld()).toString(36)};_.uu=function(a,b,c){return _.le(document,arguments)};vu=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
_.wu=function(a,b,c){for(var d=0,e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)return d;d+=e+1}return-1};_.xu=/#|$/;_.yu=function(a){if(a.Ye&&typeof a.Ye=="function")return a.Ye();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(_.kd(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.ub(a)};
_.zu=function(a){if(a.lg&&typeof a.lg=="function")return a.lg();if(!a.Ye||typeof a.Ye!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(_.kd(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.Ph(a)}}};
Au=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(_.kd(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=_.zu(a),e=_.yu(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};var Ou,Iu,Su,Ku,Ju,Mu,Lu,Pu,Nu,Tu;
_.Bu=function(a,b){this.Xd=this.wh=this.Ci="";this.Bg=null;this.uG=this.Mm="";this.Tg=!1;var c;a instanceof _.Bu?(this.Tg=b!==void 0?b:a.Tg,_.Cu(this,a.Ci),_.Du(this,a.wh),_.Eu(this,a.Ng()),_.Fu(this,a.Bg),this.setPath(a.getPath()),_.Gu(this,a.Qd.clone()),this.Uk(a.Mz())):a&&(c=String(a).match(_.Sh))?(this.Tg=!!b,_.Cu(this,c[1]||"",!0),_.Du(this,c[2]||"",!0),_.Eu(this,c[3]||"",!0),_.Fu(this,c[4]),this.setPath(c[5]||"",!0),_.Gu(this,c[6]||"",!0),this.Uk(c[7]||"",!0)):(this.Tg=!!b,this.Qd=new _.Hu(null,
this.Tg))};_.Bu.prototype.toString=function(){var a=[],b=this.Ci;b&&a.push(Iu(b,Ju,!0),":");var c=this.Ng();if(c||b=="file")a.push("//"),(b=this.wh)&&a.push(Iu(b,Ju,!0),"@"),a.push(Ku(encodeURIComponent(String(c)))),c=this.Bg,c!=null&&a.push(":",String(c));if(c=this.getPath())this.Xd&&c.charAt(0)!="/"&&a.push("/"),a.push(Iu(c,c.charAt(0)=="/"?Lu:Mu,!0));(c=this.Qd.toString())&&a.push("?",c);(c=this.Mz())&&a.push("#",Iu(c,Nu));return a.join("")};
_.Bu.prototype.resolve=function(a){var b=this.clone(),c=!!a.Ci;c?_.Cu(b,a.Ci):c=!!a.wh;c?_.Du(b,a.wh):c=!!a.Xd;c?_.Eu(b,a.Ng()):c=a.Bg!=null;var d=a.getPath();if(c)_.Fu(b,a.Bg);else if(c=!!a.Mm){if(d.charAt(0)!="/")if(this.Xd&&!this.Mm)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(_.yc(e,"./")||_.yc(e,"/.")){d=_.wc(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];k=="."?d&&h==e.length&&f.push(""):k==".."?((f.length>
1||f.length==1&&f[0]!="")&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.Uq();c?_.Gu(b,a.Qd.clone()):c=!!a.uG;c&&b.Uk(a.Mz());return b};_.Bu.prototype.clone=function(){return new _.Bu(this)};_.Cu=function(a,b,c){a.Ci=c?Ou(b,!0):b;a.Ci&&(a.Ci=a.Ci.replace(/:$/,""));return a};_.Du=function(a,b,c){a.wh=c?Ou(b):b;return a};_.Bu.prototype.Ng=function(){return this.Xd};_.Eu=function(a,b,c){a.Xd=c?Ou(b,!0):b;return a};
_.Fu=function(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("L`"+b);a.Bg=b}else a.Bg=null;return a};_.Bu.prototype.getPath=function(){return this.Mm};_.Bu.prototype.setPath=function(a,b){this.Mm=b?Ou(a,!0):a;return this};_.Bu.prototype.Uq=function(){return this.Qd.toString()!==""};_.Gu=function(a,b,c){b instanceof _.Hu?(a.Qd=b,a.Qd.LL(a.Tg)):(c||(b=Iu(b,Pu)),a.Qd=new _.Hu(b,a.Tg));return a};_.Bu.prototype.hb=function(a,b){return _.Gu(this,a,b)};_.Bu.prototype.getQuery=function(){return this.Qd.toString()};
_.Qu=function(a,b,c){a.Qd.set(b,c);return a};_.g=_.Bu.prototype;_.g.Pg=function(a){return this.Qd.get(a)};_.g.Mz=function(){return this.uG};_.g.Uk=function(a,b){this.uG=b?Ou(a):a;return this};_.g.removeParameter=function(a){this.Qd.remove(a);return this};_.g.LL=function(a){this.Tg=a;this.Qd&&this.Qd.LL(a)};_.Ru=function(a,b){return a instanceof _.Bu?a.clone():new _.Bu(a,b)};Ou=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
Iu=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Su),c&&(a=Ku(a)),a):null};Su=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};Ku=function(a){return a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")};Ju=/[#\/\?@]/g;Mu=/[#\?:]/g;Lu=/[#\?]/g;Pu=/[#\?@]/g;Nu=/#/g;_.Hu=function(a,b){this.Ae=this.Lc=null;this.jg=a||null;this.Tg=!!b};Tu=function(a){a.Lc||(a.Lc=new Map,a.Ae=0,a.jg&&vu(a.jg,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};
_.g=_.Hu.prototype;_.g.Zb=function(){Tu(this);return this.Ae};_.g.add=function(a,b){Tu(this);this.jg=null;a=Uu(this,a);var c=this.Lc.get(a);c||this.Lc.set(a,c=[]);c.push(b);this.Ae+=1;return this};_.g.remove=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)?(this.jg=null,this.Ae-=this.Lc.get(a).length,this.Lc.delete(a)):!1};_.g.clear=function(){this.Lc=this.jg=null;this.Ae=0};_.g.isEmpty=function(){Tu(this);return this.Ae==0};_.g.Fl=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)};
_.g.forEach=function(a,b){Tu(this);this.Lc.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.g.lg=function(){Tu(this);for(var a=Array.from(this.Lc.values()),b=Array.from(this.Lc.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.g.Ye=function(a){Tu(this);var b=[];if(typeof a==="string")this.Fl(a)&&(b=b.concat(this.Lc.get(Uu(this,a))));else{a=Array.from(this.Lc.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
_.g.set=function(a,b){Tu(this);this.jg=null;a=Uu(this,a);this.Fl(a)&&(this.Ae-=this.Lc.get(a).length);this.Lc.set(a,[b]);this.Ae+=1;return this};_.g.get=function(a,b){if(!a)return b;a=this.Ye(a);return a.length>0?String(a[0]):b};_.g.setValues=function(a,b){this.remove(a);b.length>0&&(this.jg=null,this.Lc.set(Uu(this,a),_.Yb(b)),this.Ae+=b.length)};
_.g.toString=function(){if(this.jg)return this.jg;if(!this.Lc)return"";for(var a=[],b=Array.from(this.Lc.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Ye(d);for(var f=0;f<d.length;f++){var h=e;d[f]!==""&&(h+="="+encodeURIComponent(String(d[f])));a.push(h)}}return this.jg=a.join("&")};_.g.clone=function(){var a=new _.Hu;a.jg=this.jg;this.Lc&&(a.Lc=new Map(this.Lc),a.Ae=this.Ae);return a};var Uu=function(a,b){b=String(b);a.Tg&&(b=b.toLowerCase());return b};
_.Hu.prototype.LL=function(a){a&&!this.Tg&&(Tu(this),this.jg=null,this.Lc.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.Tg=a};_.Hu.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Au(arguments[b],function(c,d){this.add(d,c)},this)};
_.rz=function(a,b){_.Oj.call(this);this.Cm=a||1;this.gx=b||_.Xa;this.OP=(0,_.z)(this.mha,this);this.xW=_.ld()};_.eb(_.rz,_.Oj);_.g=_.rz.prototype;_.g.enabled=!1;_.g.Hc=null;_.g.setInterval=function(a){this.Cm=a;this.Hc&&this.enabled?(this.stop(),this.start()):this.Hc&&this.stop()};
_.g.mha=function(){if(this.enabled){var a=_.ld()-this.xW;a>0&&a<this.Cm*.8?this.Hc=this.gx.setTimeout(this.OP,this.Cm-a):(this.Hc&&(this.gx.clearTimeout(this.Hc),this.Hc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Hc||(this.Hc=this.gx.setTimeout(this.OP,this.Cm),this.xW=_.ld())};_.g.stop=function(){this.enabled=!1;this.Hc&&(this.gx.clearTimeout(this.Hc),this.Hc=null)};_.g.ua=function(){_.rz.N.ua.call(this);this.stop();delete this.gx};
_.sz=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.z)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.z)(a.handleEvent,a);else throw Error("wa");return Number(b)>2147483647?-1:_.Xa.setTimeout(a,b||0)};_.tz=function(a){_.Xa.clearTimeout(a)};
var WC,XC,YC;_.VC=function(a){var b=arguments.length;if(b==1&&Array.isArray(arguments[0]))return _.VC.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};WC=function(a){return a.replace(/&([^;]+);/g,function(b,c){switch(c){case "amp":return"&";case "lt":return"<";case "gt":return">";case "quot":return'"';default:return c.charAt(0)!="#"||(c=Number("0"+c.slice(1)),isNaN(c))?b:String.fromCharCode(c)}})};XC=/&([^;\s<&]+);?/g;
YC=function(a){var b={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};var c=_.Xa.document.createElement("div");return a.replace(XC,function(d,e){var f=b[d];if(f)return f;e.charAt(0)=="#"&&(e=Number("0"+e.slice(1)),isNaN(e)||(f=String.fromCharCode(e)));f||(_.Hc(c,_.ec(d+" ")),f=c.firstChild.nodeValue.slice(0,-1));return b[d]=f})};_.ZC=function(a){return _.yc(a,"&")?"document"in _.Xa?YC(a):WC(a):a};var $C;_.VC("A AREA BUTTON HEAD INPUT LINK MENU META OPTGROUP OPTION PROGRESS STYLE SELECT SOURCE TEXTAREA TITLE TRACK".split(" "));_.aD=function(a,b){b?a.setAttribute("role",b):a.removeAttribute("role")};
_.bD=function(a,b,c){Array.isArray(c)&&(c=c.join(" "));var d="aria-"+b;c===""||c==void 0?($C||(c={},$C=(c.atomic=!1,c.autocomplete="none",c.dropeffect="none",c.haspopup=!1,c.live="off",c.multiline=!1,c.multiselectable=!1,c.orientation="vertical",c.readonly=!1,c.relevant="additions text",c.required=!1,c.sort="none",c.busy=!1,c.disabled=!1,c.hidden=!1,c.invalid="false",c)),c=$C,b in c?a.setAttribute(d,c[b]):a.removeAttribute(d)):a.setAttribute(d,c)};
_.cD=function(a,b){a=a.getAttribute("aria-"+b);return a==null||a==void 0?"":String(a)};_.dD=function(a,b){var c="";b&&(c=b.id);_.bD(a,"activedescendant",c)};
var eD,fD;eD=function(a){return typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||""};fD=function(a){return a.classList?a.classList:eD(a).match(/\S+/g)||[]};_.gD=function(a,b){typeof a.className=="string"?a.className=b:a.setAttribute&&a.setAttribute("class",b)};_.hD=function(a,b){return a.classList?a.classList.contains(b):_.tb(fD(a),b)};_.iD=function(a,b){if(a.classList)a.classList.add(b);else if(!_.hD(a,b)){var c=eD(a);_.gD(a,c+(c.length>0?" "+b:b))}};
_.jD=function(a,b){if(a.classList)Array.prototype.forEach.call(b,function(e){_.iD(a,e)});else{var c={};Array.prototype.forEach.call(fD(a),function(e){c[e]=!0});Array.prototype.forEach.call(b,function(e){c[e]=!0});b="";for(var d in c)b+=b.length>0?" "+d:d;_.gD(a,b)}};_.kD=function(a,b){a.classList?a.classList.remove(b):_.hD(a,b)&&_.gD(a,Array.prototype.filter.call(fD(a),function(c){return c!=b}).join(" "))};
_.lD=function(a,b){a.classList?Array.prototype.forEach.call(b,function(c){_.kD(a,c)}):_.gD(a,Array.prototype.filter.call(fD(a),function(c){return!_.tb(b,c)}).join(" "))};_.mD=function(a,b,c){c?_.iD(a,b):_.kD(a,b)};
_.nD=function(a){a.Do=void 0;a.Ia=function(){return a.Do?a.Do:a.Do=new a}};_.oD=function(a,b){b?a.tabIndex=0:(a.tabIndex=-1,a.removeAttribute("tabIndex"))};_.pD=function(){};_.nD(_.pD);_.pD.prototype.nda=0;_.qD=function(a){return":"+(a.nda++).toString(36)};_.sD=function(a){_.Oj.call(this);this.Eb=a||_.ae();this.sC=rD;this.Da=null;this.ob=!1;this.ma=null;this.lm=void 0;this.Kh=this.Wc=this.Gb=this.xr=null};_.eb(_.sD,_.Oj);_.sD.prototype.zba=_.pD.Ia();var rD=null;_.g=_.sD.prototype;_.g.getId=function(){return this.Da||(this.Da=_.qD(this.zba))};_.g.Me=function(a){if(this.Gb&&this.Gb.Kh){var b=this.Gb.Kh,c=this.Da;c in b&&delete b[c];_.et(this.Gb.Kh,a,this)}this.Da=a};_.g.O=function(){return this.ma};
_.g.wb=function(){this.lm||(this.lm=new _.jt(this));return this.lm};_.g.Nb=function(a){if(this==a)throw Error("Da");if(a&&this.Gb&&this.Da&&this.Gb.uu(this.Da)&&this.Gb!=a)throw Error("Da");this.Gb=a;_.sD.N.aD.call(this,a)};_.g.getParent=function(){return this.Gb};_.g.aD=function(a){if(this.Gb&&this.Gb!=a)throw Error("Ea");_.sD.N.aD.call(this,a)};_.g.Ha=function(){return this.Eb};_.g.wa=function(){this.ma=this.Eb.createElement("DIV")};_.g.va=function(a){this.Ai(a)};
_.g.Ai=function(a,b){if(this.ob)throw Error("Fa");this.ma||this.wa();a?a.insertBefore(this.ma,b||null):this.Eb.ub().body.appendChild(this.ma);this.Gb&&!this.Gb.ob||this.vc()};_.g.vc=function(){this.ob=!0;_.tD(this,function(a){!a.ob&&a.O()&&a.vc()})};_.g.Zd=function(){_.tD(this,function(a){a.ob&&a.Zd()});this.lm&&this.lm.removeAll();this.ob=!1};
_.g.ua=function(){this.ob&&this.Zd();this.lm&&(this.lm.dispose(),delete this.lm);_.tD(this,function(a){a.dispose()});this.ma&&_.re(this.ma);this.Gb=this.xr=this.ma=this.Kh=this.Wc=null;_.sD.N.ua.call(this)};_.g.Zl=_.jb(19);_.g.js=function(a){this.xr=a};_.g.qn=_.jb(20);_.g.Yj=_.jb(21);_.g.Ua=function(){return this.ma};_.g.Ap=_.jb(22);_.g.uu=function(a){if(this.Kh&&a){var b=this.Kh;a=(b!==null&&a in b?b[a]:void 0)||null}else a=null;return a};_.uD=function(a,b){return a.Wc?a.Wc[b]||null:null};
_.tD=function(a,b,c){a.Wc&&a.Wc.forEach(b,c)};_.sD.prototype.removeChild=function(a,b){if(a){var c=typeof a==="string"?a:a.getId();a=this.uu(c);if(c&&a){var d=this.Kh;c in d&&delete d[c];_.gj(this.Wc,a);b&&(a.Zd(),a.ma&&_.re(a.ma));a.Nb(null)}}if(!a)throw Error("Ha");return a};_.vD=function(a,b,c){return a.removeChild(_.uD(a,b),c)};_.sD.prototype.ne=function(a){for(var b=[];this.Wc&&this.Wc.length!=0;)b.push(_.vD(this,0,a));return b};
var GH,IH;_.FH=function(a,b){var c={},d;for(d in a)c[d]=b.call(void 0,a[d],d,a);return c};GH={};_.HH=function(a){if(GH[a])return GH[a];a=String(a);if(!GH[a]){var b=/function\s+([^\(]+)/m.exec(a);GH[a]=b?b[1]:"[Anonymous]"}return GH[a]};
IH=function(a,b){var c=[];if(_.tb(b,a))c.push("[...circular reference...]");else if(a&&b.length<50){c.push(_.HH(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){e>0&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=_.HH(f))?f:"[fn]";break;default:f=typeof f}f.length>40&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(IH(a.caller,
b))}catch(h){c.push("[exception trying to get caller]\n")}}else a?c.push("[...long stack...]"):c.push("[end]");return c.join("")};_.JH=function(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||_.JH),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=IH(a||arguments.callee.caller,[]));return b};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
_.KH=function(a,b){this.LC=[];this.IX=a;this.PQ=b||null;this.Qu=this.Kn=!1;this.qf=void 0;this.nN=this.u7=this.SE=!1;this.JD=0;this.Gb=null;this.UE=0};_.KH.prototype.cancel=function(a){if(this.Kn)this.qf instanceof _.KH&&this.qf.cancel();else{if(this.Gb){var b=this.Gb;delete this.Gb;a?b.cancel(a):(b.UE--,b.UE<=0&&b.cancel())}this.IX?this.IX.call(this.PQ,this):this.nN=!0;this.Kn||this.Ol(new _.LH(this))}};_.KH.prototype.yQ=function(a,b){this.SE=!1;MH(this,a,b)};
var MH=function(a,b,c){a.Kn=!0;a.qf=c;a.Qu=!b;NH(a)},PH=function(a){if(a.Kn){if(!a.nN)throw new OH(a);a.nN=!1}};_.KH.prototype.callback=function(a){PH(this);MH(this,!0,a)};_.KH.prototype.Ol=function(a){PH(this);MH(this,!1,a)};_.KH.prototype.ye=function(a,b){return _.QH(this,a,null,b)};_.KH.prototype.finally=function(a){var b=this;return new Promise(function(c,d){_.QH(b,function(e){a();c(e)},function(e){a();d(e)})})};
_.QH=function(a,b,c,d){var e=a.Kn;e||(b===c?b=c=(0,_.ok)(b):(b=(0,_.ok)(b),c=(0,_.ok)(c)));a.LC.push([b,c,d]);e&&NH(a);return a};_.KH.prototype.then=function(a,b,c){var d,e,f=new _.xk(function(h,k){e=h;d=k});_.QH(this,e,function(h){h instanceof _.LH?f.cancel():d(h);return RH},this);return f.then(a,b,c)};_.mk(_.KH);
var SH=function(a){return _.Jb(a.LC,function(b){return typeof b[1]==="function"})},RH={},NH=function(a){if(a.JD&&a.Kn&&SH(a)){var b=a.JD,c=TH[b];c&&(_.Xa.clearTimeout(c.Da),delete TH[b]);a.JD=0}a.Gb&&(a.Gb.UE--,delete a.Gb);b=a.qf;for(var d=c=!1;a.LC.length&&!a.SE;){var e=a.LC.shift(),f=e[0],h=e[1];e=e[2];if(f=a.Qu?h:f)try{var k=f.call(e||a.PQ,b);k===RH&&(k=void 0);k!==void 0&&(a.Qu=a.Qu&&(k==b||k instanceof Error),a.qf=b=k);if(_.nk(b)||typeof _.Xa.Promise==="function"&&b instanceof _.Xa.Promise)d=
!0,a.SE=!0}catch(l){b=l,a.Qu=!0,SH(a)||(c=!0)}}a.qf=b;d&&(k=(0,_.z)(a.yQ,a,!0),d=(0,_.z)(a.yQ,a,!1),b instanceof _.KH?(_.QH(b,k,d),b.u7=!0):b.then(k,d));c&&(b=new UH(b),TH[b.Da]=b,a.JD=b.Da)},OH=function(a){_.lb.call(this);this.QQ=a};_.eb(OH,_.lb);OH.prototype.message="Deferred has already fired";OH.prototype.name="AlreadyCalledError";_.LH=function(a){_.lb.call(this);this.QQ=a};_.eb(_.LH,_.lb);_.LH.prototype.message="Deferred was canceled";_.LH.prototype.name="CanceledError";
var UH=function(a){this.Da=_.Xa.setTimeout((0,_.z)(this.lha,this),0);this.lz=a};UH.prototype.lha=function(){delete TH[this.Da];throw this.lz;};var TH={};
_.VH=function(a){var b="Do";if(a.Do&&a.hasOwnProperty(b))return a.Do;b=new a;return a.Do=b};
_.WH=function(a){a.Fra=!0;return a};_.XH=_.WH(function(a){return typeof a==="number"});_.YH=_.WH(function(a){return typeof a==="string"});_.ZH=_.WH(function(a){return typeof a==="boolean"});
var YJ,wK,zK,CK,HK,KK,XK,eL,iL,QK,$J;_.WJ=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};YJ=function(a){return XJ[a]||""};_.bK=function(a){if(!_.ZJ)return $J(a);a=aK.test(a)?a.replace(aK,YJ):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b};_.dK=function(a){return cK&&a!=null&&a instanceof Uint8Array};
_.eK=function(a,b){var c=a.length;if(c!==b.length)return!1;for(var d=0;d<c;d++)if(a[d]!==b[d])return!1;return!0};_.fK=function(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b};_.kK=function(a,b){_.gK||_.hK in a||iK(a,jK);a[_.hK]|=b};_.lK=function(a,b){_.gK||_.hK in a||iK(a,jK);a[_.hK]=b};_.mK=function(a,b){a[_.hK]&=~b};_.nK=function(a){if(4&a)return 512&a?512:1024&a?1024:0};
_.oK=function(){return typeof BigInt==="function"};_.rK=function(a){return a[pK]===qK};_.tK=function(a,b){return b===void 0?a.uF!==sK&&!!(2&(a.V[_.hK]|0)):!!(2&b)&&a.uF!==sK};_.uK=function(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();};_.vK=function(a,b){if(typeof b!=="number"||b<0||b>a.length)throw Error();};wK=function(a){return a};
_.yK=function(a){var b=a;if((0,_.YH)(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if((0,_.XH)(b)&&!Number.isSafeInteger(b))throw Error(String(b));return xK?BigInt(a):a=(0,_.ZH)(a)?a?"1":"0":(0,_.YH)(a)?a.trim()||"0":String(a)};zK=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};CK=function(a){var b=a>>>0;_.AK=b;_.BK=(a-b)/4294967296>>>0};
_.EK=function(a){if(a<0){CK(-a);var b=_.Aa(_.DK(_.AK,_.BK));a=b.next().value;b=b.next().value;_.AK=a>>>0;_.BK=b>>>0}else CK(a)};_.GK=function(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:_.FK(a,b)};
_.FK=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else _.oK()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+HK(c)+HK(a));return c};HK=function(a){a=String(a);return"0000000".slice(a.length)+a};
_.IK=function(a){if(a.length<16)_.EK(Number(a));else if(_.oK())a=BigInt(a),_.AK=Number(a&BigInt(4294967295))>>>0,_.BK=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");_.BK=_.AK=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),_.BK*=1E6,_.AK=_.AK*1E6+d,_.AK>=4294967296&&(_.BK+=Math.trunc(_.AK/4294967296),_.BK>>>=0,_.AK>>>=0);b&&(b=_.Aa(_.DK(_.AK,_.BK)),a=b.next().value,b=b.next().value,_.AK=a,_.BK=b)}};_.DK=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};
KK=function(a,b){if(a!=null){var c;var d=(c=JK)!=null?c:JK={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),_.WJ(a,"incident"),_.sh(a))}};_.LK=function(a){return Array.prototype.slice.call(a)};_.MK=function(a){if(a!=null&&typeof a!=="boolean")throw Error("ob`"+_.jd(a)+"`"+a);return a};_.NK=function(a){return a==null||typeof a==="string"?a:void 0};
_.PK=function(a,b,c,d){if(a!=null&&typeof a==="object"&&_.rK(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[_.OK])||(a=new b,_.kK(a.V,34),a=b[_.OK]=a),b=a):b=new b:b=void 0,b;c=a[_.hK]|0;d=c|d&32|d&2;d!==c&&_.lK(a,d);return new b(a)};_.SK=function(a){var b=QK(RK);return b?a[b]:void 0};_.UK=function(a,b){var c=QK(RK),d;_.gK&&c&&((d=a[c])==null?void 0:d[b])!=null&&KK(TK,3)};
XK=function(a,b){var c=c===void 0?!1:c;if(QK(VK)&&QK(RK)&&void 0===VK){var d=a.V,e=d[RK];if(!e)return;if(e=e.FZ)try{e(d,b,WK);return}catch(f){_.sh(f)}}c&&(a=a.V,(c=QK(RK))&&c in a&&(a=a[c])&&delete a[b])};
_.$K=function(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f=[],h=a.length,k=4294967295,l=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,k=h):p=void 0;if(m&&!(b&128)&&!e){l=!0;var q;k=((q=YK)!=null?q:wK)(k-n,n,a,p)+n}}q=void 0;for(var r=0;r<h;r++){var w=a[r];if(w!=null&&(w=c(w,d))!=null)if(m&&r>=k){var u=r-n,x=void 0;((x=q)!=null?x:q={})[u]=w}else f[r]=w}if(p)for(var A in p)h=p[A],h!=null&&(h=c(h,d))!=null&&(r=+A,w=void 0,
m&&!Number.isNaN(r)&&(w=r+n)<k?f[w]=h:(r=void 0,((r=q)!=null?r:q={})[A]=h));q&&(l?f.push(q):f[k]=q);e&&(_.lK(f,b&16761025|34),QK(RK)&&(a=_.SK(a))&&"function"==typeof _.ZK&&a instanceof _.ZK&&(f[RK]=a.V7()));return f};
_.bL=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(0,_.aL)(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[_.hK]|0;return a.length===0&&b&1?void 0:_.$K(a,b,_.bL)}if(_.rK(a))return _.cL(a);if("function"==typeof _.dL&&a instanceof _.dL)return a.KE();return}return a};_.cL=function(a){a=a.V;return _.$K(a,a[_.hK]|0,_.bL)};
_.fL=function(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-16760833|(b&1023)<<14)}else{if(!Array.isArray(a))throw Error("pb");e=a[_.hK]|0;4096&e&&!(2&e)&&eL();if(e&256)throw Error("rb");if(e&64)return d!==0||e&4096||_.lK(a,e|4096),a;if(c&&(e|=128,c!==a[0]))throw Error("sb");a:{c=a;e|=64;var f=c.length;if(f){var h=f-1,k=c[h];if(k!=null&&typeof k==="object"&&k.constructor===Object){b=e&128?0:-1;h-=b;if(h>=1024)throw Error("ub");for(var l in k)f=+l,f<h&&(c[f+b]=k[l],
delete k[l]);e=e&-16760833|(h&1023)<<14;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("vb");e=e&-16760833|(l&1023)<<14}}}e|=64;d===0&&(e|=4096);_.lK(a,e);return a};eL=function(){KK(gL,5)};
iL=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[_.hK]|0;a.length===0&&c&1?a=void 0:c&2||(!b||8192&c||16&c?a=_.hL(a,c,b&&!(c&16)):(_.kK(a,34),c&4&&Object.freeze(a)));return a}if(_.rK(a))return b=a.V,c=b[_.hK]|0,_.tK(a,c)?a:_.hL(b,c);if("function"==typeof _.dL&&a instanceof _.dL)return a};_.hL=function(a,b,c){c!=null||(c=!!(34&b));return _.$K(a,b,iL,c,!0)};_.jL=function(a){var b=a.V,c=b[_.hK]|0;if(!_.tK(a,c))return a;a=new a.constructor(_.hL(b,c));_.mK(a.V,2);return a};
_.kL=function(a){if(a.uF!==sK)return!1;var b=a.V;b=_.hL(b,b[_.hK]|0);_.mK(b,2);a.V=b;a.uF=void 0;return!0};_.lL=function(a){if(!_.kL(a)&&_.tK(a,a.V[_.hK]|0))throw Error();};_.mL=function(a,b,c,d,e){var f=c+(e?0:-1),h=a.length-1;if(h>=1+(e?0:-1)&&f>=h){var k=a[h];if(k!=null&&typeof k==="object"&&k.constructor===Object)return k[c]=d,b}if(f<=h)return a[f]=d,b;if(d!==void 0){var l;h=((l=b)!=null?l:b=a[_.hK]|0)>>14&1023||536870912;c>=h?d!=null&&(f={},a[h+(e?0:-1)]=(f[c]=d,f)):a[f]=d}return b};
_.pL=function(a,b,c,d,e,f,h,k,l){var m=b;h===1||(h!==4?0:2&b||!(16&b)&&32&d)?_.nL(b)||(b|=!a.length||k&&!(8192&b)||32&d&&!(8192&b||16&b)?2:256,b!==m&&_.lK(a,b),Object.freeze(a)):(h===2&&_.nL(b)&&(a=_.LK(a),m=0,b=_.oL(b,d),_.mL(c,d,e,a,f)),_.nL(b)||(l||(b|=16),b!==m&&_.lK(a,b)));return a};_.sL=function(a,b,c){a=_.qL(a,b,c);return Array.isArray(a)?a:_.rL};_.tL=function(a,b){2&b&&(a|=2);return a|1};_.nL=function(a){return!!(2&a)&&!!(4&a)||!!(256&a)};
_.uL=function(a,b,c,d){_.lL(a);var e=a.V;_.mL(e,e[_.hK]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a};_.vL=function(a,b,c,d,e){a=_.qL(a,d,e,function(f){return _.PK(f,c,!1,b)});if(a!=null)return a};
_.wL=function(a,b,c,d,e,f,h,k,l){var m=_.tK(a,c);f=m?1:f;k=!!k||f===3;m=l&&!m;(f===2||m)&&_.kL(a)&&(b=a.V,c=b[_.hK]|0);a=_.sL(b,e,h);var n=a===_.rL?7:a[_.hK]|0,p=_.tL(n,c);if(l=!(4&p)){var q=a,r=c,w=!!(2&p);w&&(r|=2);for(var u=!w,x=!0,A=0,D=0;A<q.length;A++){var E=_.PK(q[A],d,!1,r);if(E instanceof d){if(!w){var N=_.tK(E);u&&(u=!N);x&&(x=N)}q[D++]=E}}D<A&&(q.length=D);p|=4;p=x?p&-8193:p|8192;p=u?p|8:p&-9}p!==n&&(_.lK(a,p),2&p&&Object.freeze(a));if(m&&!(8&p||!a.length&&(f===1||(f!==4?0:2&p||!(16&p)&&
32&c)))){_.nL(p)&&(a=_.LK(a),p=_.oL(p,c),c=_.mL(b,c,e,a,h));d=a;m=p;for(n=0;n<d.length;n++)q=d[n],p=_.jL(q),q!==p&&(d[n]=p);m|=8;p=m=d.length?m|8192:m&-8193;_.lK(a,p)}return a=_.pL(a,p,b,c,e,h,f,l,k)};_.oL=function(a,b){return a=(2&b?a|2:a&-3)&-273};
_.xL=function(a,b,c,d,e,f,h,k){_.lL(a);var l=a.V;a=_.wL(a,l,l[_.hK]|0,c,b,2,d,!0);h&&k?(f!=null||(f=a.length-1),_.uK(a,f),a.splice(f,h),a.length||_.mK(a,8192)):(h?_.vK(a,f):e=e!=null?e:new c,f!=void 0?a.splice(f,h,e):a.push(e),f=c=a===_.rL?7:a[_.hK]|0,_.tK(e)?(c&=-9,a.length===1&&(c&=-8193)):c|=8192,c!==f&&_.lK(a,c))};QK=function(a){return a};
$J=function(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):_.yc("=.",a[b-1])&&(c=_.yc("=.",a[b-2])?c-2:c-1);var d=new Uint8Array(c),e=0;_.Oh(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d};var cK,aK,XJ;cK=typeof Uint8Array!=="undefined";_.ZJ=!_.yd&&typeof btoa==="function";aK=/[-_.]/g;XJ={"-":"+",_:"/",".":"="};var yL,RK,TK,gL,pK,VK;_.gK=typeof Symbol==="function"&&typeof Symbol()==="symbol";yL=_.fK("jas",void 0,!0);_.OK=_.fK(void 0,"0di");RK=_.fK(void 0,Symbol());TK=_.fK(void 0,"0ub");gL=_.fK(void 0,"0actk");pK=_.fK("m_m","Ura",!0);VK=_.fK();var jK,iK,zL;jK={Uba:{value:0,configurable:!0,writable:!0,enumerable:!1}};iK=Object.defineProperties;_.hK=_.gK?yL:"Uba";zL=[];_.lK(zL,7);_.rL=Object.freeze(zL);var qK,sK;qK={};sK={};_.AL=Object.freeze({});var xK=typeof _.Xa.BigInt==="function"&&typeof _.Xa.BigInt(0)==="bigint";var DL,BL,EL,CL;_.aL=_.WH(function(a){return xK?a>=BL&&a<=CL:a[0]==="-"?zK(a,DL):zK(a,EL)});DL=Number.MIN_SAFE_INTEGER.toString();BL=xK?BigInt(Number.MIN_SAFE_INTEGER):void 0;EL=Number.MAX_SAFE_INTEGER.toString();CL=xK?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.AK=0;_.BK=0;var JK=void 0;_.FL=typeof BigInt==="function"?BigInt.asIntN:void 0;_.GL=typeof BigInt==="function"?BigInt.asUintN:void 0;_.HL=Number.isSafeInteger;_.IL=Number.isFinite;_.JL=Math.trunc;var WK={Dsa:!0};var YK;_.KL=_.yK(0);_.LL=function(a,b,c){return _.qL(a.V,b,c)};_.qL=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var h=a[f];if(h!=null&&typeof h==="object"&&h.constructor===Object){c=h[b];var k=!0}else if(e===f)c=h;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return k?h[b]=d:a[e]=d,d}return c}};_.ML=function(a,b,c,d){a=a.V;return _.vL(a,a[_.hK]|0,b,c,d)!==void 0};_.NL=function(a,b,c){this.V=_.fL(a,b,c)};_.NL.prototype.toJSON=function(){return _.cL(this)};_.OL=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("Fb");_.kK(b,32);return new a(b)};_.NL.prototype.getExtension=function(a){_.UK(this.V,a.Qh);XK(this,a.Qh);return a.ctor?a.Kz(this,a.ctor,a.Qh,a.Vu):a.Kz(this,a.Qh,a.defaultValue,a.Vu)};
_.PL=function(a,b){_.UK(a.V,b.Qh);XK(a,b.Qh);a=b.ctor?b.Kz(a,b.ctor,b.Qh,b.Vu):b.Kz(a,b.Qh,null,b.Vu);return a===null?void 0:a};_.NL.prototype.hasExtension=function(a){_.UK(this.V,a.Qh);XK(this,a.Qh);return a.ctor?_.ML(this,a.ctor,a.Qh,a.Vu):_.PL(this,a)!==void 0};_.NL.prototype.clone=function(){var a=this,b=a.V;a=new a.constructor(_.hL(b,b[_.hK]|0));_.mK(a.V,2);return a};_.NL.prototype[pK]=qK;_.NL.prototype.toString=function(){return this.V.toString()};
var iN,lN,qN,rN,sN,tN,xN,yN,JN,MN,NN,ON,QN,RN,UN,EN,mN,qO;_.hN=function(a){var b=a.V,c=b[_.hK]|0;return _.tK(a,c)?a:new a.constructor(_.hL(b,c))};iN=function(){var a=_.AK,b=_.BK;b&2147483648?_.oK()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=_.Aa(_.DK(a,b)),a=b.next().value,b=b.next().value,a="-"+_.FK(a,b)):a=_.FK(a,b);return a};_.jN=function(a){a=Error(a);_.WJ(a,"warning");return a};_.kN=function(a){if(a!=null&&typeof a!=="number")throw Error("nb`"+typeof a+"`"+a);return a};
lN=function(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)};_.nN=function(a){switch(typeof a){case "bigint":return!0;case "number":return(0,_.IL)(a);case "string":return mN.test(a);default:return!1}};_.oN=function(a){if(!(0,_.IL)(a))throw _.jN("enum");return a|0};_.pN=function(a){if(typeof a!=="number")throw _.jN("int32");if(!(0,_.IL)(a))throw _.jN("int32");return a|0};
qN=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.IL)(a)?a|0:void 0};rN=function(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337};sN=function(a){if(rN(a))return a;_.IK(a);return iN()};tN=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b))return _.yK(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return _.oK()?_.yK((0,_.FL)(64,BigInt(a))):_.yK(sN(a))};
_.uN=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return sN(a)};_.vN=function(a){a=(0,_.JL)(a);if(!(0,_.HL)(a)){_.EK(a);var b=_.AK,c=_.BK;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=_.GK(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a};_.wN=function(a){a=(0,_.JL)(a);if((0,_.HL)(a))a=String(a);else{var b=String(a);rN(b)?a=b:(_.EK(a),a=iN())}return a};xN=function(a){return(0,_.HL)(a)?_.yK(_.vN(a)):_.yK(_.wN(a))};
yN=function(a,b){b=b===void 0?0:b;if(!_.nN(a))throw _.jN("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return _.uN(a);case "bigint":return String((0,_.FL)(64,a));default:return _.wN(a)}case 1024:switch(c){case "string":return tN(a);case "bigint":return _.yK((0,_.FL)(64,a));default:return xN(a)}case 0:switch(c){case "string":return _.uN(a);case "bigint":return _.yK((0,_.FL)(64,a));default:return _.vN(a)}default:return _.rb(b,"Unknown format requested type for int64")}};
_.zN=function(a,b){return a==null?a:yN(a,b===void 0?0:b)};_.AN=function(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return _.yK((0,_.FL)(64,a));if(_.nN(a))return b==="string"?tN(a):xN(a)};_.BN=function(a){if(typeof a!=="string")throw Error();return a};_.CN=function(a){if(a!=null&&typeof a!=="string")throw Error();return a};
_.DN=function(a,b,c,d,e){_.lL(a);var f=a.V,h=f[_.hK]|0;if(c==null)return _.mL(f,h,b,void 0,e),a;var k=c===_.rL?7:c[_.hK]|0,l=k,m=_.nL(k),n=m||Object.isFrozen(c);m||(k=0);n||(c=_.LK(c),l=0,k=_.oL(k,h),n=!1);k|=5;var p;m=(p=_.nK(k))!=null?p:0;for(p=0;p<c.length;p++){var q=c[p],r=d(q,m);Object.is(q,r)||(n&&(c=_.LK(c),l=0,k=_.oL(k,h),n=!1),c[p]=r)}k!==l&&(n&&(c=_.LK(c),k=_.oL(k,h)),_.lK(c,k));_.mL(f,h,b,c,e);return a};
_.FN=function(a){if(_.gK){var b;return(b=a[EN])!=null?b:a[EN]=new Map}if(EN in a)return a[EN];b=new Map;Object.defineProperty(a,EN,{value:b});return b};_.GN=function(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var h=d[f];_.qL(b,h,void 0)!=null&&(e!==0&&(c=_.mL(b,c,e,void 0,void 0)),e=h)}a.set(d,e);return e};_.HN=function(a){return a===_.AL?2:4};_.IN=function(a){return JSON.stringify(_.cL(a))};
JN=function(){this.tI=!1;this.nn=null;this.Vj=void 0;this.Pc=1;this.Jn=this.wn=0;this.BS=this.Bh=null};_.g=JN.prototype;_.g.Pw=function(){if(this.tI)throw new TypeError("Generator is already running");this.tI=!0};_.g.Hp=function(){this.tI=!1};_.g.Lv=function(a){this.Vj=a};_.g.bx=function(a){this.Bh={dS:a,QV:!0};this.Pc=this.wn||this.Jn};_.g.return=function(a){this.Bh={return:a};this.Pc=this.Jn};_.KN=function(a,b,c){a.Pc=c;return{value:b}};JN.prototype.Wg=function(a){this.Pc=a};
_.LN=function(a){a.wn=0;var b=a.Bh.dS;a.Bh=null;return b};MN=function(a){this.qb=new JN;this.wea=a};MN.prototype.Lv=function(a){this.qb.Pw();if(this.qb.nn)return NN(this,this.qb.nn.next,a,this.qb.Lv);this.qb.Lv(a);return ON(this)};var PN=function(a,b){a.qb.Pw();var c=a.qb.nn;if(c)return NN(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.qb.return);a.qb.return(b);return ON(a)};
MN.prototype.bx=function(a){this.qb.Pw();if(this.qb.nn)return NN(this,this.qb.nn["throw"],a,this.qb.Lv);this.qb.bx(a);return ON(this)};NN=function(a,b,c,d){try{var e=b.call(a.qb.nn,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.qb.Hp(),e;var f=e.value}catch(h){return a.qb.nn=null,a.qb.bx(h),ON(a)}a.qb.nn=null;d.call(a.qb,f);return ON(a)};
ON=function(a){for(;a.qb.Pc;)try{var b=a.wea(a.qb);if(b)return a.qb.Hp(),{value:b.value,done:!1}}catch(c){a.qb.Vj=void 0,a.qb.bx(c)}a.qb.Hp();if(a.qb.Bh){b=a.qb.Bh;a.qb.Bh=null;if(b.QV)throw b.dS;return{value:b.return,done:!0}}return{value:void 0,done:!0}};QN=function(a){this.next=function(b){return a.Lv(b)};this.throw=function(b){return a.bx(b)};this.return=function(b){return PN(a,b)};this[Symbol.iterator]=function(){return this}};
RN=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(h){h.done?d(h.value):Promise.resolve(h.value).then(b,c).then(f,e)}f(a.next())})};_.SN=function(a){return RN(new QN(new MN(a)))};_.TN={};EN=_.fK(void 0,"1oa");_.C={};mN=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;_.VN=function(a,b,c,d){_.lL(a);var e=a.V;_.mL(e,e[_.hK]|0,b,c,d);return a};_.WN=function(a,b){return _.qL(a.V,b,void 0,lN)};
_.XN=function(a,b){a=a.V;return _.GN(_.FN(a),a,void 0,b)};_.YN=function(a,b,c){return _.XN(a,b)===c?c:-1};_.ZN=function(a,b,c,d){var e=a.V,f=e[_.hK]|0;b=_.vL(e,f,b,c,d);if(b==null)return b;f=e[_.hK]|0;if(!_.tK(a,f)){var h=_.jL(b);h!==b&&(_.kL(a)&&(e=a.V,f=e[_.hK]|0),b=h,_.mL(e,f,c,b,d))}return b};_.$N=function(a,b,c,d,e,f){_.xL(a,b,c,f,e,d,1);return a};_.aO=function(a,b,c,d,e){var f=a.V;return _.wL(a,f,f[_.hK]|0,b,c,d,e,!1,!0)};_.bO=function(a,b,c,d){c==null&&(c=void 0);_.VN(a,b,c,d);return a};
_.cO=function(a,b,c,d){_.lL(a);var e=a.V,f=e[_.hK]|0;if(c==null)return _.mL(e,f,b,void 0,d),a;for(var h=c===_.rL?7:c[_.hK]|0,k=h,l=_.nL(h),m=l||Object.isFrozen(c),n=!0,p=!0,q=0;q<c.length;q++){var r=c[q];l||(r=_.tK(r),n&&(n=!r),p&&(p=r))}l||(h=n?13:5,h=p?h&-8193:h|8192);m&&h===k||(c=_.LK(c),k=0,h=_.oL(h,f));h!==k&&_.lK(c,h);_.mL(e,f,b,c,d);return a};
_.dO=function(a,b){a=_.LL(a,b);a!=null&&(typeof a==="bigint"?(0,_.aL)(a)?a=Number(a):(a=(0,_.FL)(64,a),a=(0,_.aL)(a)?Number(a):String(a)):a=_.nN(a)?typeof a==="number"?_.vN(a):_.uN(a):void 0);return a};_.eO=function(a,b,c){return qN(_.LL(a,b,c))};_.fO=function(a,b,c){return _.NK(_.LL(a,b,c))};_.gO=function(a,b,c){a=_.LL(a,b,c);return a==null?a:(0,_.IL)(a)?a|0:void 0};_.hO=function(a,b,c){c=c===void 0?0:c;var d;return(d=_.eO(a,b))!=null?d:c};
_.iO=function(a,b){var c=c===void 0?"":c;var d;return(d=_.fO(a,b))!=null?d:c};_.jO=function(a,b){var c=c===void 0?0:c;var d;return(d=_.gO(a,b))!=null?d:c};_.kO=function(a,b,c,d){return _.VN(a,b,_.MK(c),d)};_.lO=function(a,b,c,d){return _.VN(a,b,c==null?c:_.pN(c),d)};_.mO=function(a,b,c,d,e){return _.VN(a,b,_.zN(c,d===void 0?0:d),e)};_.nO=function(a,b,c,d){return _.VN(a,b,_.CN(c),d)};_.oO=function(a,b,c,d){return _.VN(a,b,c==null?c:_.oN(c),d)};
_.dL=function(a,b){if(b!==_.TN)throw Error("mb");this.La=a;if(a!=null&&a.length===0)throw Error("lb");};_.pO=function(){return UN||(UN=new _.dL(null,_.TN))};_.dL.prototype.KE=function(){var a=this.La;if(a==null)a="";else if(typeof a!=="string"){if(_.ZJ){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else a=_.Nh(a);a=this.La=a}return a};
_.dL.prototype.isEmpty=function(){return this.La==null};_.dL.prototype.gta=function(){var a=qO(this);return a?a.length:0};_.rO=function(a,b){if(!a.La||!b.La||a.La===b.La)return a.La===b.La;if(typeof a.La==="string"&&typeof b.La==="string"){var c=a.La,d=b.La;b.La.length>a.La.length&&(d=a.La,c=b.La);if(c.lastIndexOf(d,0)!==0)return!1;for(b=d.length;b<c.length;b++)if(c[b]!=="=")return!1;return!0}c=qO(a);b=qO(b);return _.eK(c,b)};
qO=function(a){if(_.TN!==_.TN)throw Error("mb");var b=a.La;b==null||_.dK(b)||(typeof b==="string"?b=_.bK(b):(_.jd(b),b=null));return b==null?b:a.La=b};_.dL.prototype.DV=function(a){if(typeof a==="string")a=a?new _.dL(a,_.TN):_.pO();else if(a instanceof Uint8Array)a=new _.dL(a,_.TN);else if(!(a instanceof _.dL))return!1;return _.rO(this,a)};
var PO,RO,SO,TO,UO,WO,VO,XO;
PO=function(a){var b=_.Jc();if(a==="Internet Explorer")return _.Qc()?_.Yc(b):"";b=_.Nc(b);var c=_.Xc(b);switch(a){case "Opera":if(_.Pc())return c(["Version","Opera"]);if(_.Uc())return c(["OPR"]);break;case "Microsoft Edge":if(_.Sc())return c(["Edge"]);if(_.Tc())return c(["Edg"]);break;case "Chromium":if(_.Wc())return c(["Chrome","CriOS","HeadlessChrome"])}return a==="Firefox"&&_.Vc()||a==="Safari"&&_.vh()||a==="Android Browser"&&_.wh()||a==="Silk"&&_.Mc("Silk")?(a=b[2])&&a[1]||"":""};
_.QO=function(a){if(_.Oc()&&a!=="Silk"){var b=_.Kc.brands.find(function(c){return c.brand===a});if(!b||!b.version)return NaN;b=b.version.split(".")}else{b=PO(a);if(b==="")return NaN;b=b.split(".")}return b.length===0?NaN:Number(b[0])};RO=function(a){return!Array.isArray(a)||a.length?!1:(a[_.hK]|0)&1?!0:!1};SO=function(a,b){if(typeof b==="string")try{b=_.bK(b)}catch(c){return!1}return _.dK(b)&&_.eK(a,b)};TO=function(a){switch(a){case "bigint":case "string":case "number":return!0;default:return!1}};
UO=function(a,b,c,d,e){var f;return(f=a<d?b[a+e]:void 0)!=null?f:c==null?void 0:c[a]};WO=function(a,b){if(_.rK(a))a=a.V;else if(!Array.isArray(a))return!1;if(_.rK(b))b=b.V;else if(!Array.isArray(b))return!1;return VO(a,b,void 0,2)};
VO=function(a,b,c,d){if(a===b||a==null&&b==null)return!0;if(a instanceof Map)return a.Wba(b,c);if(b instanceof Map)return b.Wba(a,c);if(a==null||b==null)return!1;if("function"==typeof _.dL&&a instanceof _.dL)return a.DV(b);if("function"==typeof _.dL&&b instanceof _.dL)return b.DV(a);if(_.dK(a))return SO(a,b);if(_.dK(b))return SO(b,a);var e=typeof a,f=typeof b;if(e!=="object"||f!=="object")return Number.isNaN(a)||Number.isNaN(b)?String(a)===String(b):TO(e)&&TO(f)?""+a===""+b:e==="boolean"&&f==="number"||
e==="number"&&f==="boolean"?!a===!b:!1;if(_.rK(a)||_.rK(b))return WO(a,b);if(a.constructor!=b.constructor)return!1;if(a.constructor===Array){f=a[_.hK]|0;var h=b[_.hK]|0,k=a.length,l=b.length,m=Math.max(k,l);e=(f|h|64)&128?0:-1;(d===1||(f|h)&1)&&(d=1);f=k&&a[k-1];h=l&&b[l-1];f!=null&&typeof f==="object"&&f.constructor===Object||(f=null);h!=null&&typeof h==="object"&&h.constructor===Object||(h=null);k=k-e-+!!f;l=l-e-+!!h;for(var n=0;n<m;n++)if(!XO(n-e,a,f,k,b,h,l,e,c,d))return!1;if(f)for(var p in f){d=
a;m=f;n=k;var q=b,r=h,w=l,u=e,x=c,A=+p;if(!(!Number.isFinite(A)||A<n||A<w||XO(A,d,m,n,q,r,w,u,x,2)))return!1}if(h)for(var D in h)if((p=f&&D in f)||(p=a,d=f,m=k,n=b,q=h,r=l,w=e,u=c,x=+D,p=!Number.isFinite(x)||x<m||x<r?!0:XO(x,p,d,m,n,q,r,w,u,2)),!p)return!1;return!0}if(a.constructor===Object)return VO([a],[b],void 0,0);throw Error();};XO=function(a,b,c,d,e,f,h,k,l,m){b=UO(a,b,c,d,k);e=UO(a,e,f,h,k);m=m===1;if(e==null&&RO(b)||b==null&&RO(e))return!0;a=m?l:l==null?void 0:l.Tqa(a);return VO(b,e,a,0)};
_.YO=function(a){return(0,_.aL)(a)?Number(a):String(a)};_.F=function(a,b,c){a=_.LL(a,b,c);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0};_.ZO=function(a,b,c,d){c=c===void 0?!1:c;var e;return(e=_.F(a,b,d))!=null?e:c};_.$O=function(a,b){return a===b||a==null&&b==null||!(!a||!b)&&a instanceof b.constructor&&WO(a,b)};
var xP,yP;xP=function(a){if(a[0]==="-")return!1;var b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467};yP=function(a){if(a<0){_.EK(a);var b=_.FK(_.AK,_.BK);a=Number(b);return(0,_.HL)(a)?a:b}b=String(a);if(xP(b))return b;_.EK(a);return _.GK(_.AK,_.BK)};_.zP=function(a){if(xP(a))return a;_.IK(a);return _.FK(_.AK,_.BK)};_.AP=function(a){a=(0,_.JL)(a);return a>=0&&(0,_.HL)(a)?a:yP(a)};
_.BP=function(a){a=(0,_.JL)(a);if(a>=0&&(0,_.HL)(a))a=String(a);else{var b=String(a);xP(b)?a=b:(_.EK(a),a=_.FK(_.AK,_.BK))}return a};_.CP=function(a){return(0,_.HL)(a)?_.yK(_.AP(a)):_.yK(_.BP(a))};_.DP=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b)&&b>=0)return _.yK(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return _.oK()?_.yK((0,_.GL)(64,BigInt(a))):_.yK(_.zP(a))};
_.EP=function(a,b,c,d,e){var f=a.V,h=f[_.hK]|0;c=_.tK(a,h)?1:c;d=!!d||c===3;c===2&&_.kL(a)&&(f=a.V,h=f[_.hK]|0);a=_.sL(f,b,e);var k=a===_.rL?7:a[_.hK]|0,l=_.tL(k,h);var m=4&l?!1:!0;if(m){4&l&&(a=_.LK(a),k=0,l=_.oL(l,h),h=_.mL(f,h,b,a,e));for(var n=0,p=0;n<a.length;n++){var q=_.NK(a[n]);q!=null&&(a[p++]=q)}p<n&&(a.length=p);l=(l|4)&-513;l&=-1025;l&=-8193}l!==k&&(_.lK(a,l),2&l&&Object.freeze(a));return a=_.pL(a,l,f,h,b,e,c,m,d)};
_.FP=function(a,b,c,d,e,f){_.lL(a);b=_.EP(a,b,2,!0,e);var h;e=(h=_.nK(b===_.rL?7:b[_.hK]|0))!=null?h:0;f&&_.vK(b,d);d!=void 0?b.splice(d,f,_.BN(c,e)):b.push(_.BN(c,e));return a};_.GP=function(a){return a};_.HP=function(a,b,c,d,e){_.xL(a,b,c,e,void 0,d,1,!0);return a};_.IP=function(a,b,c,d){a=_.EP(a,b,3,!0,d);_.uK(a,c);return a[c]};_.JP=function(a,b,c){return _.uL(a,b,_.MK(c),!1)};var NP,KP,LP,MP;
_.OP=function(a,b){var c=b||{};b=c.document||document;var d=_.kc(a).toString(),e=(new _.Zd(b)).createElement("SCRIPT"),f={MZ:e,Ii:void 0},h=new _.KH(KP,f),k=null,l=c.timeout!=null?c.timeout:5E3;l>0&&(k=window.setTimeout(function(){LP(e,!0);h.Ol(new MP(1,"Timeout reached for loading script "+d))},l),f.Ii=k);e.onload=e.onreadystatechange=function(){e.readyState&&e.readyState!="loaded"&&e.readyState!="complete"||(LP(e,c.gqa||!1,k),h.callback(null))};e.onerror=function(){LP(e,!0,k);h.Ol(new MP(0,"Error while loading script "+
d))};f=c.attributes||{};_.ij(f,{type:"text/javascript",charset:"UTF-8"});_.ee(e,f);_.Hh(e,a);NP(b).appendChild(e);return h};NP=function(a){var b=(a||document).getElementsByTagName("HEAD");return b&&b.length!==0?b[0]:a.documentElement};KP=function(){if(this&&this.MZ){var a=this.MZ;a&&a.tagName=="SCRIPT"&&LP(a,!0,this.Ii)}};LP=function(a,b,c){c!=null&&_.Xa.clearTimeout(c);a.onload=function(){};a.onerror=function(){};a.onreadystatechange=function(){};b&&window.setTimeout(function(){_.re(a)},0)};
MP=function(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);_.lb.call(this,c);this.code=a};_.eb(MP,_.lb);
_.f1=function(a,b){b=b===void 0?!1:b;var c=typeof a;if(a==null)return a;if(c==="bigint")return String((0,_.FL)(64,a));if(_.nN(a))return c==="string"?_.uN(a):b?_.wN(a):_.vN(a)};_.g1=function(a){return function(b){return _.OL(a,b)}};_.h1=function(a,b,c){c=c===void 0?_.KL:c;var d;return(d=_.AN(_.LL(a,b)))!=null?d:c};
var Xca,Wca;Xca=function(a,b){var c=Wca;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};Wca={};_.s4=function(a){return Xca(a,function(){return _.Dc(_.Vd,a)>=0})};
var z4;_.t4=function(a,b){a.src=_.kc(b).toString()};_.u4=function(a){return _.Ks(a)};_.v4=function(a){return a};_.w4=function(a,b){var c=_.As(a,b+"Left"),d=_.As(a,b+"Right"),e=_.As(a,b+"Top");a=_.As(a,b+"Bottom");return new _.ms(parseFloat(e),parseFloat(d),parseFloat(a),parseFloat(c))};_.x4=function(a,b){var c=c===void 0?0:c;var d;return(d=_.WN(a,b))!=null?d:c};_.y4=function(a,b,c,d){return _.ZN(a,b,_.YN(a,d,c),void 0)};
z4=function(a){_.Oj.call(this);this.ma=a;this.Bca=_.Dj(this.ma,"focus",this,!0);this.Cca=_.Dj(this.ma,"blur",this,!0)};_.eb(z4,_.Oj);z4.prototype.handleEvent=function(a){var b=new _.rj(a.Df);b.type=a.type=="focusin"||a.type=="focus"?"focusin":"focusout";this.dispatchEvent(b)};z4.prototype.ua=function(){z4.N.ua.call(this);_.Kj(this.Bca);_.Kj(this.Cca);delete this.ma};_.A4=function(a,b,c){_.Oj.call(this);this.target=a;this.handle=b||a;this.KI=c||new _.Qs(NaN,NaN,NaN,NaN);this.Bc=_.$d(a);this.Fa=new _.jt(this);_.fj(this,this.Fa);this.deltaY=this.deltaX=this.jl=this.fl=this.screenY=this.screenX=this.clientY=this.clientX=0;this.Oh=!0;this.Hn=!1;_.Dj(this.handle,["touchstart","mousedown"],this.E0,!1,this);this.PD=Yca};_.eb(_.A4,_.Oj);var Yca=_.Xa.document&&_.Xa.document.documentElement&&!!_.Xa.document.documentElement.setCapture&&!!_.Xa.document.releaseCapture;
_.A4.prototype.wb=function(){return this.Fa};_.B4=function(a,b){a.KI=b||new _.Qs(NaN,NaN,NaN,NaN)};_.g=_.A4.prototype;_.g.yc=function(a){this.Oh=a};_.g.ua=function(){_.A4.N.ua.call(this);_.Nj(this.handle,["touchstart","mousedown"],this.E0,!1,this);this.Fa.removeAll();this.PD&&this.Bc.releaseCapture();this.handle=this.target=null};
_.g.E0=function(a){var b=a.type=="mousedown";if(!this.Oh||this.Hn||b&&(a.Df.button!=0||_.Ed&&a.ctrlKey))this.dispatchEvent("earlycancel");else if(this.dispatchEvent(new C4("start",this,a.clientX,a.clientY,a))){this.Hn=!0;b&&a.preventDefault();b=this.Bc;var c=b.documentElement,d=!this.PD;this.Fa.na(b,["touchmove","mousemove"],this.Aaa,{capture:d,passive:!1});this.Fa.na(b,["touchend","mouseup"],this.kz,d);this.PD?(c.setCapture(!1),this.Fa.na(c,"losecapture",this.kz)):this.Fa.na(_.ie(b),"blur",this.kz);
this.vfa&&this.Fa.na(this.vfa,"scroll",this.Rv,d);this.clientX=this.fl=a.clientX;this.clientY=this.jl=a.clientY;this.screenX=a.screenX;this.screenY=a.screenY;this.deltaX=this.target.offsetLeft;this.deltaY=this.target.offsetTop;this.OJ=_.ss(_.ae(this.Bc))}};_.g.kz=function(a,b){this.Fa.removeAll();this.PD&&this.Bc.releaseCapture();this.Hn?(this.Hn=!1,this.dispatchEvent(new C4("end",this,a.clientX,a.clientY,a,D4(this,this.deltaX),E4(this,this.deltaY),b||a.type=="touchcancel"))):this.dispatchEvent("earlycancel")};
_.g.Aaa=function(a){if(this.Oh){var b=a.clientX-this.clientX,c=a.clientY-this.clientY;this.clientX=a.clientX;this.clientY=a.clientY;this.screenX=a.screenX;this.screenY=a.screenY;if(!this.Hn){var d=this.fl-this.clientX,e=this.jl-this.clientY;if(d*d+e*e>0)if(this.dispatchEvent(new C4("start",this,a.clientX,a.clientY,a)))this.Hn=!0;else{this.isDisposed()||this.kz(a);return}}c=F4(this,b,c);b=c.x;c=c.y;this.Hn&&this.dispatchEvent(new C4("beforedrag",this,a.clientX,a.clientY,a,b,c))&&(G4(this,a,b,c),a.preventDefault())}};
var F4=function(a,b,c){var d=_.ss(_.ae(a.Bc));b+=d.x-a.OJ.x;c+=d.y-a.OJ.y;a.OJ=d;a.deltaX+=b;a.deltaY+=c;return new _.os(D4(a,a.deltaX),E4(a,a.deltaY))};_.A4.prototype.Rv=function(a){var b=F4(this,0,0);a.clientX=this.clientX;a.clientY=this.clientY;G4(this,a,b.x,b.y)};
var G4=function(a,b,c,d){a.target.style.left=c+"px";a.target.style.top=d+"px";a.dispatchEvent(new C4("drag",a,b.clientX,b.clientY,b,c,d))},D4=function(a,b){var c=a.KI;a=isNaN(c.left)?null:c.left;c=isNaN(c.width)?0:c.width;return Math.min(a!=null?a+c:Infinity,Math.max(a!=null?a:-Infinity,b))},E4=function(a,b){var c=a.KI;a=isNaN(c.top)?null:c.top;c=isNaN(c.height)?0:c.height;return Math.min(a!=null?a+c:Infinity,Math.max(a!=null?a:-Infinity,b))},C4=function(a,b,c,d,e,f,h){_.qj.call(this,a);this.clientX=
c;this.clientY=d;this.VE=e;this.left=f!==void 0?f:b.deltaX;this.top=h!==void 0?h:b.deltaY};_.eb(C4,_.qj);var H4=function(a){this.Sa=new Map;var b=arguments.length;if(b>1){if(b%2)throw Error("i");for(var c=0;c<b;c+=2)this.set(arguments[c],arguments[c+1])}else a&&this.addAll(a)};_.g=H4.prototype;_.g.Zb=function(){return this.Sa.size};_.g.Ye=function(){return Array.from(this.Sa.values())};_.g.lg=function(){return Array.from(this.Sa.keys())};_.g.Fl=function(a){return this.Sa.has(a)};
_.g.equals=function(a,b){var c=this;b=b===void 0?function(d,e){return d===e}:b;return this===a?!0:this.Sa.size!=a.Zb()?!1:this.lg().every(function(d){return b(c.Sa.get(d),a.get(d))})};_.g.isEmpty=function(){return this.Sa.size==0};_.g.clear=function(){this.Sa.clear()};_.g.remove=function(a){return this.Sa.delete(a)};_.g.get=function(a,b){return this.Sa.has(a)?this.Sa.get(a):b};_.g.set=function(a,b){this.Sa.set(a,b);return this};
_.g.addAll=function(a){if(a instanceof H4){a=_.Aa(a.Sa);for(var b=a.next();!b.done;b=a.next()){var c=_.Aa(b.value);b=c.next().value;c=c.next().value;this.Sa.set(b,c)}}else if(a)for(a=_.Aa(Object.entries(a)),b=a.next();!b.done;b=a.next())c=_.Aa(b.value),b=c.next().value,c=c.next().value,this.Sa.set(b,c)};_.g.forEach=function(a,b){var c=this;b=b===void 0?this:b;this.Sa.forEach(function(d,e){return a.call(b,d,e,c)})};_.g.clone=function(){return new H4(this)};(function(){for(var a=["ms","moz","webkit","o"],b,c=0;b=a[c]&&!_.Xa.requestAnimationFrame;++c)_.Xa.requestAnimationFrame=_.Xa[b+"RequestAnimationFrame"],_.Xa.cancelAnimationFrame=_.Xa[b+"CancelAnimationFrame"]||_.Xa[b+"CancelRequestAnimationFrame"];if(!_.Xa.requestAnimationFrame){var d=0;_.Xa.requestAnimationFrame=function(e){var f=(new Date).getTime(),h=Math.max(0,16-(f-d));d=f+h;return _.Xa.setTimeout(function(){e(f+h)},h)};_.Xa.cancelAnimationFrame||(_.Xa.cancelAnimationFrame=function(e){clearTimeout(e)})}})();
var I4=[[],[]],J4=0,K4=!1,Zca=0,L4=function(a,b){var c=Zca++,d={Zca:{id:c,Rh:a.measure,context:b},hda:{id:c,Rh:a.lX,context:b},state:{},vd:void 0,WA:!1};return function(){arguments.length>0?(d.vd||(d.vd=[]),d.vd.length=0,d.vd.push.apply(d.vd,arguments),d.vd.push(d.state)):d.vd&&d.vd.length!=0?(d.vd[0]=d.state,d.vd.length=1):d.vd=[d.state];d.WA||(d.WA=!0,I4[J4].push(d));K4||(K4=!0,window.requestAnimationFrame($ca))}},$ca=function(){K4=!1;var a=I4[J4],b=a.length;J4=(J4+1)%2;for(var c,d=0;d<b;++d){c=
a[d];var e=c.Zca;c.WA=!1;e.Rh&&e.Rh.apply(e.context,c.vd)}for(d=0;d<b;++d)c=a[d],e=c.hda,c.WA=!1,e.Rh&&e.Rh.apply(e.context,c.vd),c.state={};a.length=0};var ada=function(a,b){this.ma=a;this.Eb=b};var M4=function(a,b){_.sD.call(this,b);this.Cha=!!a;this.lr=null;this.wZ=L4({lX:this.Hj},this);L4({lX:this.D1},this)};_.eb(M4,_.sD);_.g=M4.prototype;_.g.rG=null;_.g.kc=!1;_.g.Ya=null;_.g.lb=null;_.g.qh=null;_.g.OE=!1;_.g.Cb=function(){return"shr-a-shr-ne"};_.g.Ul=function(){return this.Ya};
_.g.wa=function(){M4.N.wa.call(this);var a=this.O(),b=_.zc(this.Cb()).split(" ");_.jD(a,b);_.oD(a,!0);_.Ls(a,!1);this.Cha&&!this.lb&&(a=this.Ha().wa("IFRAME",{frameborder:0,style:"border:0;vertical-align:bottom;"}),_.t4(a,_.Ft),this.lb=a,this.lb.className=this.Cb()+"-shr-cc",_.Ls(this.lb,!1),_.Ts(this.lb,0));this.Ya||(this.Ya=this.Ha().wa("DIV",this.Cb()+"-shr-cc"),_.Ls(this.Ya,!1));this.qh||(this.qh=this.Ha().createElement("SPAN"),_.Ls(this.qh,!1),_.oD(this.qh,!0),this.qh.style.position="absolute")};
_.g.vZ=function(){this.OE=!1};_.g.vc=function(){this.lb&&_.qe(this.lb,this.O());_.qe(this.Ya,this.O());M4.N.vc.call(this);_.gt(this.qh,this.O());this.rG=new z4(this.Ha().ub());this.wb().na(this.rG,"focusin",this.yJ);N4(this,!1)};_.g.Zd=function(){this.isVisible()&&this.setVisible(!1);_.cj(this.rG);M4.N.Zd.call(this);_.re(this.lb);_.re(this.Ya);_.re(this.qh)};
_.g.setVisible=function(a){a!=this.kc&&(this.Wo&&this.Wo.stop(),this.bq&&this.bq.stop(),this.Vo&&this.Vo.stop(),this.Zp&&this.Zp.stop(),this.ob&&N4(this,a),a?this.lN():this.ev())};var N4=function(a,b){a.hX||(a.hX=new ada(a.ma,a.Eb));a=a.hX;if(b){a.Wq||(a.Wq=[]);b=a.Eb.EG(a.Eb.ub().body);for(var c=0;c<b.length;c++){var d=b[c];d==a.ma||_.cD(d,"hidden")||(_.bD(d,"hidden",!0),a.Wq.push(d))}}else if(a.Wq){for(b=0;b<a.Wq.length;b++)a.Wq[b].removeAttribute("aria-hidden");a.Wq=null}};
M4.prototype.lN=function(){if(this.dispatchEvent("beforeshow")){try{this.lr=this.Ha().ub().activeElement}catch(a){}this.D1();this.wb().na(this.Ha().getWindow(),"resize",this.Hj).na(this.Ha().getWindow(),"orientationchange",this.wZ);O4(this,!0);this.focus();this.kc=!0;this.Wo&&this.bq?(_.Cj(this.Wo,"end",this.CB,!1,this),this.bq.play(),this.Wo.play()):this.CB()}};M4.prototype.D1=function(){this.Hj();this.Ie()};
M4.prototype.ev=function(){if(this.dispatchEvent("beforehide")){this.wb().Ac(this.Ha().getWindow(),"resize",this.Hj).Ac(this.Ha().getWindow(),"orientationchange",this.wZ);this.kc=!1;this.Vo&&this.Zp?(_.Cj(this.Vo,"end",this.Pv,!1,this),this.Zp.play(),this.Vo.play()):this.Pv();a:{try{var a=this.Ha(),b=a.ub().body,c=a.ub().activeElement||b;if(!this.lr||this.lr==b){this.lr=null;break a}(c==b||a.contains(this.O(),c))&&this.lr.focus()}catch(d){}this.lr=null}}};
var O4=function(a,b){a.lb&&_.Ls(a.lb,b);a.Ya&&_.Ls(a.Ya,b);_.Ls(a.O(),b);_.Ls(a.qh,b)};_.g=M4.prototype;_.g.CB=function(){this.dispatchEvent("show")};_.g.Pv=function(){O4(this,!1);this.dispatchEvent("hide")};_.g.isVisible=function(){return this.kc};_.g.focus=function(){this.NS()};
_.g.Hj=function(){this.lb&&_.Ls(this.lb,!1);this.Ya&&_.Ls(this.Ya,!1);var a=this.Ha().ub();var b=P4(this);var c=Math.max(b.width,Math.max(a.body.scrollWidth,a.documentElement.scrollWidth));a=Math.max(b.height,Math.max(a.body.scrollHeight,a.documentElement.scrollHeight));this.lb&&(_.Ls(this.lb,!0),_.Js(this.lb,c,a));this.Ya&&(_.Ls(this.Ya,!0),_.Js(this.Ya,c,a))};
_.g.Ie=function(){var a;if(_.Rs(this.O())=="fixed")var b=a=0;else b=_.ss(this.Ha()),a=b.x,b=b.y;var c=_.u4(this.O());var d=P4(this);a=Math.max(a+d.width/2-c.width/2,0);b=Math.max(b+d.height/2-c.height/2,0);_.Ss(this.O(),a,b);_.Ss(this.qh,a,b)};_.g.yJ=function(a){this.OE?this.vZ():a.target==this.qh&&_.sz(this.NS,0,this)};_.g.NS=function(){try{this.O().focus()}catch(a){}};var P4=function(a){a=a.Ha().ub();return _.ge(_.ie(a)||window)};
M4.prototype.ua=function(){_.cj(this.Wo);this.Wo=null;_.cj(this.Vo);this.Vo=null;_.cj(this.bq);this.bq=null;_.cj(this.Zp);this.Zp=null;M4.N.ua.call(this)};var a5,T4,f5,g5,bda,cda;_.U4=function(a,b,c){M4.call(this,b,c);this.ik=a||"shr-bb-shr-cb";this.Ih=_.Q4(_.Q4(new _.R4,_.S4,!0),T4,!1,!0)};_.eb(_.U4,M4);_.g=_.U4.prototype;_.g.bG=!0;_.g.zA=!0;_.g.gJ=!0;_.g.OF=!0;_.g.NE=.5;_.g.zN="";_.g.Kd=null;_.g.nk=null;_.g.Gn=!1;_.g.Kp=null;_.g.Hs=null;_.g.yN=null;_.g.uh=null;_.g.Te=null;_.g.Hh=null;_.g.cw="dialog";_.g.Cb=function(){return this.ik};_.g.setTitle=function(a){this.zN=a;this.Hs&&_.ve(this.Hs,a)};_.g.getTitle=function(){return this.zN};
_.g.getContent=function(){return this.Kd!=null?_.fc(this.Kd).toString():""};_.g.Zn=function(){return this.cw};_.g.bD=function(a){this.cw=a};_.V4=function(a){a.O()||a.va()};_.U4.prototype.Ua=function(){_.V4(this);return this.Te};_.W4=function(a){_.V4(a);return a.Kp};_.X4=function(a){_.V4(a);return a.O()};_.U4.prototype.Ul=function(){_.V4(this);return _.U4.N.Ul.call(this)};_.Y4=function(a,b){a.NE=b;a.O()&&(b=a.Ul())&&_.Ts(b,a.NE)};
_.Z4=function(a,b){a.gJ=b;if(a.ob){var c=a.Ha(),d=a.Ul(),e=a.lb;b?(e&&c.xV(e,a.O()),c.xV(d,a.O())):(c.removeNode(e),c.removeNode(d))}a.isVisible()&&N4(a,b)};_.U4.prototype.setDraggable=function(a){this.OF=a;$4(this,a&&this.ob)};_.U4.prototype.sU=function(){};_.U4.prototype.getDraggable=function(){return this.OF};
var $4=function(a,b){var c=_.zc(a.ik+"-shr-s-shr-le").split(" ");a.O()&&(b?_.jD(a.Kp,c):_.lD(a.Kp,c));b&&!a.nk?(b=new _.A4(a.O(),a.Kp),a.nk=b,_.jD(a.Kp,c),_.Dj(a.nk,"start",a.xL,!1,a),_.Dj(a.nk,"drag",a.sU,!1,a)):!b&&a.nk&&(a.nk.dispose(),a.nk=null)};_.g=_.U4.prototype;
_.g.wa=function(){_.U4.N.wa.call(this);var a=this.O(),b=this.Ha();this.yN=this.getId();var c=this.getId()+".contentEl";this.Kp=b.wa("DIV",this.ik+"-shr-s",this.Hs=b.wa("SPAN",{className:this.ik+"-shr-s-shr-t",id:this.yN},this.zN),this.uh=b.wa("SPAN",this.ik+"-shr-s-shr-dc"));_.oe(a,this.Kp,this.Te=b.wa("DIV",{className:this.ik+"-shr-ce",id:c}),this.Hh=b.wa("DIV",this.ik+"-shr-je"));_.aD(this.Hs,"heading");_.aD(this.uh,"button");_.oD(this.uh,!0);_.bD(this.uh,"label","Close");_.aD(a,this.Zn());_.bD(a,
"labelledby",this.yN||"");this.Kd&&_.Hc(this.Te,this.Kd);_.Ls(this.uh,this.zA);this.Ih&&(a=this.Ih,a.ma=this.Hh,a.va());_.Ls(this.Hh,!!this.Ih);_.Y4(this,this.NE)};_.g.vc=function(){_.U4.N.vc.call(this);this.wb().na(this.O(),"keydown",this.KX).na(this.O(),"keypress",this.KX);this.wb().na(this.Hh,"click",this.tda);$4(this,this.OF);this.wb().na(this.uh,"click",this.Nda);var a=this.O();_.aD(a,this.Zn());this.Hs.id!==""&&_.bD(a,"labelledby",this.Hs.id);this.gJ||_.Z4(this,!1)};
_.g.Zd=function(){this.isVisible()&&this.setVisible(!1);$4(this,!1);_.U4.N.Zd.call(this)};_.g.setVisible=function(a){a!=this.isVisible()&&(this.ob||this.va(),_.U4.N.setVisible.call(this,a))};_.g.CB=function(){_.U4.N.CB.call(this);this.dispatchEvent("aftershow")};_.g.Pv=function(){_.U4.N.Pv.call(this);this.dispatchEvent("afterhide");this.Gn&&this.dispose()};
_.g.xL=function(){var a=this.Ha().ub(),b=_.ge(_.ie(a)||window),c=Math.max(a.body.scrollWidth,b.width);a=Math.max(a.body.scrollHeight,b.height);var d=_.u4(this.O());_.Rs(this.O())=="fixed"?_.B4(this.nk,new _.Qs(0,0,Math.max(0,b.width-d.width),Math.max(0,b.height-d.height))):_.B4(this.nk,new _.Qs(0,0,c-d.width,a-d.height))};_.g.Nda=function(){a5(this)};a5=function(a){if(a.zA){var b=a.Ih,c=b&&b.Ay;c?(b=b.get(c),a.dispatchEvent(new b5(c,b))&&a.setVisible(!1)):a.setVisible(!1)}};
_.c5=function(a){a.zA=!1;a.uh&&_.Ls(a.uh,a.zA)};_.U4.prototype.tL=_.jb(50);_.U4.prototype.ua=function(){this.Hh=this.uh=null;_.U4.N.ua.call(this)};_.d5=function(a,b){a.Ih=b;a.Hh&&(a.Ih?(b=a.Ih,b.ma=a.Hh,b.va()):_.Hc(a.Hh,_.nd),_.Ls(a.Hh,!!a.Ih))};_.U4.prototype.tda=function(a){a:{for(a=a.target;a!=null&&a!=this.Hh;){if(a.tagName=="BUTTON")break a;a=a.parentNode}a=null}if(a&&!a.disabled){a=a.name;var b=this.Ih.get(a);this.dispatchEvent(new b5(a,b))&&this.setVisible(!1)}};
_.U4.prototype.KX=function(a){var b=!1,c=!1,d=this.Ih,e=a.target;if(a.type=="keydown")if(this.bG&&a.keyCode==27){var f=d&&d.Ay;e=e.tagName=="SELECT"&&!e.disabled;f&&!e?(c=!0,b=d.get(f),b=this.dispatchEvent(new b5(f,b))):e||(b=!0)}else{if(a.keyCode==9&&a.shiftKey&&e==this.O()){this.OE=!0;try{this.qh.focus()}catch(l){}_.sz(this.vZ,0,this)}}else if(a.keyCode==13){if(e.tagName=="BUTTON"&&!e.disabled)f=e.name;else if(e==this.uh)a5(this);else if(d){var h=d.Ty,k=h&&_.e5(d,h);e=(e.tagName=="TEXTAREA"||e.tagName==
"SELECT"||e.tagName=="A")&&!e.disabled;!k||k.disabled||e||(f=h)}f&&d&&(c=!0,b=this.dispatchEvent(new b5(f,String(d.get(f)))))}else e!=this.uh||a.keyCode!=32&&a.key!=" "||a5(this);if(b||c)a.stopPropagation(),a.preventDefault();b&&this.setVisible(!1)};var b5=function(a,b){this.type="dialogselect";this.key=a;this.caption=b};_.eb(b5,_.qj);_.R4=function(a){H4.call(this);this.Eb=a||_.ae();this.ik="shr-a-shr-ac";this.Ay=this.ma=this.Ty=null};_.eb(_.R4,H4);
_.R4.prototype.clear=function(){H4.prototype.clear.call(this);this.Ty=this.Ay=null};_.R4.prototype.set=function(a,b,c,d){H4.prototype.set.call(this,a,b);c&&(this.Ty=a);d&&(this.Ay=a);return this};_.Q4=function(a,b,c,d){return a.set(b.key,b.caption,c,d)};_.R4.prototype.va=function(){if(this.ma){_.Hc(this.ma,_.nd);var a=_.ae(this.ma);this.forEach(function(b,c){b=a.wa("BUTTON",{name:c},b);c==this.Ty&&(b.className=this.ik+"-shr-jc");this.ma.appendChild(b)},this)}};_.R4.prototype.O=function(){return this.ma};
_.R4.prototype.Ha=function(){return this.Eb};_.e5=function(a,b){a=(a.ma||document).getElementsByTagName("BUTTON");for(var c,d=0;c=a[d];d++)if(c.name==b||c.id==b)return c;return null};_.S4={key:"ok",caption:"OK"};T4={key:"cancel",caption:"Cancel"};f5={key:"yes",caption:"Yes"};g5={key:"no",caption:"No"};bda={key:"save",caption:"Save"};cda={key:"continue",caption:"Continue"};
typeof document!="undefined"&&(_.Q4(new _.R4,_.S4,!0,!0),_.Q4(_.Q4(new _.R4,_.S4,!0),T4,!1,!0),_.Q4(_.Q4(new _.R4,f5,!0),g5,!1,!0),_.Q4(_.Q4(_.Q4(new _.R4,f5),g5,!0),T4,!1,!0),_.Q4(_.Q4(_.Q4(new _.R4,cda),bda),T4,!0,!0));
var nea=function(a){for(var b=typeof a==="string"?a.split(""):a,c=a.length-1;c>=0;c--)if(c in b&&_.v4.call(void 0,b[c],c,a))return c;return-1},S8=function(a){this.action=a;this.wfv=!1},T8=function(a,b){S8.call(this,"set-drive-options");this.appId=a;this.appOrigin=b},U8=function(a){S8.call(this,"visibility");this.visible=a},V8=function(a,b,c){_.U4.call(this,a,b,c)},W8=function(a){a=a&&a.getWindow()||window;return a.gadgets&&a.gadgets.rpc},oea=function(a,b){var c=b&&b.getWindow()||window;if(W8(b))a();
else if(X8)X8.push(a);else var d=X8=[a],e=c.setInterval(function(){if(W8(b)){c.clearInterval(e);for(var f=0;f<d.length;f++)d[f]();X8=null}},100)},pea=function(a,b){var c=new Y8(a);_.fj(b,c);var d={passive:!1};b.na(c,"mousewheel",function(e){return void Z8(a,e)},d).na(a,"scroll",function(e){return void Z8(a,e)},d)},Z8=function(a,b){var c;a:{for(c=b.target;c;){if(c.nodeType==1){var d=_.Bs(c,"overflowY");if(d=="auto"||d=="scroll")break a}c=c.parentNode}c=null}if(!c||!_.ue(a,c)||c.scrollHeight==c.clientHeight||
b.deltaY>0&&Math.abs(c.scrollTop-(c.scrollHeight-c.clientHeight))<=1||b.deltaY<0&&c.scrollTop==0)b.preventDefault(),b.stopPropagation()},b9=function(a,b,c,d,e,f,h,k){k=k===void 0?!1:k;_.U4.call(this,void 0,void 0,void 0);_.U4.call(this,f?f+" picker shr-bb-shr-cb":"picker shr-bb-shr-cb",!0,c);c=a.search(_.xu);f=_.wu(a,"protocol",c);if(!(f<0)){var l=a.indexOf("&",f);if(l<0||l>c)l=c;decodeURIComponent(a.slice(f+9,l!==-1?l:0).replace(/\+/g," "))}this.UH=new $8;this.fn=_.tu();this.Um=_.tu();a=_.Ru(a);
a.Pg("hostId");c=a.Pg("parent");this.J1=c==null?void 0:c.includes("onepick.corp.google.com");k&&_.Qu(a,"fv2","true");this.setUrl(a.toString());this.HY=d;this.GY=e;d!==void 0||e!==void 0?d=!0:(d=_.Ru(a.toString()).Pg("hostId"),a9||(a9=new Set("DocVerse fusiontables geo geowiki gm gmail-gadget gws hotpot jointly presentations pwa sites templates trix trix-copy-sheet webstore".split(" "))),d=!a9.has(d));this.p7=d;this.yz=h||this.Ha();b&&(h=this.yz,b=_.Ru(this.Hd).Pg("grugl")=="true"?qea:rea,W8(h)||(h=
h||_.ae(document),d=h.createElement("SCRIPT"),_.Hh(d,b),d.type="text/javascript",h.ub().body.appendChild(d)));b=a.toString();h=null;d=b.indexOf("/picker?");d>-1?h=b.substring(0,d+8-1):_.Mj(b,"/picker")&&(h=b);h&&this.Oj(h+"/resources/rpc_relay.html");this.bG=!1;_.d5(this,null);this.Ab=null},d9=function(a){c9.call(this,a)},f9=function(a){e9.call(this,"upload");this.Ta.query=a},g9=function(){f9.call(this,"docs")},h9=function(a){e9.call(this,a||"all")};_.U4.prototype.tL=_.pb(50,function(a){this.Gn=a});
_.Zd.prototype.uL=_.pb(0,function(a){this.Bc=a});
var i9=function(a){_.V4(a);return a.Hh},sea=function(a){return new _.xk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m){b(m)},h=function(m,n){d--;e[m]=n;d==0&&c(e)},k,l=0;l<a.length;l++)k=a[l],_.Ek(k,f,_.bb(h,l));else b(void 0)})},j9=function(a){return _.w4(a,"margin")},tea=function(){var a=null;return(new _.xk(function(b,c){a=_.sz(function(){b(void 0)},5E3);a==-1&&c(Error("xa"))})).zD(function(b){_.tz(a);throw b;})},l9=function(a,b){var c=a.V;return _.wL(a,c,c[_.hK]|0,k9,b,3).length},
m9=function(a){var b=b===void 0?_.KL:b;a=_.LL(a,23);var c=typeof a;a=a==null?a:c==="bigint"?_.yK((0,_.GL)(64,a)):_.nN(a)?c==="string"?_.DP(a):_.CP(a):void 0;return a!=null?a:b},n9=function(a,b,c){return _.uL(a,b,c==null?c:_.oN(c),0)},o9=function(a){this.V=_.fL(a)};_.y(o9,_.NL);o9.prototype.getSeconds=function(){return _.h1(this,1)};o9.prototype.setSeconds=function(a){return _.uL(this,1,_.zN(a,0),"0")};
var p9=function(a){var b=Number;var c=c===void 0?"0":c;var d;var e=(d=_.f1(_.LL(a,1),!0))!=null?d:c;b=b(e);a=_.hO(a,2);return new Date(b*1E3+a/1E6)},q9=function(a){return _.Cd&&(_.Ed||_.Gd)&&a%40!=0?a:a/40},r9=function(a,b,c,d){_.rj.call(this,b);this.type="mousewheel";this.detail=a;this.deltaX=c;this.deltaY=d};_.eb(r9,_.rj);
var Y8=function(a,b){_.Oj.call(this);this.ma=a;a=_.te(this.ma)?this.ma:this.ma?this.ma.body:null;this.qca=!!a&&_.Ps(a);this.FW=_.Dj(this.ma,_.Bd?"DOMMouseScroll":"mousewheel",this,b)};_.eb(Y8,_.Oj);
Y8.prototype.handleEvent=function(a){var b=0,c=0,d=a.Df;d.type=="mousewheel"?(a=q9(-d.wheelDelta),d.wheelDeltaX!==void 0?(b=q9(-d.wheelDeltaX),c=q9(-d.wheelDeltaY)):c=a):(a=d.detail,a>100?a=3:a<-100&&(a=-3),d.axis!==void 0&&d.axis===d.HORIZONTAL_AXIS?b=a:c=a);typeof this.XW==="number"&&(b=Math.min(Math.max(b,-this.XW),this.XW));typeof this.YW==="number"&&(c=Math.min(Math.max(c,-this.YW),this.YW));this.qca&&(b=-b);b=new r9(a,d,b,c);this.dispatchEvent(b)};
Y8.prototype.ua=function(){Y8.N.ua.call(this);_.Kj(this.FW);this.FW=null};var s9=function(a){this.V=_.fL(a)};_.y(s9,_.NL);s9.prototype.getId=function(){return _.iO(this,1)};s9.prototype.Me=function(a){return _.uL(this,1,_.CN(a),"")};var t9=function(a){this.V=_.fL(a)};_.y(t9,_.NL);t9.prototype.getSeconds=function(){return _.h1(this,1)};t9.prototype.setSeconds=function(a){return _.uL(this,1,_.zN(a,0),"0")};var u9=function(a){this.V=_.fL(a)};_.y(u9,_.NL);_.g=u9.prototype;_.g.getUrl=function(){return _.iO(this,1)};_.g.setUrl=function(a){return _.uL(this,1,_.CN(a),"")};_.g.Nc=function(){return _.hO(this,2)};_.g.Td=function(a){return _.uL(this,2,a==null?a:_.pN(a),0)};_.g.Qb=function(){return _.hO(this,3)};_.g.Ne=function(a){return _.uL(this,3,a==null?a:_.pN(a),0)};var v9=function(a){this.V=_.fL(a)};_.y(v9,_.NL);v9.prototype.getDuration=function(){return _.ZN(this,t9,2)};var w9=function(a){this.V=_.fL(a)};_.y(w9,_.NL);var x9=function(a){this.V=_.fL(a)};_.y(x9,_.NL);x9.prototype.Qn=function(a){return _.IP(this,1,a)};var y9=function(a){this.V=_.fL(a)};_.y(y9,_.NL);var z9=function(a){this.V=_.fL(a)};_.y(z9,_.NL);z9.prototype.wT=function(){return _.iO(this,5)};var A9=function(a){this.V=_.fL(a)};_.y(A9,_.NL);var B9=function(a){this.V=_.fL(a)};_.y(B9,_.NL);var C9=function(a){this.V=_.fL(a)};_.y(C9,_.NL);C9.prototype.dA=function(){return _.iO(this,1)};var D9=function(a){this.V=_.fL(a)};_.y(D9,_.NL);var E9=function(a){this.V=_.fL(a)};_.y(E9,_.NL);var F9=function(a){this.V=_.fL(a)};_.y(F9,_.NL);var G9=[2,4,5,6,7];var H9=function(a){this.V=_.fL(a)};_.y(H9,_.NL);var k9=function(a){this.V=_.fL(a)};_.y(k9,_.NL);_.g=k9.prototype;_.g.getId=function(){return _.ZN(this,s9,1)};_.g.Me=function(a){return _.bO(this,1,a)};_.g.getName=function(){return _.iO(this,2)};_.g.A_=function(a){return _.uL(this,2,_.CN(a),"")};_.g.JT=function(){return _.iO(this,4)};_.g.getType=function(){return _.jO(this,6)};_.g.Ud=function(a){return n9(this,6,a)};_.g.getMimeType=function(){return _.iO(this,7)};_.g.getUrl=function(){return _.iO(this,8)};
_.g.setUrl=function(a){return _.uL(this,8,_.CN(a),"")};_.g.OG=function(){return _.iO(this,11)};var I9=function(a){return _.aO(a,u9,10,_.HN())};_.g=k9.prototype;_.g.aU=function(){return _.ZN(this,v9,34)};_.g.NU=function(){return _.ML(this,v9,34)};_.g.D_=function(a){return _.JP(this,39,a)};_.g.Q_=function(a){return _.JP(this,44,a)};_.g.Oq=function(){return _.h1(this,45)};_.g.W_=function(a){return _.uL(this,45,_.zN(a,0),"0")};var J9=function(a){this.V=_.fL(a)};_.y(J9,_.NL);
J9.prototype.aU=function(){return _.ZN(this,v9,3)};J9.prototype.NU=function(){return _.ML(this,v9,3)};J9.prototype.wT=function(){return _.iO(this,10)};var K9=function(a){this.V=_.fL(a)};_.y(K9,_.NL);K9.prototype.Uz=function(){return _.iO(this,2)};K9.prototype.zc=function(a){return _.uL(this,2,_.CN(a),"")};var L9=function(a){this.V=_.fL(a)};_.y(L9,_.NL);_.g=L9.prototype;_.g.getStatus=function(){return _.jO(this,1)};_.g.Rz=function(a){return _.aO(this,k9,2,_.HN(a))};_.g.Yf=function(a){_.xL(this,2,k9,void 0,a)};_.g.setItem=function(a,b){return _.$N(this,2,k9,a,b)};_.g.removeItem=function(a){return _.HP(this,2,k9,a)};_.g.Yh=function(){return l9(this,2)};var M9=function(a){this.V=_.fL(a)};_.y(M9,_.NL);_.g=M9.prototype;_.g.lL=function(a){return n9(this,1,a)};_.g.getVisible=function(){return _.ZO(this,5)};_.g.setVisible=function(a){return _.JP(this,5,a)};_.g.Rz=function(a){return _.aO(this,k9,7,_.HN(a))};_.g.Yf=function(a){_.xL(this,7,k9,void 0,a)};_.g.setItem=function(a,b){return _.$N(this,7,k9,a,b)};_.g.removeItem=function(a){return _.HP(this,7,k9,a)};_.g.Yh=function(){return l9(this,7)};_.g.getQuery=function(){return _.iO(this,8)};
_.g.hb=function(a){return _.uL(this,8,_.CN(a),"")};_.g.Bz=function(){return _.iO(this,9)};_.g.qp=function(a){return _.uL(this,9,_.CN(a),"")};var N9=function(a){this.V=_.fL(a)};_.y(N9,_.NL);_.g=N9.prototype;_.g.lL=function(a){return n9(this,1,a)};_.g.Rz=function(a){return _.aO(this,k9,4,_.HN(a))};_.g.Yf=function(a){_.xL(this,4,k9,void 0,a)};_.g.setItem=function(a,b){return _.$N(this,4,k9,a,b)};_.g.removeItem=function(a){return _.HP(this,4,k9,a)};_.g.Yh=function(){return l9(this,4)};var uea=_.g1(N9);var O9=new Map;O9.set("application/vnd.google-apps.document","application/vnd.google-gsuite.document-blob");O9.set("application/vnd.google-apps.spreadsheet","application/vnd.google-gsuite.spreadsheet-blob");O9.set("application/vnd.google-apps.presentation","application/vnd.google-gsuite.presentation-blob");var P9=new Set;P9.add("application/vnd.google-apps.kix");P9.add("application/vnd.google-apps.ritz");P9.add("application/vnd.google-apps.punch");var Eea=function(a,b,c){var d=c&&_.iO(c,1);c=Q9(c);var e=a[0].getId();switch(_.jO(e,2)){case 13:return vea(a,b,d,c);case 3:return wea(a,b,d,c);case 10:return R9(a,b,d,c);case 27:return R9(a,b,d,c);case 12:return xea(a,b);case 2:return yea(a,b,d,c);case 5:return zea(a,b,d,c);case 9:return Aea(a,b,d,c);case 6:return Bea(a,b,d,c);case 25:return Cea(a,b,d,c);case 1:return _.ML(a[0],w9,26)?Dea(a,b,d,c):R9(a,b,d,c);default:return{}}},Bea=function(a,b,c,d){a=a.map(function(e){return{id:e.getId().getId(),
serviceId:"youtube",name:e.getName(),description:_.iO(e,3),type:S9(e),lastEditedUtc:p9(_.ZN(e,o9,16)).getTime(),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",url:e.getUrl(),embedUrl:_.iO(e,13),thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"youtube",v2Translated:!0}},yea=function(a,b,c,d){var e=a.map(function(f){var h=f.getId().getId(),k=f.getMimeType(),l=f.getName(),m=S9(f),n=p9(_.ZN(f,o9,12)).getTime(),p=f.getUrl(),q=Number(m9(f)),r=_.iO(f,3),
w=f.OG(),u=T9(I9(f));var x=_.ZN(f,J9,21);x=_.ZO(x,1);var A=_.ZN(f,J9,21).wT(),D=_.iO(f,13);var E=_.ZN(f,J9,21);E=_.ZO(E,9);var N=_.ZN(f,J9,21);N=_.iO(N,11);h={id:h,serviceId:"docs",mimeType:k,name:l,type:m,lastEditedUtc:n,url:p,sizeBytes:q,description:r,iconUrl:w,thumbnails:u,isShared:x,downloadUrl:A,embedUrl:D,copyable:E,resourceKey:N};k=_.ZN(f,J9,21);_.ZO(k,12)&&(h.uploadState="success",h.isNew=!0);_.ZN(f,J9,21).NU()&&(f=_.ZN(f,J9,21).aU(),_.ML(f,t9,2)&&(k=_.YO(f.getDuration().getSeconds()),l=f.getDuration(),
l=_.hO(l,2),h.duration=k+l/1E9),h.aspectRatio=_.x4(f,1));return h});return{action:b,docs:e,viewToken:d,view:c||"all",extraUserInputs:U9(a),v2Translated:!0}},zea=function(a,b,c,d){a=a.map(function(e){var f=_.ZN(e,H9,15);f=_.ZN(f,u9,1);_.xL(e,10,u9,void 0,f);return{id:e.getId().getId(),serviceId:"web",mimeType:e.getMimeType(),name:e.getName(),type:S9(e),url:e.getUrl(),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e))}});return{action:b,
docs:a,viewToken:d,view:c||"image-search",v2Translated:!0}},Aea=function(a,b,c,d){a=a.map(function(e){return{serviceId:"url",name:I9(e)[0].getUrl().split("/").pop(),type:S9(e),mimeType:e.getMimeType(),url:e.getUrl(),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"url",v2Translated:!0}},Dea=function(a,b,c,d){a=a.map(function(e){return{id:e.getId().getId(),serviceId:"picasa",name:e.getName(),
type:S9(e),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"webcam",v2Translated:!0}},R9=function(a,b,c,d){return{action:b,docs:a.map(Fea),viewToken:d,view:Gea(c||"upload",a),extraUserInputs:U9(a),v2Translated:!0}},Gea=function(a,b){return b.some(function(c){var d;return((d=_.ZN(c,F9,30))==null?void 0:_.jO(d,3))===9})?a+"/gmailphotos":a},wea=function(a,b,c,d){var e=a.map(function(f){var h=
{id:f.getId().getId(),serviceId:"picasa",mimeType:f.getMimeType(),name:f.getName(),type:S9(f),lastEditedUtc:p9(_.ZN(f,o9,12)).getTime(),url:f.getUrl(),sizeBytes:Number(m9(f)),description:_.iO(f,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(f)),mediaKey:f.getId().getId(),parentId:f.JT()},k,l=(k=_.ZN(f,z9,18))==null?void 0:_.ZN(k,y9,3);l&&(h.latitude=_.x4(l,1),h.longitude=_.x4(l,2));var m;f=(m=_.ZN(f,z9,18))==null?void 0:_.h1(m,6);m=f!=null?_.YO(f):
void 0;m&&(h.version=m);return h});return{action:b,docs:e,viewToken:d,view:c||"photos",extraUserInputs:U9(a),v2Translated:!0}},vea=function(a,b,c,d){a=a.map(function(e){return{id:e.getId().getId(),serviceId:"et",name:I9(e)[0].getUrl().split("/").pop(),description:_.iO(e,3),type:"et",iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e),"etjpg")}});return{action:b,docs:a,viewToken:d,view:c||"et",v2Translated:!0}},Cea=function(a,b,c,d){a=a.map(function(e){var f=
e.getId().getId(),h=d["2"];return{id:f,serviceId:h.type!=="gmail_themes"&&h.parent==="6226252643674576769"?"picasa":"static_themes",name:e.getName(),mimeType:e.getMimeType(),type:S9(e),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",url:e.getUrl(),embedUrl:e.getUrl(),thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"photos",v2Translated:!0}},xea=function(a,b){a=a.map(function(c){var d=_.ZN(c,x9,27);d=_.EP(d,1,_.HN())[0];return{id:c.getId().getId(),
serviceId:"contacts",mimeType:c.getMimeType(),name:c.getName(),description:_.iO(c,3),url:"mailto:"+d,thumbnail:[{url:c.OG()}],email:d}});return{action:b,docs:a,view:"contacts",v2Translated:!0}},Q9=function(a){if(!a)return{};var b=_.iO(a,3);b=b&&JSON.parse(b)||{};return{0:_.iO(a,1),1:a.Uz(),2:b}},T9=function(a,b){b=b===void 0?"":b;if(!a.length)return null;b=="etjpg"&&(a=a.filter(function(c){return c.getUrl().includes("w1200-h300")}));return a.map(function(c){return{url:c.getUrl(),height:c.Nc(),width:c.Qb(),
type:b}})},U9=function(a){return{isAttachment:a.some(function(b){return _.ZO(b,31)})}},Fea=function(a){var b,c={id:(b=a.getId())==null?void 0:b.getId(),serviceId:Hea(a),mimeType:a.getMimeType(),name:a.getName(),type:S9(a),sizeBytes:Number(m9(a)),description:_.iO(a,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(a)),isNew:!0};if(b=_.ZN(a,F9,30)){c.dataUrl=_.iO(b,1)||null;a:{var d=_.ZN(a,F9,30);switch(_.jO(d,3)){case 9:d=_.y4(d,D9,2,G9);d={remoteRefs:d&&
_.iO(d,1)};break a;case 10:case 15:d=_.y4(d,C9,4,G9);var e=I9(a);d={photo_id:d&&d.dA(),media_key:a.getId().getId(),media_type:a.getType()===1?1:2,image_url:e&&e[0].getUrl(),width:e&&String(e[0].Qb()),height:e&&String(e[0].Nc())};break a}d=null}c.uploadMetadata=d;c.uploadId=_.iO(b,9)||null;c.uploadState=Iea(b)||null}(d=b&&_.y4(b,C9,4,G9))&&(c.id=d.dA());(d=b&&_.y4(b,E9,5,G9))&&(c.contentId=_.iO(d,1));(d=b&&_.y4(b,A9,7,G9))&&(c.contentId=_.iO(d,1));(d=b&&_.y4(b,B9,6,G9))&&(c.contentId=_.iO(d,1));(b==
null?void 0:_.jO(b,3))===1&&(c.mediaKey=a.getId().getId());return c},Hea=function(a){var b;switch(((b=_.ZN(a,F9,30))==null?void 0:_.jO(b,3))||0){case 1:return"picasa";case 2:case 3:case 4:case 8:case 12:return"photo";case 10:case 15:return"dragonflyphotos";case 11:return"mapspro";case 13:return"books";case 14:return"cultural"}return null},S9=function(a){switch(a.getType()){case 1:case 21:return"photo";case 2:return"video";case 6:return"folder";case 13:return"calendar";case 14:return"album";case 19:return"contact";
case 3:case 4:case 5:case 7:case 12:return a.getMimeType().startsWith("application/vnd.google-apps.")?"document":"file";default:return"file"}},Iea=function(a){switch(a==null?void 0:_.jO(a,8)){case 0:return"default";case 1:return"canceled";case 2:return"error";case 3:return"running";case 4:return"scheduled";case 5:return"success"}return null};var Jea=_.gd(["https://apis.google.com/js/api.js"]),Kea=_.vc(Jea),Lea=function(a,b){var c=!1,d=V9(a,b).then(function(f){c=!0;return f}),e=tea().then(function(){return c?V9(a,b):W9(b).then(function(f){return X9(f,a)})});return sea([d,e])},V9=function(a,b){return Mea(b).then(function(c){return X9(c,a)})},X9=function(a,b){var c=_.wc(b,"gapi.")?b.slice(5):b;return a[c]?_.Bk(a[c]):new _.xk(function(d,e){var f=_.sz(function(){e(Error("ed"))},3E4);a.load(b,{callback:function(){_.tz(f);d(a[c])},onerror:function(h){_.tz(f);
e(h)}})})},Mea=function(a){return a.gapi&&a.gapi.load?_.Bk(a.gapi):W9(a)},W9=function(a){return _.OP(Kea,{document:a.document}).then(function(){return a.gapi})},Y9=function(){this.Ab=null};Y9.prototype.load=function(a,b){b=b===void 0?window:b;var c=Date.now();return Lea(a,b).then(function(d){return{zW:d,Ica:c,Fca:Date.now()}},function(d){throw d instanceof Error?d:Error(String(d));})};var $8=function(){_.dj.call(this);this.z9=new Y9;this.Ab=this.Cd=null;this.wq=_.Hk();this.Az=_.Hk();this.Az.promise.then(null,function(){});this.wq.promise.then(null,function(){});this.WU={}};_.y($8,_.dj);
var Z9=function(a,b){var c=_.wb("gapi.iframes",b);return c?(a=Date.now(),_.Bk({zW:c,Ica:a,Fca:a})):a.z9.load("gapi.iframes",b)},Nea=function(a,b,c,d,e,f){var h=!1;h=h===void 0?!1:h;Z9(a,_.ie(b.ownerDocument)).then(function(k){var l=k.zW;a.Az.resolve(l);k=a.wq;var m=k.resolve,n=h;n=n===void 0?!1:n;var p={};p["host-message-handler"]=c;a.WU=p;var q=_.Ru(d);q=_.Ru(q);q=_.Fu(_.Eu(_.Cu(new _.Bu,q.Ci),q.Ng()),q.Bg).toString();a.Cd=q;q=_.to((new _.uo).setUrl(d),b);q.T.allowPost=n;n=_.ek(_.dk(q,p),_.wb("makeWhiteListIframesFilter",
l)([a.Cd]));e!=null&&n.Me(e);f!=null&&n.Zm(f);n.Vn().Ei({display:"block","min-width":"100%",width:"1px"}).Td("100%");n.Vn().value().allow="camera 'src' "+a.Cd;l=l.getContext().openChild(n.value());m.call(k,l)},function(k){a.Az.reject(k);a.wq.reject(k)});return a.wq.promise},Oea=function(a,b){return _.Fk([a.Az.promise,a.wq.promise]).then(function(c){var d=_.Aa(c);c=d.next().value;return(d=d.next().value)?d.send("picker-message-handler",b,void 0,_.wb("makeWhiteListIframesFilter",c)([a.Cd])):_.Ck(Error("fd"))})};
$8.prototype.ua=function(){Pea(this);_.dj.prototype.ua.call(this)};var Pea=function(a){a.wq.promise.then(function(b){b&&(b.unregister("host-message-handler"),delete a.WU["host-message-handler"])})};var $9=function(){this.mda=0};$9.prototype.getUniqueId=function(){return":"+(this.mda++).toString(36)};var Rea=function(a,b){switch(a.action){case "select-contacts":a=Qea(a.contacts);break;case "visibility":a=a.visible;a=(new M9).lL(7).setVisible(a);var c={};c["iframe-command"]=_.IN(a);a=c;break;default:a=null}return a?Oea(b,a):_.Bk()},Qea=function(a){if(!a||a.length==0)return null;var b=new M9;b.lL(11);var c=new $9;a.forEach(function(d){if(d.email){var e=(new k9).A_(d.name?d.name:d.email);var f=new x9;d=_.FP(f,1,d.email);e=_.bO(e,27,d);d=e.Me;f=(new s9).Me(c.getUniqueId());f=n9(f,2,12);e=d.call(e,
f)}else e=null;e&&b.Yf(e)});a={};a["iframe-command"]=_.IN(b);return a};_.eb(T8,S8);_.eb(U8,S8);_.eb(V8,_.U4);var Sea=_.gd(["//www-onepick-opensocial.googleusercontent.com/gadgets/js/rpc.js?c=1&container=onepick"]),Tea=_.gd(["//apis.google.com/js/rpc.js"]),rea=_.vc(Sea),qea=_.vc(Tea),X8=null;var a9;var Uea=_.gd(["https://about:blank"]),Vea=_.gd(['javascript:""']),Wea=_.gd(["about:blank"]);_.eb(b9,V8);_.g=b9.prototype;_.g.Hd="";_.g.Ja=null;_.g.GA=!1;_.g.RI=!1;_.g.Yb=function(){};_.g.vc=function(){b9.N.vc.call(this);pea(this.Ul(),this.wb())};_.g.va=function(a){var b=this;b9.N.va.call(this,a);this.J1?Z9(this.UH,window).then(function(){return Xea(b)}).then(null,function(c){return void b.Rq(c)}):Yea(this)};
var Xea=function(a){var b=a.Ha().wa("div",["picker-dialog-content","picker-frame"]);_.Ls(b,!1);a.Ua().appendChild(b);return Nea(a.UH,b,function(c){var d=uea(c["iframe-command"]);switch(_.jO(d,1)){case 1:c=a.Yb;d=_.ZN(d,L9,2);var e="";switch(d.getStatus()){case 2:e="cancel";break;case 1:e="picked";break;case 3:e="error"}var f=_.GP(d.Rz(_.AL));d=f.length===0?{action:e}:Eea(f,e,_.ZN(d,K9,7));c.call(a,d);a.setVisible(!1);break;case 4:_.c5(a);a.Yb({action:"loaded"});break;case 7:case 8:c=a.Yb;a:{e=_.jO(d,
1);f=_.ZN(d,L9,2);switch(e){case 7:e="uploadScheduled";break;case 8:e="uploadStateChange";break;default:d={};break a}d=_.GP(f.Rz(_.AL));if(d.length===0)d={action:e};else{var h=_.ZN(f,K9,7);f=h&&_.iO(h,1);h=Q9(h);d=R9(d,e,f,h)}}c.call(a,d)}},a.Hd,a.fn,a.Um).then(function(c){a.Ja=c.getIframeEl();a$(a);_.Ls(a.Ja.parentElement,!0);b$(a);c$(a);return c})},b$=function(a){_.iD(a.O(),"picker-dialog");_.iD(a.Ja,"picker-dialog-frame");_.iD(_.W4(a),"picker-dialog-title");_.iD(a.Ul(),"picker-dialog-bg");_.iD(a.lb,
"picker-dialog-bg");_.iD(a.Ua(),"picker-dialog-content");i9(a)&&_.iD(i9(a),"picker-dialog-buttons")},Yea=function(a){a.Ja=a.Eb.wa("IFRAME",{id:a.fn,name:a.fn,"class":"picker-frame",frameBorder:"0",allow:"camera"});_.t4(a.Ja,Zea(a));b$(a);a.Ua().appendChild(a.Ja);a.wb().na(a.Ja,"load",function(){return void a$(a)});a.Ja.src=a.Hd;c$(a)},$ea=function(a){d$(a,(0,_.z)(function(b){b.setAuthToken(this.fn,this.Um)},a))},c$=function(a){var b=_.Ru(a.Hd).Pg("title");b&&a.setTitle(b)},Zea=function(a){return _.QO("Internet Explorer")>=
7&&_.wc(a.Hd,"https")?_.vc(Uea):_.yd?_.vc(Vea):_.vc(Wea)},a$=function(a){$ea(a);a.GA=!0;_.oD(a.Ja,!0);a.isVisible()&&a.focus()};b9.prototype.BH=function(a){a.keyCode==27&&(this.setVisible(!1),this.Yb({action:"cancel"}),a.stopPropagation(),a.preventDefault())};var d$=function(a,b){var c=a.yz;oea(function(){b(c.getWindow().gadgets.rpc)},c)};_.g=b9.prototype;
_.g.setUrl=function(a){a=_.Qu(new _.Bu(a),"rpcService",this.fn);_.Qu(a,"rpctoken",this.Um);a.Uk("rpctoken="+this.Um);_.Qu(a,"thirdParty","true");_.Cu(a,"https");this.Hd=a.toString();this.Ja&&(this.Ja.src=this.Hd)};_.g.qp=function(a){this.Yx=a;this.RI&&e$(this)};_.g.Xr=function(a){this.Yb=a;d$(this,(0,_.z)(function(b){b.register(this.fn,(0,_.z)(this.Caa,this))},this))};
_.g.Caa=function(a){var b=a.action;b=="loaded"&&(this.RI=!0,f$(this,new U8(this.isVisible())),e$(this),_.c5(this),this.wb().Ac(this.Ha().getWindow(),"keydown",this.BH),_.oD(this.Ja,!0));b!="picked"&&b!="cancel"||this.setVisible(!1);this.Yb(a)};_.g.vA=function(a){return _.Ru(this.Hd).Pg(a)=="true"};var e$=function(a){a.Yx&&f$(a,new T8(a.Yx,window.location.protocol+"//"+window.location.host))};b9.prototype.Oj=function(a){d$(this,(0,_.z)(function(b){b.setRelayUrl(this.fn,a)},this))};
var f$=function(a,b){a.J1&&Rea(b,a.UH).then(null,function(c){return void a.Rq(c)});d$(a,(0,_.z)(function(c){c.call(this.fn,"picker",null,b)},a))};_.g=b9.prototype;
_.g.AK=function(){var a=this.Ha().ub();a=_.ie(a)||window;if(this.vA("ignoreLimits"))a=new _.rd(this.HY,this.GY);else if(this.vA("shadeDialog")){var b=j9(_.X4(this)),c=_.ge(a);a=c.width-80;c=c.height-40;b&&(a-=b.left?b.left:0,a-=b.right?b.right:0,c-=b.top?b.top:0,c-=b.bottom?b.bottom:0);a=new _.rd(a>0?a:0,c>0?c:0)}else(b=this.HY)?(b=Math.max(320,Math.min(1051,b)),(c=this.GY)||(c=_.ge(a).height*.85),c=Math.max(480,Math.min(650,c))):(b=_.ge(a),c=b.width*.618,c=c<b.height?Math.round(Math.max(480,Math.min(650,
c*.85))):Math.round(Math.max(480,Math.min(650,b.height*.85))),b=Math.round(c/.618)),a=_.ge(a),b=Math.min(b,Math.max(a.width,320)),c=Math.min(c,Math.max(a.height,480)),a=new _.rd(b,c);_.Js(this.Ua(),a);this.Ie()};_.g.Ie=function(){if(this.vA("shadeDialog")){var a=_.u4(this.O()),b=_.ge(this.Ha().getWindow());a=Math.floor(b.width/2-a.width/2);if(b=j9(_.X4(this))){var c=b.left?b.left:0;c+=b.right?b.right:0;a=Math.floor(a-c/2)}a=a>0?a:0;b=_.ss(this.Ha()).y;_.Ss(this.O(),a,b)}else b9.N.Ie.call(this)};
_.g.setVisible=function(a){if(a!=this.isVisible()&&this.p7){var b=this.Ha().getWindow();a?(this.AK(),this.wb().na(b,"resize",this.AK),this.GA||this.wb().na(b,"keydown",this.BH)):(this.wb().Ac(b,"resize",this.AK),this.GA||this.wb().Ac(b,"keydown",this.BH))}b9.N.setVisible.call(this,a);f$(this,new U8(a))};_.g.focus=function(){b9.N.focus.call(this);if(this.Ja&&this.GA&&this.RI)try{this.Ja.focus()}catch(a){}};_.g.Rq=function(){this.Yb({action:"error"})};var e9=function(a){this.Da=a;this.Ta={}};_.g=e9.prototype;_.g.zc=function(a){this.pW=a||void 0;return this};_.g.setOptions=function(a){this.Ta=a;return this};_.g.hb=function(a){this.Ta.query=a;return this};_.g.Mc=function(a){this.Ta.mimeTypes=a;return this};_.g.Nb=function(a){this.Ta.parent=a;return this};_.g.e1=function(){var a=_.gi(this.Ta,function(b){return b!==null});a=_.Gh(a)?null:a;a=[this.Da,this.pW,a];return a=a.slice(0,nea(a)+1)};
_.g.toString=function(){var a=this.e1();return"("+_.Ib(a,function(b){return JSON.stringify(b)}).join(",")+")"};_.g.getId=function(){return this.Da};_.g.Uz=function(){return this.pW};_.g.getOptions=function(){return _.fk(this.Ta)};_.g.getQuery=function(){return this.Ta.query};var g$=function(){e9.call(this,"image-search");this.Ta.license="crwm"};_.y(g$,e9);_.g=g$.prototype;_.g.Fw=function(a){this.Ta.site=a;return this};_.g.Ud=function(a){this.Ta.type=a;return this};_.g.tga=function(a){a=="*"?delete this.Ta.license:this.Ta.license=a;return this};_.g.setSize=function(a){this.Ta.imgsz=a;return this};_.g.setColor=function(a){this.Ta.imgcolor=a;return this};_.g.iD=function(a){this.Ta.safe=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};
_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var h$=function(){e9.call(this,"maps")};_.y(h$,e9);_.g=h$.prototype;_.g.XC=function(a){this.Ta.mode=a;return this};_.g.setCenter=function(a,b){this.Ta.center=[a,b];return this};_.g.setZoom=function(a){this.Ta.zoom=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var i$=function(){e9.call(this,"photos")};_.y(i$,e9);_.g=i$.prototype;_.g.XC=function(a){this.Ta.mode=a;return this};_.g.Ud=function(a){this.Ta.type=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var j$=function(){e9.call(this,"url")};_.y(j$,e9);_.g=j$.prototype;_.g.Fw=function(a){this.Ta.site=a;return this};_.g.Ud=function(a){this.Ta.type=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var k$=function(){e9.call(this,"video-search")};_.y(k$,e9);_.g=k$.prototype;_.g.Fw=function(a){this.Ta.site=a;return this};_.g.iD=function(a){this.Ta.safe=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var l$=function(){e9.call(this,"web")};_.y(l$,e9);_.g=l$.prototype;_.g.Fw=function(a){this.Ta.site=a;return this};_.g.iD=function(a){this.Ta.safe=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var m$=function(a){this.Sd=typeof a==="string"?new e9(a):a;this.Mf=[];this.Ta={}},afa=function(a){switch(a){case "image-search":return new g$;case "maps":return new h$;case "photos":return new i$;case "url":return new j$;case "video-search":return new k$;case "web":return new l$}return new e9(a)};m$.prototype.Px=function(a){this.Mf.push(typeof a==="string"?afa(a):a);return this};m$.prototype.ZO=function(a){this.Mf.push((new e9(null)).zc(a));return this};
m$.prototype.Qx=function(a){this.Mf.push(a);return this};var n$=function(a){return"("+_.Ib(a.Mf,function(b){return b.toString()}).join(",")+")"},bfa=function(a){a=_.gi(a.Ta,function(b){return b!==null});return(a=_.Gh(a)?null:a)?JSON.stringify(_.FH(a,function(b){return b.toString()})):""};m$.prototype.toString=function(){if(this.Sd){var a=["{root:",this.Sd.toString(),",items:",n$(this)],b=bfa(this);b&&(a.push(",options:"),a.push(b));a.push("}");return a.join("")}return n$(this)};
m$.prototype.getOptions=function(){return _.fk(this.Ta)};m$.prototype.bga=function(a){this.Ta.collapsible=a;return this};var c9=function(a){this.s7=a||"https://docs.google.com/picker";this.xB=new m$};_.g=c9.prototype;_.g.xB=null;_.g.yd=null;_.g.ZO=function(){throw Error("gd");};_.g.Px=function(a){this.xB.Px(a);return this};_.g.Qx=function(a){this.xB.Qx(a);return this};_.g.Bz=function(){return this.Yx};_.g.Ha=function(){return this.yd};_.g.pba=function(){return this.setTitle("")};_.g.qp=function(a){this.Yx=a;return this};_.g.Xr=function(a){this.Yb=a;return this};_.g.uL=function(a){this.yd=new _.Zd(a);return this};_.eb(d9,c9);var o$=function(a,b){b=b===void 0?b9:b;c9.call(this,a);this.hea=b;this.jc=new Map;this.jc.set("protocol","gadgets");window.google&&(a=(a=window.google)&&a.picker&&a.picker.LoadArgs)&&(a=(new _.Hu(a)).get("hl"))&&this.ds(a.toString());(a=window.location.origin)||(a=window.location.protocol+"//"+window.location.host);this.Mj(a);this.DY=[];this.yz=void 0};_.y(o$,d9);_.g=o$.prototype;_.g.Gn=!1;
_.g.Jb=function(){this.jc.set("hostId",window.location.host.split(":")[0]);this.Bz()&&this.jc.has("oauth_token")&&this.jc.set("appId",this.Bz());this.Gu()||this.Oj(_.Gu(_.Ru(window.location.href)).Uk("").setPath("/favicon.ico").toString());this.jc.set("ifls",Date.now());if(this.jc.get("minimal"))throw Error("hd");var a=new this.hea(this.g1().toString(),!0,this.Ha(),this.YQ,this.XQ,"",this.yz,!1,!1);a.tL(this.Gn);a.qp(this.Bz());a.Xr(this.Yb);return a};_.g.t8=function(a){this.jc.delete(a);return this};
_.g.N8=function(a){this.jc.set(a,"true");return this};_.g.Nc=function(){return this.XQ};_.g.Xn=function(){return this.jc.get("hl")};_.g.Gu=function(){return this.jc.get("parent")};_.g.getTitle=function(){return this.jc.get("title")};_.g.Qb=function(){return this.YQ};_.g.fca=function(a){return this.jc.get(a)=="true"};_.g.dga=function(a){this.jc.set("developerKey",a);return this};_.g.Zfa=function(a){this.jc.set("authuser",a);return this};_.g.tL=function(a){this.Gn=a};
_.g.uga=function(a){this.jc.set("maxItems",a);return this};_.g.Mj=function(a){a&&this.jc.set("origin",a);return this};_.g.oga=function(a){a instanceof e9?this.jc.set("view",a.toString()):this.jc.set("view",a);return this};_.g.ds=function(a){this.jc.set("hl",a);return this};_.g.gM=function(a){this.jc.set("oauth_token",a);return this};_.g.Oj=function(a){this.jc.set("parent",a);return this};_.g.Aga=function(a){this.jc.set("selectableMimeTypes",a);return this};
_.g.setSize=function(a,b){this.YQ=a;this.XQ=b;return this};_.g.Fga=function(a){this.jc.set("uploadToAlbumId",a);return this};_.g.setTitle=function(a){this.jc.set("title",a);return this};_.g.g1=function(){this.DY.length&&this.jc.set("pp",JSON.stringify(this.DY));this.jc.set("nav",n$(this.xB));var a=new _.Bu(this.s7);this.jc.forEach(function(b,c){_.Qu(a,c,b)});return a};_.eb(f9,e9);f9.prototype.hb=function(){throw Error("id");};f9.prototype.zc=function(a){f9.N.zc.call(this,a);return this};f9.prototype.Mc=function(a){f9.N.Mc.call(this,a);return this};f9.prototype.Nb=function(a){f9.N.Nb.call(this,a);return this};_.eb(g9,f9);_.g=g9.prototype;_.g.PL=function(a){this.Ta.includeFolders=a;return this};_.g.zc=function(a){g9.N.zc.call(this,a);return this};_.g.hb=function(a){g9.N.hb.call(this,a);return this};_.g.Mc=function(a){g9.N.Mc.call(this,a);return this};_.g.Nb=function(a){g9.N.Nb.call(this,a);return this};_.eb(h9,e9);var cfa=["dr","fileIds","parent"];_.g=h9.prototype;_.g.XC=function(a){this.Ta.mode=a;return this};_.g.fga=function(a){p$(this);this.Ta.dr=a;return this};_.g.gga=function(a){this.Ta.td=a;return this};_.g.D_=function(a){a!==void 0?this.Ta.ownedByMe=a:delete this.Ta.ownedByMe;return this};_.g.Q_=function(a){this.Ta.starred=a;return this};_.g.PL=function(a){this.Ta.includeFolders=a;return this};_.g.zga=function(a){this.Ta.selectFolder=a;return this};
_.g.ega=function(a){this.Ta.docTypesDropDown=a;return this};_.g.zc=function(a){h9.N.zc.call(this,a);return this};_.g.hb=function(a){h9.N.hb.call(this,a);return this};_.g.Mc=function(a){h9.N.Mc.call(this,a);return this};_.g.kga=function(a){p$(this);this.Ta.fileIds=a;return this};_.g.Nb=function(a){p$(this);h9.N.Nb.call(this,a);return this};var p$=function(a){for(var b=_.Aa(cfa),c=b.next();!c.done;c=b.next())c=c.value,a.Ta[c]&&delete a.Ta[c]};var q$=function(){e9.call(this,"photo-albums")};_.y(q$,e9);_.g=q$.prototype;_.g.Ud=function(a){this.Ta.type=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var dfa={DoclistBlob:"file",doc:"document",drawing:"drawing",folder:"folder",kix:"document",pres:"presentation",spread:"spreadsheet"};var r$=function(a,b){e9.call(this,"webcam");a&&(this.Ta.type=a);b&&(this.Ta.query=b)};_.y(r$,e9);r$.prototype.hb=function(){throw Error("jd");};r$.prototype.zc=function(a){e9.prototype.zc.call(this,a);return this};r$.prototype.Mc=function(a){e9.prototype.Mc.call(this,a);return this};r$.prototype.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var efa=_.gd([".picker-dialog-frame{width:100%;height:100%;border:0;overflow:hidden}.picker-dialog-bg{position:absolute;top:0;left:0;background-color:#fff;z-index:1000}.picker-dialog{position:absolute;top:0;left:0;background-color:#fff;border:1px solid #acacac;width:auto;padding:0;z-index:1001;overflow:auto;box-shadow:0 4px 16px rgba(0,0,0,.2)}.picker-dialog-content{height:100%;font-size:0;padding:0}.picker-dialog-buttons,.picker-dialog-title{display:none}"]),ffa=_.hc(efa[0]);try{_.Ns(ffa)}catch(a){_.Vf.error("Failed to install picker styles : "+a),_.Xa.setTimeout(function(){_.Ta.MW(a)},0)};_.t("gapi.picker.api.Action",{xia:"cancel",oma:"picked",roa:"uploadProgress",soa:"uploadScheduled",uoa:"uploadStateChange",bla:"loaded",nia:"blurred",Oja:"enableWhiteCallout",Coa:"viewChanged",Hoa:"viewUpdated",Doa:"viewContentRendered",ERROR:"error",Gma:"received"});_.t("gapi.picker.api.Action.CANCEL","cancel");_.t("gapi.picker.api.Action.PICKED","picked");_.t("gapi.picker.api.Audience",{hma:"ownerOnly",nO:"limited",aia:"allPersonalCircles",Uja:"extendedCircles",xja:"domainPublic",AO:"public"});
_.t("gapi.picker.api.Audience.PUBLIC","public");_.t("gapi.picker.api.Audience.DOMAIN_PUBLIC","domainPublic");_.t("gapi.picker.api.Audience.EXTENDED_CIRCLES","extendedCircles");_.t("gapi.picker.api.Audience.ALL_PERSONAL_CIRCLES","allPersonalCircles");_.t("gapi.picker.api.Audience.LIMITED","limited");_.t("gapi.picker.api.Audience.OWNER_ONLY","ownerOnly");
_.t("gapi.picker.api.Document",{gia:"audience",mia:"blobId",Aia:"children",P2:"contentId",Kia:"copyable",fja:"coverPhotoId",ija:"crop",kja:"customerId",nja:"dataUrl",rja:"description",yja:"domainUsersOnly",Aja:"downloadUrl",Hja:"driveSuccess",Fja:"driveError",Dja:"drivesId",Eja:"drivesName",Ija:"email",Jja:"embedUrl",Gka:"iconUrl",mE:"id",Ika:"isLocalProfilePhoto",Jka:"isNew",Kka:"isRoot",Lka:"isShared",Mka:"kansasVersionInfo",Vka:"lastEditedUtc",Wka:"lastModifiedByMeUtc",Xka:"lastViewedByMeUtc",
Yka:"latitude",fla:"longitude",nla:"markedForRemoval",pla:"mediaKey",tla:"mimeType",Lla:"name",Ula:"numChildren",Vla:"numTagged",Wla:"numUntagged",dma:"organizationDisplayName",ema:"organizeIntoTeamDrive",gma:"otherParents",ama:"ogv",jma:"parentId",qE:"people",qma:"placeId",READ_ONLY:"readOnly",Kma:"rpt",Lma:"rptn",Pma:"resourceKey",Uma:"rotation",Vma:"rotationDegree",jna:"serviceId",Bna:"sizeBytes",Jna:"sourceTeamDriveId",Kna:"sourceTeamDriveName",eoa:"teamDriveId",foa:"teamDriveName",goa:"teamMembersOnly",
ioa:"thumbnails",TYPE:"type",noa:"undoable",poa:"uploadId",qoa:"uploadMetadata",toa:"uploadState",URL:"url",VERSION:"version",Ioa:"visibility"});_.t("gapi.picker.api.Document.ADDRESS_LINES","addressLines");_.t("gapi.picker.api.Document.AUDIENCE","audience");_.t("gapi.picker.api.Document.DESCRIPTION","description");_.t("gapi.picker.api.Document.DURATION","duration");_.t("gapi.picker.api.Document.EMBEDDABLE_URL","embedUrl");_.t("gapi.picker.api.Document.ICON_URL","iconUrl");
_.t("gapi.picker.api.Document.ID","id");_.t("gapi.picker.api.Document.IS_NEW","isNew");_.t("gapi.picker.api.Document.LAST_EDITED_UTC","lastEditedUtc");_.t("gapi.picker.api.Document.LATITUDE","latitude");_.t("gapi.picker.api.Document.LONGITUDE","longitude");_.t("gapi.picker.api.Document.MIME_TYPE","mimeType");_.t("gapi.picker.api.Document.NAME","name");_.t("gapi.picker.api.Document.NUM_CHILDREN","numChildren");_.t("gapi.picker.api.Document.PARENT_ID","parentId");
_.t("gapi.picker.api.Document.PHONE_NUMBERS","phoneNumbers");_.t("gapi.picker.api.Document.SERVICE_ID","serviceId");_.t("gapi.picker.api.Document.THUMBNAILS","thumbnails");_.t("gapi.picker.api.Document.TYPE","type");_.t("gapi.picker.api.Document.URL","url");_.t("gapi.picker.api.DocsUploadView",g9);g9.prototype.setIncludeFolders=g9.prototype.PL;e9.prototype.setParent=g9.prototype.Nb;_.t("gapi.picker.api.DocsView",h9);h9.prototype.setIncludeFolders=h9.prototype.PL;
h9.prototype.setSelectFolderEnabled=h9.prototype.zga;h9.prototype.setMode=h9.prototype.XC;e9.prototype.setParent=h9.prototype.Nb;h9.prototype.setOwnedByMe=h9.prototype.D_;h9.prototype.setStarred=h9.prototype.Q_;h9.prototype.setDocTypesDropDownEnabled=h9.prototype.ega;h9.prototype.setEnableDrives=h9.prototype.fga;h9.prototype.setEnableTeamDrives=h9.prototype.gga;h9.prototype.setFileIds=h9.prototype.kga;_.t("gapi.picker.api.DocsViewMode",{xka:"grid",ala:"list"});
_.t("gapi.picker.api.DocsViewMode.GRID","grid");_.t("gapi.picker.api.DocsViewMode.LIST","list");_.t("gapi.picker.api.Feature",{hja:"cropA11y",Mja:"showAttach",Nja:"edbe",eka:"ftd",kka:"formsEnabled",Bka:"horizNav",Hka:"ignoreLimits",ula:"mineOnly",vla:"minimal",wla:"minew",Ila:"multiselectEnabled",Ola:"navHidden",Qla:"newDriveView",Rla:"newHorizNav",Sla:"newPhotoGridView",bma:"odv",Ama:"profilePhoto",kna:"shadeDialog",pna:"simpleUploadEnabled",Ina:"sawffmi",Zna:"sdr",aoa:"std",voa:"urlInputVisible"});
_.t("gapi.picker.api.Feature.MULTISELECT_ENABLED","multiselectEnabled");_.t("gapi.picker.api.Feature.NAV_HIDDEN","navHidden");_.t("gapi.picker.api.Feature.MINE_ONLY","mineOnly");_.t("gapi.picker.api.Feature.SIMPLE_UPLOAD_ENABLED","simpleUploadEnabled");_.t("gapi.picker.api.Feature.SUPPORT_DRIVES","sdr");_.t("gapi.picker.api.Feature.SUPPORT_TEAM_DRIVES","std");_.t("gapi.picker.api.ImageSearchView",g$);_.t("gapi.picker.api.ImageSearchView.License",{NONE:"*",Rma:"r",Eia:"cr",Sma:"rwm",Fia:"crwm"});
_.t("gapi.picker.api.ImageSearchView.License.COMMERCIAL_REUSE","cr");_.t("gapi.picker.api.ImageSearchView.License.COMMERCIAL_REUSE_WITH_MODIFICATION","crwm");_.t("gapi.picker.api.ImageSearchView.License.NONE","*");_.t("gapi.picker.api.ImageSearchView.License.REUSE","r");_.t("gapi.picker.api.ImageSearchView.License.REUSE_WITH_MODIFICATION","rwm");
_.t("gapi.picker.api.ImageSearchView.Size",{Cna:"qsvga",Ena:"vga",Dna:"svga",Hna:"xga",Fna:"wxga",Gna:"wxga2",vna:"2mp",xna:"4mp",yna:"6mp",Ana:"8mp",qna:"10mp",rna:"12mp",tna:"15mp",una:"20mp",wna:"40mp",zna:"70mp",sna:"140mp"});_.t("gapi.picker.api.ImageSearchView.Size.SIZE_10MP","10mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_12MP","12mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_140MP","140mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_15MP","15mp");
_.t("gapi.picker.api.ImageSearchView.Size.SIZE_20MP","20mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_2MP","2mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_40MP","40mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_4MP","4mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_6MP","6mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_70MP","70mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_8MP","8mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_QSVGA","qsvga");
_.t("gapi.picker.api.ImageSearchView.Size.SIZE_SVGA","svga");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_VGA","vga");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_WXGA","wxga");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_WXGA2","wxga2");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_XGA","xga");g$.prototype.setLicense=g$.prototype.tga;g$.prototype.setSite=g$.prototype.Fw;g$.prototype.setSize=g$.prototype.setSize;g$.prototype.setSafeSearch=g$.prototype.iD;_.t("gapi.picker.api.MapsView",h$);
h$.prototype.setCenter=h$.prototype.setCenter;h$.prototype.setZoom=h$.prototype.setZoom;_.t("gapi.picker.api.PhotoAlbumsView",q$);_.t("gapi.picker.api.PhotosView",i$);_.t("gapi.picker.api.PhotosView.Type",{BANNER:"banner",lia:"bannergallery",wia:"camerasync",aka:"featured",uka:"gmail_themes",gka:"flat",rka:"getty",Aka:"highlights",z4:"mediacollection",yla:"moment",Zla:"ofuser",Rna:"streamid",doa:"tpp",ooa:"uploaded",zoa:"videos-camerasync",Aoa:"videos-uploaded",Poa:"localalbum",Npa:"ytbanner"});
_.t("gapi.picker.api.PhotosView.Type.FEATURED","featured");_.t("gapi.picker.api.PhotosView.Type.GETTY","getty");_.t("gapi.picker.api.PhotosView.Type.UPLOADED","uploaded");e9.prototype.setParent=i$.prototype.Nb;i$.prototype.setType=i$.prototype.Ud;_.t("gapi.picker.api.ResourceId.generate",function(a){return a?[a.mimeType=="application/pdf"?"pdf":dfa[a.serviceId]||"file",":",a.id].join(""):null});
_.t("gapi.picker.api.Response",{Xha:"action",Vja:"extraUserInputs",wja:"docs",ima:"parents",Jx:"view",Goa:"viewToken",yoa:"v2Translated"});_.t("gapi.picker.api.Response.ACTION","action");_.t("gapi.picker.api.Response.DOCUMENTS","docs");_.t("gapi.picker.api.Response.PARENTS","parents");_.t("gapi.picker.api.Response.VIEW","viewToken");
_.t("gapi.picker.api.ServiceId",{pia:"books",A2:"calendar",Gia:"contacts",Jia:"contrib",wka:"gready",gja:"cportal",jja:"cultural",vja:"docs",zja:"photo",Cja:"dragonflyphotos",hE:"drive",Gja:"drive-select",Zja:"feag",ika:"fonts",Rja:"et",qka:"geodiscussion",lla:"maps",mla:"mapspro",ola:"media",lma:"party",nma:"picasa",pma:"places",Jma:"relatedcontent",Ona:"static_themes",Qna:"stories",Mpa:"youtube",Loa:"web",Joa:"vr-assets",qE:"people",dna:"search-api",URL:"url",Hma:"recent",eja:"cosmo",uja:"DoclistBlob",
gE:"doc",eO:"drawing",G3:"form",lka:"freebird",Tka:"kix",x5:"pres",Dma:"punch",Tma:"ritz",EO:"spread"});_.t("gapi.picker.api.ServiceId.DOCS","docs");_.t("gapi.picker.api.ServiceId.MAPS","maps");_.t("gapi.picker.api.ServiceId.PHOTOS","picasa");_.t("gapi.picker.api.ServiceId.SEARCH_API","search-api");_.t("gapi.picker.api.ServiceId.URL","url");_.t("gapi.picker.api.ServiceId.YOUTUBE","youtube");_.t("gapi.picker.api.Thumbnail",{lE:"height",URL:"url",xE:"width",TYPE:"type"});
_.t("gapi.picker.api.Thumbnail.HEIGHT","height");_.t("gapi.picker.api.Thumbnail.WIDTH","width");_.t("gapi.picker.api.Thumbnail.URL","url");_.t("gapi.picker.api.Type",{Zha:"album",bia:"android-app",A2:"calendar",Dia:"chrome-app",CIRCLE:"circle",N2:"contact",gE:"document",hO:"event",Qja:"et",Yja:"faces",dka:"file",jO:"folder",hka:"font",LOCATION:"location",kla:"map",z4:"mediacollection",zO:"person",mma:"photo",URL:"url",LO:"video"});_.t("gapi.picker.api.Type.ALBUM","album");
_.t("gapi.picker.api.Type.DOCUMENT","document");_.t("gapi.picker.api.Type.LOCATION","location");_.t("gapi.picker.api.Type.PHOTO","photo");_.t("gapi.picker.api.Type.URL","url");_.t("gapi.picker.api.Type.VIDEO","video");_.t("gapi.picker.api.VideoSearchView",k$);_.t("gapi.picker.api.VideoSearchView.YOUTUBE","youtube.com");k$.prototype.setSite=k$.prototype.Fw;k$.prototype.setSafeSearch=k$.prototype.iD;_.t("gapi.picker.api.View",e9);e9.prototype.getId=e9.prototype.getId;e9.prototype.setLabel=e9.prototype.zc;
e9.prototype.setMimeTypes=e9.prototype.Mc;e9.prototype.setQuery=e9.prototype.hb;e9.prototype.getQuery=e9.prototype.getQuery;e9.prototype.getLabel=e9.prototype.Uz;_.t("gapi.picker.api.ViewGroup",m$);m$.prototype.addLabel=m$.prototype.ZO;m$.prototype.addView=m$.prototype.Px;m$.prototype.addViewGroup=m$.prototype.Qx;m$.prototype.setCollapsible=m$.prototype.bga;_.t("gapi.picker.api.ViewId.DOCS","all");_.t("gapi.picker.api.ViewId.DOCS_IMAGES","docs-images");
_.t("gapi.picker.api.ViewId.DOCS_IMAGES_AND_VIDEOS","docs-images-and-videos");_.t("gapi.picker.api.ViewId.DOCS_VIDEOS","docs-videos");_.t("gapi.picker.api.ViewId.DOCUMENTS","documents");_.t("gapi.picker.api.ViewId.DRAWINGS","drawings");_.t("gapi.picker.api.ViewId.FOLDERS","folders");_.t("gapi.picker.api.ViewId.FORMS","forms");_.t("gapi.picker.api.ViewId.IMAGE_SEARCH","image-search");_.t("gapi.picker.api.ViewId.MAPS","maps");_.t("gapi.picker.api.ViewId.PDFS","pdfs");
_.t("gapi.picker.api.ViewId.PHOTO_ALBUMS","photo-albums");_.t("gapi.picker.api.ViewId.PHOTOS","photos");_.t("gapi.picker.api.ViewId.PHOTO_UPLOAD","photo-upload");_.t("gapi.picker.api.ViewId.PRESENTATIONS","presentations");_.t("gapi.picker.api.ViewId.RECENTLY_PICKED","recently-picked");_.t("gapi.picker.api.ViewId.SPREADSHEETS","spreadsheets");_.t("gapi.picker.api.ViewId.VIDEO_SEARCH","video-search");_.t("gapi.picker.api.ViewId.WEBCAM","webcam");_.t("gapi.picker.api.ViewId.YOUTUBE","youtube");
_.t("gapi.picker.api.ViewToken",{Eoa:0,Uka:1,Foa:2});_.t("gapi.picker.api.ViewToken.LABEL",1);_.t("gapi.picker.api.ViewToken.VIEW_ID",0);_.t("gapi.picker.api.ViewToken.VIEW_OPTIONS",2);_.t("gapi.picker.api.WebCamView",r$);_.t("gapi.picker.api.WebCamViewType.STANDARD","standard");(_.Xa.google=_.Xa.google||{}).picker=_.Xa.gapi.picker.api;_.t("gapi.picker.api.Picker",b9);b9.prototype.isVisible=_.U4.prototype.isVisible;b9.prototype.setAppId=b9.prototype.qp;b9.prototype.setCallback=b9.prototype.Xr;b9.prototype.setRelayUrl=b9.prototype.Oj;b9.prototype.setVisible=b9.prototype.setVisible;b9.prototype.dispose=b9.prototype.dispose;_.t("gapi.picker.api.PickerBuilder",o$);o$.prototype.addView=o$.prototype.Px;o$.prototype.addViewGroup=o$.prototype.Qx;o$.prototype.build=o$.prototype.Jb;o$.prototype.disableFeature=o$.prototype.t8;
o$.prototype.enableFeature=o$.prototype.N8;o$.prototype.getRelayUrl=o$.prototype.Gu;o$.prototype.getTitle=o$.prototype.getTitle;o$.prototype.hideTitleBar=o$.prototype.pba;o$.prototype.isFeatureEnabled=o$.prototype.fca;o$.prototype.setAppId=o$.prototype.qp;o$.prototype.setAuthUser=o$.prototype.Zfa;o$.prototype.setCallback=o$.prototype.Xr;o$.prototype.setDeveloperKey=o$.prototype.dga;o$.prototype.setDocument=o$.prototype.uL;o$.prototype.setInitialView=o$.prototype.oga;o$.prototype.setLocale=o$.prototype.ds;
o$.prototype.setMaxItems=o$.prototype.uga;o$.prototype.setOAuthToken=o$.prototype.gM;o$.prototype.setOrigin=o$.prototype.Mj;o$.prototype.setRelayUrl=o$.prototype.Oj;o$.prototype.setSelectableMimeTypes=o$.prototype.Aga;o$.prototype.setSize=o$.prototype.setSize;o$.prototype.setTitle=o$.prototype.setTitle;o$.prototype.setUploadToAlbumId=o$.prototype.Fga;o$.prototype.toUri=o$.prototype.g1;(_.Xa.google=_.Xa.google||{}).picker=_.Xa.gapi.picker.api;
});
// Google Inc.
