# Perplexity Web MCP Setup Guide

## 🎯 Quick Start

### 1. Install Dependencies
```bash
cd perplecityWebMCP
npm install
```

### 2. Start MCP Bridge Server
```bash
npm start
# Server will run on ws://localhost:54319
```

### 3. Load Browser Extension

#### Chrome/Edge:
1. Open `chrome://extensions/` (or `edge://extensions/`)
2. Enable "Developer mode" (top-right toggle)
3. Click "Load unpacked"
4. Select the `extension` folder from this project
5. The MCP Tools extension should now be loaded

#### Firefox:
1. Open `about:debugging`
2. Click "This Firefox"
3. Click "Load Temporary Add-on"
4. Select `extension/manifest.json`

### 4. Test the Integration

1. **Navigate to Perplexity**: Go to https://www.perplexity.ai
2. **Look for MCP Interface**: You should see a 🔧 toggle button in the top-right
3. **Click the toggle**: Opens the MCP Tools panel showing connected servers
4. **Test prompt enhancement**: Try prompts with keywords like:
   - "read the latest file in my project"
   - "search for recent changes in the repository" 
   - "analyze the current code structure"
   - "browse the current directory"

## 🔧 System Architecture

```
┌─────────────────┐    WebSocket    ┌─────────────────┐    stdio    ┌─────────────────┐
│   Perplexity    │ ◄────────────► │   MCP Bridge    │ ◄────────► │   MCP Servers   │
│  Web Interface  │   ws://54319    │     Server      │             │  (filesystem)   │
│  + Extension    │                 │   (bridge.js)   │             │                 │
└─────────────────┘                 └─────────────────┘             └─────────────────┘
        │                                   │
        │                                   │
   Content Script                    ┌─────────────────┐
   (content.js)                      │   Config File   │
        │                            │  (config.json)  │
        │                            └─────────────────┘
   Prompt Injection
   MCP Tool Calls
```

## 🎛️ Configuration

### MCP Server Configuration
Edit `config.json` to add/modify MCP servers:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/directory"],
      "env": {}
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_token_here"
      }
    }
  }
}
```

### Adding New MCP Servers
1. Install the MCP server package (usually via `npx`)
2. Add configuration to `config.json`
3. Restart the bridge server (`npm start`)
4. Refresh Perplexity and check the MCP panel

## 🔍 How It Works

### Prompt Enhancement
The system automatically detects when your Perplexity prompts could benefit from MCP tools:

**Trigger Keywords:**
- File operations: `file`, `read`, `write`, `directory`, `folder`, `path`
- Data/API: `api`, `data`, `search`, `database`, `github`, `git`
- Code analysis: `code`, `repository`, `analysis`, `recent`, `latest`
- Real-time: `current`, `real-time`, `live`, `browse`, `fetch`

When detected, the system:
1. **Injects system prompt** with available MCP tools
2. **Provides tool descriptions** and usage instructions
3. **Enables tool execution** via `mcpExecuteTool(serverId, toolName, params)`

### Example Enhanced Prompt
**Original:** "read the latest file in my project"

**Enhanced:**
```
## Available MCP Tools

### read_file (filesystem)
**Description:** Read the complete contents of a file
**Parameters:** {"path": "string - absolute file path"}
**Usage:** To use this tool, call mcpExecuteTool("filesystem", "read_file", {"path": "/path/to/file"})

### list_directory (filesystem)  
**Description:** List directory contents with file types and sizes
**Parameters:** {"path": "string - directory path"}
**Usage:** To use this tool, call mcpExecuteTool("filesystem", "list_directory", {"path": "/path/to/dir"})

## User Query
read the latest file in my project
```

## 🛠️ Troubleshooting

### Common Issues

#### ❌ "Connection failed" in MCP panel
- **Solution**: Ensure bridge server is running (`npm start`)
- **Check**: WebSocket connection on `ws://localhost:54319`

#### ❌ No MCP toggle button visible
- **Solution**: Refresh Perplexity page, check extension is loaded
- **Check**: Extension appears in browser's extension list

#### ❌ "No MCP servers configured"
- **Solution**: Check `config.json` has valid server configurations
- **Check**: MCP server packages are installed

#### ❌ Prompt enhancement not working
- **Solution**: Use trigger keywords in your prompts
- **Check**: Browser console for error messages (F12)

### Debug Mode
Enable detailed logging by opening browser console (F12) on Perplexity:
- Look for `[Perplexity MCP]` log messages
- Check for WebSocket connection status
- Monitor prompt enhancement triggers

## 📁 Project Structure

```
perplecityWebMCP/
├── src/
│   ├── bridge.js          # Main MCP bridge server
│   └── config.js          # Configuration management
├── extension/
│   ├── manifest.json      # Browser extension manifest
│   ├── popup.html         # Extension popup interface
│   ├── css/
│   │   └── mcp-interface.css  # Styling for MCP components
│   └── js/
│       ├── background.js  # Extension background script
│       ├── content.js     # Perplexity page content script
│       └── popup.js       # Popup interface logic
├── bin/
│   └── cli.js            # Command-line interface
├── config.json           # MCP server configurations
├── test-system.js        # System test suite
└── package.json          # Node.js dependencies
```

## 🔐 Security Notes

- **Local operation**: All MCP communication stays on localhost
- **No data collection**: Extension doesn't send data to external services
- **Sandbox restrictions**: MCP servers operate within their configured permissions
- **User consent**: All tool executions require explicit user prompts

## 🚀 Advanced Usage

### Custom MCP Servers
Create your own MCP servers for specialized functionality:
1. Follow the [MCP specification](https://spec.modelcontextprotocol.io/)
2. Add server configuration to `config.json`
3. Test with the bridge system

### Integration with VS Code
Use the CLI tool for VS Code integration:
```bash
node bin/cli.js list-servers
node bin/cli.js call-tool filesystem read_file '{"path": "/path/to/file"}'
```

### Multiple Perplexity Instances
The system supports multiple browser tabs/windows with Perplexity open simultaneously.

## 📊 Performance

- **Startup time**: ~2 seconds for bridge server
- **Connection latency**: <100ms for local WebSocket
- **Memory usage**: ~50MB for bridge + MCP servers
- **Tool execution**: Varies by MCP server and operation

---

**Need help?** Check the project logs, browser console, or create an issue with detailed error information.
