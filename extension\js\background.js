// Background service worker for Perplexity Web MCP Bridge

class McpExtensionBackground {
  constructor() {
    this.isInstalled = false;
    this.bridgeConnection = null;
    this.init();
  }

  init() {
    console.log('[MCP Background] Starting...');

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstall(details);
    });

    // Handle messages from content scripts
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep connection alive for async responses
    });

    // Handle tab updates to inject scripts
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Check bridge connection periodically
    setInterval(() => this.checkBridgeConnection(), 10000);
  }

  handleInstall(details) {
    if (details.reason === 'install') {
      console.log('[MCP Background] Extension installed');
      console.log('[MCP Background] Welcome! Start the bridge with: node bin/cli.js --dev');
    } else if (details.reason === 'update') {
      console.log('[MCP Background] Extension updated');
    }
    this.isInstalled = true;
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'get_status':
          sendResponse({
            status: 'ok',
            bridge_connected: this.bridgeConnection !== null,
            installed: this.isInstalled
          });
          break;

        case 'check_bridge':
          const isConnected = await this.checkBridgeConnection();
          sendResponse({ connected: isConnected });
          break;

        case 'open_options':
          chrome.runtime.openOptionsPage();
          sendResponse({ success: true });
          break;

        case 'bridge_test':
          const testResult = await this.testBridgeConnection();
          sendResponse(testResult);
          break;

        default:
          console.log('[MCP Background] Unknown message type:', message.type);
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('[MCP Background] Message handling error:', error);
      sendResponse({ error: error.message });
    }
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    // Inject content script into Perplexity pages if needed
    if (changeInfo.status === 'complete' &&
      tab.url &&
      (tab.url.includes('perplexity.ai'))) {

      console.log('[MCP Background] Perplexity page loaded:', tab.url);
      this.ensureContentScriptInjected(tabId);
    }
  }

  async ensureContentScriptInjected(tabId) {
    try {
      // Check if content script is already injected
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => !!window.mcpClient
      });

      if (!results[0]?.result) {
        console.log('[MCP Background] Injecting content script');
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['js/content.js']
        });
      }
    } catch (error) {
      console.error('[MCP Background] Failed to inject content script:', error);
    }
  }

  async checkBridgeConnection() {
    try {
      const response = await fetch('http://localhost:54320/health');
      if (response.ok) {
        const data = await response.json();
        this.bridgeConnection = data;
        return true;
      }
    } catch (error) {
      // Bridge not running
    }

    this.bridgeConnection = null;
    return false;
  }

  async testBridgeConnection() {
    try {
      const response = await fetch('http://localhost:54320/health');
      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          status: data.status,
          servers: data.servers,
          clients: data.clients
        };
      } else {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async logBridgeStatus() {
    const isConnected = await this.checkBridgeConnection();

    console.log('[MCP Background] Bridge Status:', isConnected
      ? 'Bridge is running and connected!'
      : 'Bridge not detected. Run: node bin/cli.js --dev');
  }
}

// Initialize background service
const mcpBackground = new McpExtensionBackground();

// Keep service worker alive
chrome.runtime.onSuspend.addListener(() => {
  console.log('[MCP Background] Suspending...');
});

chrome.runtime.onStartup.addListener(() => {
  console.log('[MCP Background] Starting up...');
});

console.log('[MCP Background] Service worker loaded');