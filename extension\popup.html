<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Perplexity MCP Bridge</title>
  <style>
    body {
      width: 350px;
      min-height: 400px;
      margin: 0;
      padding: 0;
      background: #1a1a1a;
      color: #fff;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    }

    .header {
      background: linear-gradient(135deg, #2d2d2d, #1f1f1f);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #333;
      position: relative;
    }

    .settings-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 8px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #fff;
      transition: all 0.2s ease;
    }

    .settings-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    .logo {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .subtitle {
      font-size: 12px;
      color: #888;
    }

    .content {
      padding: 20px;
    }

    .status-section {
      margin-bottom: 24px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #333;
    }

    .status-item:last-child {
      border-bottom: none;
    }

    .status-label {
      font-size: 14px;
      color: #ccc;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #666;
    }

    .status-dot.connected {
      background: #00ff88;
      animation: pulse 2s infinite;
    }

    .status-dot.disconnected {
      background: #ff4444;
    }

    @keyframes pulse {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.5;
      }
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .actions-row {
      display: flex;
      gap: 8px;
    }

    .actions-row .btn {
      flex: 1;
      font-size: 12px;
      padding: 10px 12px;
    }

    .btn {
      background: #404040;
      border: 1px solid #555;
      color: #fff;
      padding: 12px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
      text-decoration: none;
      display: block;
    }

    .btn:hover {
      background: #555;
      border-color: #666;
    }

    .btn:active {
      transform: translateY(1px);
    }

    .btn.primary {
      background: #007bff;
      border-color: #0056b3;
    }

    .btn.primary:hover {
      background: #0056b3;
      border-color: #004085;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .tools-breakdown {
      margin-top: 12px;
      padding: 12px;
      background: #2a2a2a;
      border-radius: 8px;
      border: 1px solid #404040;
    }

    .tools-summary {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .tools-count {
      font-size: 18px;
      font-weight: bold;
      color: #00ff88;
    }

    .tools-label {
      color: #ccc;
      font-size: 12px;
    }

    .servers-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .server-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 8px;
      background: #1f1f1f;
      border-radius: 4px;
      font-size: 11px;
    }

    .server-name {
      color: #00ff88;
      font-weight: 500;
    }

    .server-tools {
      color: #888;
    }

    .connection-help {
      padding: 16px;
      background: #2a1f1f;
      border-radius: 8px;
      border: 1px solid #404040;
      margin-top: 12px;
    }

    .help-title {
      color: #ff6b6b;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .help-text {
      color: #ccc;
      font-size: 12px;
      margin-bottom: 8px;
    }

    .help-command {
      background: #1f1f1f;
      border: 1px solid #333;
      border-radius: 4px;
      padding: 8px 12px;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      color: #00ff88;
      word-break: break-all;
    }

    .status-time {
      font-size: 10px;
      color: #666;
      margin-left: 4px;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .instructions {
      background: #2a2a2a;
      border: 1px solid #404040;
      border-radius: 8px;
      padding: 16px;
      margin-top: 20px;
    }

    .instructions h3 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #00ff88;
    }

    .instructions ol {
      margin: 0;
      padding-left: 20px;
      font-size: 13px;
      line-height: 1.5;
      color: #ccc;
    }

    .instructions li {
      margin-bottom: 8px;
    }

    .code {
      background: #1f1f1f;
      border: 1px solid #333;
      border-radius: 4px;
      padding: 8px 12px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      margin: 8px 0;
      color: #00ff88;
      word-break: break-all;
    }

    .footer {
      padding: 16px 20px;
      border-top: 1px solid #333;
      text-align: center;
      font-size: 11px;
      color: #666;
    }

    .footer a {
      color: #007bff;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .loading {
      text-align: center;
      padding: 20px;
      color: #888;
    }
  </style>
</head>

<body>
  <div class="header">
    <div class="logo">🌉</div>
    <div class="title">Perplexity MCP Bridge</div>
    <div class="subtitle">Connect Perplexity to MCP tools</div>
    <button class="settings-btn" id="settingsBtn" title="Settings">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="3"></circle>
        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
      </svg>
    </button>
  </div>

  <div class="content">
    <div class="status-section" id="statusSection">
      <div class="loading">Loading status...</div>
    </div>

    <div class="actions" id="actionsSection" style="display: none;">
      <button class="btn primary" id="testBridgeBtn">Test Bridge Connection</button>
      <div class="actions-row">
        <button class="btn" id="refreshBtn">Refresh Status</button>
        <button class="btn" id="quickToolsBtn">View Tools</button>
      </div>
      <button class="btn" id="openPerplexityBtn">Open Perplexity.ai</button>
      <button class="btn" id="configBtn">Configuration</button>
    </div>

    <div class="instructions">
      <h3>🚀 Getting Started</h3>
      <ol>
        <li>Install and run the bridge:
          <div class="code">npx perplexity-web-mcp-bridge</div>
        </li>
        <li>Open <a href="https://perplexity.ai" target="_blank">Perplexity.ai</a></li>
        <li>Look for the MCP tools panel</li>
        <li>Start using MCP tools in your conversations!</li>
      </ol>
    </div>
  </div>

  <div class="footer">
    <a href="https://github.com/your-repo/perplexity-web-mcp-bridge" target="_blank">
      Documentation & Support
    </a>
  </div>

  <script src="js/popup.js"></script>
</body>

</html>