const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const chalk = require('chalk');

class McpBridge {
  constructor(options = {}) {
    this.port = options.port || 54319;
    this.config = options.config || {};
    this.verbose = options.verbose || false;
    this.mcpServers = new Map();
    this.wsServer = null;
    this.httpServer = null;
    this.clients = new Set();
  }

  log(message, type = 'info') {
    if (!this.verbose && type === 'debug') return;

    const colors = {
      info: chalk.blue,
      success: chalk.green,
      warn: chalk.yellow,
      error: chalk.red,
      debug: chalk.gray
    };

    console.log(colors[type](`[Bridge] ${message}`));
  }

  async start() {
    // Start HTTP server for health checks
    const app = express();
    app.use(cors());

    app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        servers: Array.from(this.mcpServers.keys()),
        clients: this.clients.size
      });
    });

    this.httpServer = app.listen(this.port + 1);
    this.log(`Health check server on port ${this.port + 1}`, 'success');

    // Start WebSocket server
    this.wsServer = new WebSocket.Server({ port: this.port });
    this.log(`WebSocket server on port ${this.port}`, 'success');

    // Handle WebSocket connections
    this.wsServer.on('connection', (ws) => {
      this.clients.add(ws);
      this.log('Browser extension connected', 'success');

      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data);
          await this.handleMessage(ws, message);
        } catch (error) {
          this.log(`Message error: ${error.message}`, 'error');
          ws.send(JSON.stringify({
            id: message?.id,
            error: { code: -1, message: error.message }
          }));
        }
      });

      ws.on('close', () => {
        this.clients.delete(ws);
        this.log('Browser extension disconnected', 'warn');
      });

      // Send initial server list
      ws.send(JSON.stringify({
        type: 'servers',
        servers: this.getServerList()
      }));
    });

    // Start MCP servers
    await this.startMcpServers();
  }

  async stop() {
    // Stop MCP servers
    for (const [name, server] of this.mcpServers) {
      this.log(`Stopping MCP server: ${name}`, 'warn');
      if (server.process) {
        server.process.kill();
      }
    }

    // Close WebSocket server
    if (this.wsServer) {
      this.wsServer.close();
    }

    // Close HTTP server
    if (this.httpServer) {
      this.httpServer.close();
    }
  }

  async startMcpServers() {
    const servers = this.config.mcpServers || {};

    for (const [name, serverConfig] of Object.entries(servers)) {
      try {
        await this.startMcpServer(name, serverConfig);
      } catch (error) {
        this.log(`Failed to start ${name}: ${error.message}`, 'error');
      }
    }
  }

  async startMcpServer(name, config) {
    this.log(`Starting MCP server: ${name}`, 'info');

    if (config.type === 'sse') {
      // HTTP/SSE server
      this.mcpServers.set(name, {
        type: 'sse',
        url: config.url,
        config: config
      });
    } else {
      // STDIO server
      const spawnOptions = {
        env: { ...process.env, ...config.env },
        stdio: ['pipe', 'pipe', 'pipe']
      };

      // On Windows, we need shell: true for .cmd files
      if (process.platform === 'win32' && config.command.endsWith('.cmd')) {
        spawnOptions.shell = true;
      }

      const childProcess = spawn(config.command, config.args || [], spawnOptions);

      const server = {
        type: 'stdio',
        process: childProcess,
        config: config,
        requestId: 0,
        pendingRequests: new Map()
      };

      // Handle server output
      childProcess.stdout.on('data', (data) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        for (const line of lines) {
          try {
            const response = JSON.parse(line);
            this.log(`MCP Response from ${name}: ${JSON.stringify(response)}`, 'debug');
            this.handleMcpResponse(name, response);
          } catch (error) {
            this.log(`Parse error from ${name}: ${error.message}`, 'debug');
            this.log(`Raw data from ${name}: ${line}`, 'debug');
          }
        }
      });

      childProcess.stderr.on('data', (data) => {
        this.log(`${name} stderr: ${data}`, 'debug');
      });

      childProcess.on('exit', (code) => {
        this.log(`${name} exited with code ${code}`, 'warn');
        this.mcpServers.delete(name);
      });

      this.mcpServers.set(name, server);
    }

    this.log(`MCP server ${name} started`, 'success');
  }

  async handleMessage(ws, message) {
    this.log(`Received: ${JSON.stringify(message)}`, 'debug');

    switch (message.type) {
      case 'mcp_request':
        await this.handleMcpRequest(ws, message);
        break;
      case 'list_servers':
        ws.send(JSON.stringify({
          id: message.id,
          type: 'servers',
          servers: this.getServerList()
        }));
        break;
      default:
        throw new Error(`Unknown message type: ${message.type}`);
    }
  }

  async handleMcpRequest(ws, message) {
    const { serverId, request } = message;
    const server = this.mcpServers.get(serverId);

    if (!server) {
      throw new Error(`Server not found: ${serverId}`);
    }

    if (server.type === 'stdio') {
      // Send to STDIO server
      const requestId = ++server.requestId;
      const mcpRequest = {
        ...request,
        id: requestId
      };

      server.pendingRequests.set(requestId, { ws, originalId: message.id });
      server.process.stdin.write(JSON.stringify(mcpRequest) + '\n');
    } else if (server.type === 'sse') {
      // Send to HTTP/SSE server
      const fetch = require('node-fetch');
      const response = await fetch(server.url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      });

      const result = await response.json();
      ws.send(JSON.stringify({
        id: message.id,
        type: 'mcp_response',
        result: result
      }));
    }
  }

  handleMcpResponse(serverId, response) {
    this.log(`Handling MCP response from ${serverId}: ${JSON.stringify(response)}`, 'debug');
    const server = this.mcpServers.get(serverId);
    if (!server || !server.pendingRequests) {
      this.log(`No server or pending requests for ${serverId}`, 'debug');
      return;
    }

    const pending = server.pendingRequests.get(response.id);
    if (!pending) {
      this.log(`No pending request for ID ${response.id} on ${serverId}`, 'debug');
      return;
    }

    server.pendingRequests.delete(response.id);

    const wsResponse = {
      id: pending.originalId,
      type: 'mcp_response',
      result: response.result || response,
      error: response.error
    };

    this.log(`Sending WebSocket response: ${JSON.stringify(wsResponse)}`, 'debug');
    pending.ws.send(JSON.stringify(wsResponse));
  }

  getServerList() {
    return Array.from(this.mcpServers.entries()).map(([name, server]) => ({
      id: name,
      name: name,
      type: server.type,
      status: server.process ? 'running' : 'connected'
    }));
  }
}

module.exports = McpBridge;