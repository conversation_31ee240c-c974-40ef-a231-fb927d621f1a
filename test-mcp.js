#!/usr/bin/env node

// Test script for MCP system
console.log('🔧 MCP System Test Script');
console.log('========================\n');

// Check if the MCP server is running
const WebSocket = require('ws');

async function testMcpConnection() {
    console.log('1. Testing MCP Bridge Connection...');

    try {
        const ws = new WebSocket('ws://localhost:54319');

        ws.on('open', () => {
            console.log('✅ MCP Bridge is running on ws://localhost:54319');

            // Test server listing
            ws.send(JSON.stringify({
                id: 1,
                type: 'list_servers'
            }));
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log('📨 Received:', message);

                if (message.type === 'servers') {
                    console.log(`✅ Found ${message.servers.length} MCP servers:`);
                    message.servers.forEach(server => {
                        console.log(`   - ${server.name} (${server.id}): ${server.status}`);
                    });
                }
            } catch (error) {
                console.log('📨 Raw message:', data.toString());
            }

            ws.close();
        });

        ws.on('error', (error) => {
            console.log('❌ MCP Bridge connection failed:', error.message);
            console.log('   Make sure to run: node src/bridge.js');
        });

        ws.on('close', () => {
            console.log('🔌 Connection closed\n');
            testExtensionFiles();
        });

    } catch (error) {
        console.log('❌ Failed to connect to MCP Bridge:', error.message);
        testExtensionFiles();
    }
}

function testExtensionFiles() {
    console.log('2. Checking Extension Files...');

    const fs = require('fs');
    const path = require('path');

    const requiredFiles = [
        'extension/manifest.json',
        'extension/js/content.js',
        'extension/js/background.js',
        'extension/js/popup.js',
        'extension/popup.html',
        'extension/css/mcp-interface.css'
    ];

    let allFilesExist = true;

    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file}`);
        } else {
            console.log(`❌ ${file} - MISSING`);
            allFilesExist = false;
        }
    });

    if (allFilesExist) {
        console.log('✅ All extension files present');
    } else {
        console.log('❌ Some extension files are missing');
    }

    console.log('\n3. Next Steps:');
    console.log('   1. Make sure MCP Bridge is running: node src/bridge.js');
    console.log('   2. Load extension in Chrome: chrome://extensions/');
    console.log('   3. Enable Developer mode and click "Load unpacked"');
    console.log('   4. Select the "extension" folder');
    console.log('   5. Open Perplexity.ai and check console for MCP logs');
    console.log('   6. Look for the debug panel in top-right corner');
    console.log('   7. Use test page: open test-injection.html in browser');
}

// Run tests
testMcpConnection();
