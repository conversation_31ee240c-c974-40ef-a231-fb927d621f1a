<!DOCTYPE html>
<!-- saved from url=(0026)https://www.perplexity.ai/ -->
<html lang="en" data-color-scheme="dark">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style type="text/css">
    :root,
    :host {
      --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";
      --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";
      --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";
      --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";
      --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";
      --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 6 Duotone";
      --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 6 Duotone";
      --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 6 Duotone";
      --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";
      --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";
      --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";
      --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";
      --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";
      --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";
      --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 6 Sharp Duotone";
      --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 6 Sharp Duotone";
      --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 6 Sharp Duotone";
    }

    svg:not(:root).svg-inline--fa,
    svg:not(:host).svg-inline--fa {
      overflow: visible;
      box-sizing: content-box;
    }

    .svg-inline--fa {
      display: var(--fa-display, inline-block);
      height: 1em;
      overflow: visible;
      vertical-align: -0.125em;
    }

    .svg-inline--fa.fa-2xs {
      vertical-align: 0.1em;
    }

    .svg-inline--fa.fa-xs {
      vertical-align: 0em;
    }

    .svg-inline--fa.fa-sm {
      vertical-align: -0.0714285705em;
    }

    .svg-inline--fa.fa-lg {
      vertical-align: -0.2em;
    }

    .svg-inline--fa.fa-xl {
      vertical-align: -0.25em;
    }

    .svg-inline--fa.fa-2xl {
      vertical-align: -0.3125em;
    }

    .svg-inline--fa.fa-pull-left {
      margin-right: var(--fa-pull-margin, 0.3em);
      width: auto;
    }

    .svg-inline--fa.fa-pull-right {
      margin-left: var(--fa-pull-margin, 0.3em);
      width: auto;
    }

    .svg-inline--fa.fa-li {
      width: var(--fa-li-width, 2em);
      top: 0.25em;
    }

    .svg-inline--fa.fa-fw {
      width: var(--fa-fw-width, 1.25em);
    }

    .fa-layers svg.svg-inline--fa {
      bottom: 0;
      left: 0;
      margin: auto;
      position: absolute;
      right: 0;
      top: 0;
    }

    .fa-layers-counter,
    .fa-layers-text {
      display: inline-block;
      position: absolute;
      text-align: center;
    }

    .fa-layers {
      display: inline-block;
      height: 1em;
      position: relative;
      text-align: center;
      vertical-align: -0.125em;
      width: 1em;
    }

    .fa-layers svg.svg-inline--fa {
      transform-origin: center center;
    }

    .fa-layers-text {
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      transform-origin: center center;
    }

    .fa-layers-counter {
      background-color: var(--fa-counter-background-color, #ff253a);
      border-radius: var(--fa-counter-border-radius, 1em);
      box-sizing: border-box;
      color: var(--fa-inverse, #fff);
      line-height: var(--fa-counter-line-height, 1);
      max-width: var(--fa-counter-max-width, 5em);
      min-width: var(--fa-counter-min-width, 1.5em);
      overflow: hidden;
      padding: var(--fa-counter-padding, 0.25em 0.5em);
      right: var(--fa-right, 0);
      text-overflow: ellipsis;
      top: var(--fa-top, 0);
      transform: scale(var(--fa-counter-scale, 0.25));
      transform-origin: top right;
    }

    .fa-layers-bottom-right {
      bottom: var(--fa-bottom, 0);
      right: var(--fa-right, 0);
      top: auto;
      transform: scale(var(--fa-layers-scale, 0.25));
      transform-origin: bottom right;
    }

    .fa-layers-bottom-left {
      bottom: var(--fa-bottom, 0);
      left: var(--fa-left, 0);
      right: auto;
      top: auto;
      transform: scale(var(--fa-layers-scale, 0.25));
      transform-origin: bottom left;
    }

    .fa-layers-top-right {
      top: var(--fa-top, 0);
      right: var(--fa-right, 0);
      transform: scale(var(--fa-layers-scale, 0.25));
      transform-origin: top right;
    }

    .fa-layers-top-left {
      left: var(--fa-left, 0);
      right: auto;
      top: var(--fa-top, 0);
      transform: scale(var(--fa-layers-scale, 0.25));
      transform-origin: top left;
    }

    .fa-1x {
      font-size: 1em;
    }

    .fa-2x {
      font-size: 2em;
    }

    .fa-3x {
      font-size: 3em;
    }

    .fa-4x {
      font-size: 4em;
    }

    .fa-5x {
      font-size: 5em;
    }

    .fa-6x {
      font-size: 6em;
    }

    .fa-7x {
      font-size: 7em;
    }

    .fa-8x {
      font-size: 8em;
    }

    .fa-9x {
      font-size: 9em;
    }

    .fa-10x {
      font-size: 10em;
    }

    .fa-2xs {
      font-size: 0.625em;
      line-height: 0.1em;
      vertical-align: 0.225em;
    }

    .fa-xs {
      font-size: 0.75em;
      line-height: 0.0833333337em;
      vertical-align: 0.125em;
    }

    .fa-sm {
      font-size: 0.875em;
      line-height: 0.0714285718em;
      vertical-align: 0.0535714295em;
    }

    .fa-lg {
      font-size: 1.25em;
      line-height: 0.05em;
      vertical-align: -0.075em;
    }

    .fa-xl {
      font-size: 1.5em;
      line-height: 0.0416666682em;
      vertical-align: -0.125em;
    }

    .fa-2xl {
      font-size: 2em;
      line-height: 0.03125em;
      vertical-align: -0.1875em;
    }

    .fa-fw {
      text-align: center;
      width: 1.25em;
    }

    .fa-ul {
      list-style-type: none;
      margin-left: var(--fa-li-margin, 2.5em);
      padding-left: 0;
    }

    .fa-ul>li {
      position: relative;
    }

    .fa-li {
      left: calc(-1 * var(--fa-li-width, 2em));
      position: absolute;
      text-align: center;
      width: var(--fa-li-width, 2em);
      line-height: inherit;
    }

    .fa-border {
      border-color: var(--fa-border-color, #eee);
      border-radius: var(--fa-border-radius, 0.1em);
      border-style: var(--fa-border-style, solid);
      border-width: var(--fa-border-width, 0.08em);
      padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
    }

    .fa-pull-left {
      float: left;
      margin-right: var(--fa-pull-margin, 0.3em);
    }

    .fa-pull-right {
      float: right;
      margin-left: var(--fa-pull-margin, 0.3em);
    }

    .fa-beat {
      animation-name: fa-beat;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, ease-in-out);
    }

    .fa-bounce {
      animation-name: fa-bounce;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
    }

    .fa-fade {
      animation-name: fa-fade;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
    }

    .fa-beat-fade {
      animation-name: fa-beat-fade;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
    }

    .fa-flip {
      animation-name: fa-flip;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, ease-in-out);
    }

    .fa-shake {
      animation-name: fa-shake;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, linear);
    }

    .fa-spin {
      animation-name: fa-spin;
      animation-delay: var(--fa-animation-delay, 0s);
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 2s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, linear);
    }

    .fa-spin-reverse {
      --fa-animation-direction: reverse;
    }

    .fa-pulse,
    .fa-spin-pulse {
      animation-name: fa-spin;
      animation-direction: var(--fa-animation-direction, normal);
      animation-duration: var(--fa-animation-duration, 1s);
      animation-iteration-count: var(--fa-animation-iteration-count, infinite);
      animation-timing-function: var(--fa-animation-timing, steps(8));
    }

    @media (prefers-reduced-motion: reduce) {

      .fa-beat,
      .fa-bounce,
      .fa-fade,
      .fa-beat-fade,
      .fa-flip,
      .fa-pulse,
      .fa-shake,
      .fa-spin,
      .fa-spin-pulse {
        animation-delay: -1ms;
        animation-duration: 1ms;
        animation-iteration-count: 1;
        transition-delay: 0s;
        transition-duration: 0s;
      }
    }

    @keyframes fa-beat {

      0%,
      90% {
        transform: scale(1);
      }

      45% {
        transform: scale(var(--fa-beat-scale, 1.25));
      }
    }

    @keyframes fa-bounce {
      0% {
        transform: scale(1, 1) translateY(0);
      }

      10% {
        transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
      }

      30% {
        transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
      }

      50% {
        transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
      }

      57% {
        transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
      }

      64% {
        transform: scale(1, 1) translateY(0);
      }

      100% {
        transform: scale(1, 1) translateY(0);
      }
    }

    @keyframes fa-fade {
      50% {
        opacity: var(--fa-fade-opacity, 0.4);
      }
    }

    @keyframes fa-beat-fade {

      0%,
      100% {
        opacity: var(--fa-beat-fade-opacity, 0.4);
        transform: scale(1);
      }

      50% {
        opacity: 1;
        transform: scale(var(--fa-beat-fade-scale, 1.125));
      }
    }

    @keyframes fa-flip {
      50% {
        transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
      }
    }

    @keyframes fa-shake {
      0% {
        transform: rotate(-15deg);
      }

      4% {
        transform: rotate(15deg);
      }

      8%,
      24% {
        transform: rotate(-18deg);
      }

      12%,
      28% {
        transform: rotate(18deg);
      }

      16% {
        transform: rotate(-22deg);
      }

      20% {
        transform: rotate(22deg);
      }

      32% {
        transform: rotate(-12deg);
      }

      36% {
        transform: rotate(12deg);
      }

      40%,
      100% {
        transform: rotate(0deg);
      }
    }

    @keyframes fa-spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .fa-rotate-90 {
      transform: rotate(90deg);
    }

    .fa-rotate-180 {
      transform: rotate(180deg);
    }

    .fa-rotate-270 {
      transform: rotate(270deg);
    }

    .fa-flip-horizontal {
      transform: scale(-1, 1);
    }

    .fa-flip-vertical {
      transform: scale(1, -1);
    }

    .fa-flip-both,
    .fa-flip-horizontal.fa-flip-vertical {
      transform: scale(-1, -1);
    }

    .fa-rotate-by {
      transform: rotate(var(--fa-rotate-angle, 0));
    }

    .fa-stack {
      display: inline-block;
      vertical-align: middle;
      height: 2em;
      position: relative;
      width: 2.5em;
    }

    .fa-stack-1x,
    .fa-stack-2x {
      bottom: 0;
      left: 0;
      margin: auto;
      position: absolute;
      right: 0;
      top: 0;
      z-index: var(--fa-stack-z-index, auto);
    }

    .svg-inline--fa.fa-stack-1x {
      height: 1em;
      width: 1.25em;
    }

    .svg-inline--fa.fa-stack-2x {
      height: 2em;
      width: 2.5em;
    }

    .fa-inverse {
      color: var(--fa-inverse, #fff);
    }

    .sr-only,
    .fa-sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border-width: 0;
    }

    .sr-only-focusable:not(:focus),
    .fa-sr-only-focusable:not(:focus) {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border-width: 0;
    }

    .svg-inline--fa .fa-primary {
      fill: var(--fa-primary-color, currentColor);
      opacity: var(--fa-primary-opacity, 1);
    }

    .svg-inline--fa .fa-secondary {
      fill: var(--fa-secondary-color, currentColor);
      opacity: var(--fa-secondary-opacity, 0.4);
    }

    .svg-inline--fa.fa-swap-opacity .fa-primary {
      opacity: var(--fa-secondary-opacity, 0.4);
    }

    .svg-inline--fa.fa-swap-opacity .fa-secondary {
      opacity: var(--fa-primary-opacity, 1);
    }

    .svg-inline--fa mask .fa-primary,
    .svg-inline--fa mask .fa-secondary {
      fill: black;
    }
  </style>
  <link rel="preload" as="image" href="./Perplexity_files/thumbnail">
  <link rel="stylesheet" href="./Perplexity_files/5d9e8c7911b8604f.css" data-precedence="next">
  <link rel="stylesheet" href="./Perplexity_files/908b88a79b4232ac.css" data-precedence="next">
  <link rel="stylesheet" href="./Perplexity_files/e4e6af58e8daa9de.css" data-precedence="next">
  <link rel="preload" as="script" fetchpriority="low" href="./Perplexity_files/webpack-8ceae12d4aebc5a8.js.download">
  <script src="./Perplexity_files/cb=gapi.loaded_0" async=""></script>
  <script src="./Perplexity_files/54429-f5d3ce4e237f97e6.js.download" async=""></script>
  <script src="./Perplexity_files/main-app-9bc4750e2b51a297.js.download" async=""></script>
  <script src="./Perplexity_files/46397-cbdc9f1490caf6f3.js.download" async=""></script>
  <script src="./Perplexity_files/layout-21d4c68965419bd6.js.download" async=""></script>
  <script src="./Perplexity_files/client"
    data-client_id="***********-30175ip7vg79fobh0rk1sur3pdutj9l1.apps.googleusercontent.com"
    data-use_fedcm_for_prompt="true" async="" defer=""></script>
  <meta name="next-size-adjust" content="">
  <meta name="theme-color" media="(prefers-color-scheme: light)" content="#FCFCF9">
  <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#100E12">
  <script>
    window.__PPL_CONFIG__ = { "env": "beta", "version": "11088d5", "country": "FI", "isInternal": false, "noDDSampling": false, "blockO11y": false };
    const isComet = document.cookie.includes('comet_browser=');
    let erp;
    if (window.location.pathname.startsWith('/sidecar')) {
      erp = 'sidecar';
    } else if (isComet) {
      erp = 'tab';
    }
    if (erp) {
      window.__PPL_CONFIG__.erp = erp;
      document.documentElement.dataset.erp = erp;
      if (erp === 'sidecar') {
        document.head.insertAdjacentHTML('beforeend', '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />');
      }
    }
  </script>
  <title>Perplexity</title>
  <title>Perplexity</title>
  <meta name="description"
    content="Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.">
  <meta name="robots" content="index">
  <link rel="canonical" href="https://www.perplexity.ai/">
  <meta property="og:title" content="Perplexity">
  <meta property="og:description"
    content="Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.">
  <meta property="og:url" content="https://www.perplexity.ai">
  <meta property="og:site_name" content="Perplexity AI">
  <meta property="og:locale" content="en_US">
  <meta property="og:image" content="https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@perplexity_ai">
  <meta name="twitter:title" content="Perplexity">
  <meta name="twitter:description"
    content="Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.">
  <meta name="twitter:image" content="https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png">
  <script src="./Perplexity_files/polyfills-42372ed130431b0a.js.download" nomodule=""></script>
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/18a8383f82b767c1-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/24437d246c9412ba-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/2ba03bcf4600f506-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/3c0a20a48548ae37-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/404d16a48cfd5dcd-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/80d90e1e722a80dc-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/b06d54d0a237533a-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/b8107bede19baa40-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <link rel="preload" href="https://pplx-next-static-public.perplexity.ai/_next/static/media/e8d05d87005ce07f-s.p.woff2"
    as="font" crossorigin="" type="font/woff2">
  <meta name="description"
    content="Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.">
  <meta name="robots" content="index">
  <link rel="canonical" href="https://www.perplexity.ai/">
  <meta property="og:title" content="Perplexity">
  <meta property="og:description"
    content="Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.">
  <meta property="og:url" content="https://www.perplexity.ai">
  <meta property="og:site_name" content="Perplexity AI">
  <meta property="og:locale" content="en_US">
  <meta property="og:image" content="https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@perplexity_ai">
  <meta name="twitter:title" content="Perplexity">
  <meta name="twitter:description"
    content="Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.">
  <meta name="twitter:image" content="https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png">
  <style id="googleidentityservice_button_styles">
    .qJTHM {
      -webkit-user-select: none;
      color: #202124;
      direction: ltr;
      -webkit-touch-callout: none;
      font-family: "Roboto-Regular", arial, sans-serif;
      -webkit-font-smoothing: antialiased;
      font-weight: 400;
      margin: 0;
      overflow: hidden;
      -webkit-text-size-adjust: 100%
    }

    .ynRLnc {
      left: -9999px;
      position: absolute;
      top: -9999px
    }

    .L6cTce {
      display: none
    }

    .bltWBb {
      word-break: break-all
    }

    .hSRGPd {
      color: #1a73e8;
      cursor: pointer;
      font-weight: 500;
      text-decoration: none
    }

    .Bz112c-W3lGp {
      height: 16px;
      width: 16px
    }

    .Bz112c-E3DyYd {
      height: 20px;
      width: 20px
    }

    .Bz112c-r9oPif {
      height: 24px;
      width: 24px
    }

    .Bz112c-r4WDKb {
      height: 42px;
      width: 42px
    }

    .Bz112c-uaxL4e {
      -webkit-border-radius: 10px;
      border-radius: 10px
    }

    .LgbsSe-Bz112c {
      display: block
    }

    .S9gUrf-YoZ4jf,
    .S9gUrf-YoZ4jf * {
      border: none;
      margin: 0;
      padding: 0
    }

    .fFW7wc-ibnC6b>.aZ2wEe>div {
      border-color: #4285f4
    }

    .P1ekSe-ZMv3u>div:nth-child(1) {
      background-color: #1a73e8 !important
    }

    .P1ekSe-ZMv3u>div:nth-child(2),
    .P1ekSe-ZMv3u>div:nth-child(3) {
      background-image: linear-gradient(to right, rgba(255, 255, 255, .7), rgba(255, 255, 255, .7)), linear-gradient(to right, #1a73e8, #1a73e8) !important
    }

    .haAclf {
      display: inline-block
    }

    .nsm7Bb-HzV7m-LgbsSe {
      -webkit-border-radius: 4px;
      border-radius: 4px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      -webkit-transition: background-color .218s, border-color .218s;
      transition: background-color .218s, border-color .218s;
      -webkit-user-select: none;
      -webkit-appearance: none;
      background-color: #fff;
      background-image: none;
      border: 1px solid #dadce0;
      color: #3c4043;
      cursor: pointer;
      font-family: "Google Sans", arial, sans-serif;
      font-size: 14px;
      height: 40px;
      letter-spacing: 0.25px;
      outline: none;
      overflow: hidden;
      padding: 0 12px;
      position: relative;
      text-align: center;
      vertical-align: middle;
      white-space: nowrap;
      width: auto
    }

    @media screen and (-ms-high-contrast:active) {
      .nsm7Bb-HzV7m-LgbsSe {
        border: 2px solid windowText;
        color: windowText
      }
    }

    .nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe {
      font-size: 14px;
      height: 32px;
      letter-spacing: 0.25px;
      padding: 0 10px
    }

    .nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe {
      font-size: 11px;
      height: 20px;
      letter-spacing: 0.3px;
      padding: 0 8px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe {
      padding: 0;
      width: 40px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.pSzOP-SxQuSe {
      width: 32px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.purZT-SxQuSe {
      width: 20px
    }

    .nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK {
      -webkit-border-radius: 20px;
      border-radius: 20px
    }

    .nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK.pSzOP-SxQuSe {
      -webkit-border-radius: 16px;
      border-radius: 16px
    }

    .nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK.purZT-SxQuSe {
      -webkit-border-radius: 10px;
      border-radius: 10px
    }

    .nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc {
      border: none;
      color: #fff
    }

    .nsm7Bb-HzV7m-LgbsSe.MFS4be-v3pZbf-Ia7Qfc {
      background-color: #1a73e8
    }

    .nsm7Bb-HzV7m-LgbsSe.MFS4be-JaPV2b-Ia7Qfc {
      background-color: #202124;
      color: #e8eaed
    }

    .nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      height: 18px;
      margin-right: 8px;
      min-width: 18px;
      width: 18px
    }

    .nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      height: 14px;
      min-width: 14px;
      width: 14px
    }

    .nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      height: 10px;
      min-width: 10px;
      width: 10px
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      margin-left: 8px;
      margin-right: -4px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      margin: 0;
      padding: 10px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      padding: 8px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      padding: 4px
    }

    .nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-top-left-radius: 3px;
      border-top-left-radius: 3px;
      -webkit-border-bottom-left-radius: 3px;
      border-bottom-left-radius: 3px;
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      justify-content: center;
      -webkit-align-items: center;
      align-items: center;
      background-color: #fff;
      height: 36px;
      margin-left: -10px;
      margin-right: 12px;
      min-width: 36px;
      width: 36px
    }

    .nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf .nsm7Bb-HzV7m-LgbsSe-Bz112c,
    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf .nsm7Bb-HzV7m-LgbsSe-Bz112c {
      margin: 0;
      padding: 0
    }

    .nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      height: 28px;
      margin-left: -8px;
      margin-right: 10px;
      min-width: 28px;
      width: 28px
    }

    .nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      height: 16px;
      margin-left: -6px;
      margin-right: 8px;
      min-width: 16px;
      width: 16px
    }

    .nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-radius: 3px;
      border-radius: 3px;
      margin-left: 2px;
      margin-right: 0;
      padding: 0
    }

    .nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-radius: 18px;
      border-radius: 18px
    }

    .nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-radius: 14px;
      border-radius: 14px
    }

    .nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-radius: 8px;
      border-radius: 8px
    }

    .nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb {
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: center;
      align-items: center;
      -webkit-flex-direction: row;
      flex-direction: row;
      justify-content: space-between;
      -webkit-flex-wrap: nowrap;
      flex-wrap: nowrap;
      height: 100%;
      position: relative;
      width: 100%
    }

    .nsm7Bb-HzV7m-LgbsSe .oXtfBe-l4eHX {
      justify-content: center
    }

    .nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-BPrWId {
      -webkit-flex-grow: 1;
      flex-grow: 1;
      font-family: "Google Sans", arial, sans-serif;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: top
    }

    .nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-BPrWId {
      font-weight: 300
    }

    .nsm7Bb-HzV7m-LgbsSe .oXtfBe-l4eHX .nsm7Bb-HzV7m-LgbsSe-BPrWId {
      -webkit-flex-grow: 0;
      flex-grow: 0
    }

    .nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-MJoBVe {
      -webkit-transition: background-color .218s;
      transition: background-color .218s;
      bottom: 0;
      left: 0;
      position: absolute;
      right: 0;
      top: 0
    }

    .nsm7Bb-HzV7m-LgbsSe:hover,
    .nsm7Bb-HzV7m-LgbsSe:focus {
      -webkit-box-shadow: none;
      box-shadow: none;
      border-color: rgb(210, 227, 252);
      outline: none
    }

    .nsm7Bb-HzV7m-LgbsSe:focus-within {
      outline: 2px solid #202124;
      border-color: transparent
    }

    .nsm7Bb-HzV7m-LgbsSe:hover .nsm7Bb-HzV7m-LgbsSe-MJoBVe {
      background: rgba(66, 133, 244, .08)
    }

    .nsm7Bb-HzV7m-LgbsSe:active .nsm7Bb-HzV7m-LgbsSe-MJoBVe,
    .nsm7Bb-HzV7m-LgbsSe:focus .nsm7Bb-HzV7m-LgbsSe-MJoBVe {
      background: rgba(66, 133, 244, .1)
    }

    .nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:hover .nsm7Bb-HzV7m-LgbsSe-MJoBVe {
      background: rgba(255, 255, 255, .24)
    }

    .nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:active .nsm7Bb-HzV7m-LgbsSe-MJoBVe,
    .nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:focus .nsm7Bb-HzV7m-LgbsSe-MJoBVe {
      background: rgba(255, 255, 255, .32)
    }

    .nsm7Bb-HzV7m-LgbsSe .n1UuX-DkfjY {
      -webkit-border-radius: 50%;
      border-radius: 50%;
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      height: 20px;
      margin-left: -4px;
      margin-right: 8px;
      min-width: 20px;
      width: 20px
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId {
      font-family: "Roboto";
      font-size: 12px;
      text-align: left
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .ssJRIf,
    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff .fmcmS {
      overflow: hidden;
      text-overflow: ellipsis
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff {
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: center;
      align-items: center;
      color: #5f6368;
      fill: #5f6368;
      font-size: 11px;
      font-weight: 400
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe.MFS4be-Ia7Qfc .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff {
      color: #e8eaed;
      fill: #e8eaed
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff .Bz112c {
      height: 18px;
      margin: -3px -3px -3px 2px;
      min-width: 18px;
      width: 18px
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-top-left-radius: 0;
      border-top-left-radius: 0;
      -webkit-border-bottom-left-radius: 0;
      border-bottom-left-radius: 0;
      -webkit-border-top-right-radius: 3px;
      border-top-right-radius: 3px;
      -webkit-border-bottom-right-radius: 3px;
      border-bottom-right-radius: 3px;
      margin-left: 12px;
      margin-right: -10px
    }

    .nsm7Bb-HzV7m-LgbsSe.jVeSEe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf {
      -webkit-border-radius: 18px;
      border-radius: 18px
    }

    .L5Fo6c-sM5MNb {
      border: 0;
      display: block;
      left: 0;
      position: relative;
      top: 0
    }

    .L5Fo6c-bF1uUb {
      -webkit-border-radius: 4px;
      border-radius: 4px;
      bottom: 0;
      cursor: pointer;
      left: 0;
      position: absolute;
      right: 0;
      top: 0
    }

    .L5Fo6c-bF1uUb:focus {
      border: none;
      outline: none
    }

    sentinel {}
  </style>
  <style>
    .picker-dialog-frame {
      width: 100%;
      height: 100%;
      border: 0;
      overflow: hidden
    }

    .picker-dialog-bg {
      position: absolute;
      top: 0;
      left: 0;
      background-color: #fff;
      z-index: 1000
    }

    .picker-dialog {
      position: absolute;
      top: 0;
      left: 0;
      background-color: #fff;
      border: 1px solid #acacac;
      width: auto;
      padding: 0;
      z-index: 1001;
      overflow: auto;
      box-shadow: 0 4px 16px rgba(0, 0, 0, .2)
    }

    .picker-dialog-content {
      height: 100%;
      font-size: 0;
      padding: 0
    }

    .picker-dialog-buttons,
    .picker-dialog-title {
      display: none
    }
  </style>
</head>

<body id="__next"
  class="__variable_598ab8 __variable_dd3642 __variable_8a67e8 __variable_1826c3 __variable_f8d077 bg-transparent md:bg-offset dark:md:bg-offsetDark vsc-initialized">
  <main>
    <div
      class="border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offset dark:bg-offsetDark">
      <div class="isolate flex h-[100dvh]">
        <div class="group/sidebar relative z-10 hidden min-h-0 flex-1 flex-row-reverse md:flex">
          <div
            class="group/sidebar-menu pointer-events-none absolute inset-y-0 left-full -translate-x-px overflow-hidden"
            style="width:200px">
            <div
              class="inset-0 border-r border-transparent pl-[12px] pr-[20px] duration-100 dark:border-transparent absolute flex flex-col erp-tab:pt-[2px] pt-[10px] pointer-events-auto border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offset dark:bg-offsetDark"
              style="width:200px">
              <div
                class="group/sidebar-menu-header relative mb-[12px] flex min-h-0 items-center justify-between border-b py-[12px] border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                <span>
                  <div
                    class="group relative cursor-pointer select-none font-sans text-sm font-medium leading-[1.125rem] text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                    <span
                      class="bg-background-300 -inset-x-sm -inset-y-xs absolute rounded-md opacity-0 duration-150 group-hover:opacity-100"></span><span
                      class="gap-xs relative flex items-center">Home<svg xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round"
                        class="tabler-icon tabler-icon-chevron-down text-text-200/50 group-hover:text-text-200 size-4 opacity-0 duration-150 group-hover/sidebar-menu-header:opacity-100">
                        <path d="M6 9l6 6l6 -6"></path>
                      </svg></span></div>
                </span>
                <div class="flex items-center"><button data-testid="sidebar-pin-sidebar" aria-label="Unpin Sidebar"
                    type="button"
                    class="focus-visible:bg-offsetPlus dark:focus-visible:bg-offsetPlusDark hover:bg-offsetPlus text-textOff dark:text-textOffDark hover:text-textMain dark:hover:bg-offsetPlusDark dark:hover:text-textMainDark !-mr-sm font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-lg cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-xs h-6 aspect-[9/8]"
                    data-state="closed">
                    <div class="flex items-center min-w-0 font-medium gap-1 justify-center">
                      <div class="flex shrink-0 items-center justify-center size-3.5"><svg
                          xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                          fill="currentColor" stroke="none" class="tabler-icon tabler-icon-pinned-filled ">
                          <path
                            d="M16 3a1 1 0 0 1 .117 1.993l-.117 .007v4.764l1.894 3.789a1 1 0 0 1 .1 .331l.006 .116v2a1 1 0 0 1 -.883 .993l-.117 .007h-4v4a1 1 0 0 1 -1.993 .117l-.007 -.117v-4h-4a1 1 0 0 1 -.993 -.883l-.007 -.117v-2a1 1 0 0 1 .06 -.34l.046 -.107l1.894 -3.791v-4.762a1 1 0 0 1 -.117 -1.993l.117 -.007h8z">
                          </path>
                        </svg></div>
                    </div>
                  </button></div>
              </div>
              <div class="relative flex min-h-0 flex-1 flex-col" style="opacity:1;transform:none">
                <div
                  class="scrollbar-thin scrollbar-thumb-idle dark:scrollbar-thumb-idleDark scrollbar-track-transparent -mx-[12px] -mt-[12px] flex min-h-0 flex-1 overflow-y-auto overflow-x-hidden p-[12px]">
                  <div class="w-full">
                    <div class="flex flex-col gap-[12px]">
                      <div class="flex flex-col gap-[10px]"><a class="group relative block"
                          href="https://www.perplexity.ai/finance">
                          <div class="group relative block">
                            <div
                              class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                            </div>
                            <div
                              class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                color="currentColor" class="size-[18px] -translate-y-px" fill="currentColor"
                                fill-rule="evenodd">
                                <path
                                  d="M19.6602 2C20.9486 2.00018 21.993 3.0446 21.9932 4.33301C21.9932 5.62156 20.9487 6.66682 19.6602 6.66699C19.6103 6.66699 19.5608 6.66128 19.5117 6.6582L16.9111 12.3467C16.803 12.5829 16.5903 12.7551 16.3369 12.8125C16.1151 12.8626 15.8841 12.8195 15.6963 12.6963L15.6182 12.6387L11.374 9.08691L5.05176 19.4873H10.333V13.667C10.333 13.2069 10.706 12.8342 11.166 12.834C11.6263 12.834 11.999 13.2068 11.999 13.667V19.4873H15.3203V15.333C15.3205 14.8729 15.6932 14.5 16.1533 14.5C16.6134 14.5001 16.9862 14.873 16.9863 15.333V19.4873H19.5C19.9595 19.4871 20.3328 19.1128 20.333 18.6533V9.49414C20.333 9.03406 20.706 8.66042 21.166 8.66016C21.6263 8.66016 22 9.0339 22 9.49414V18.6533C21.9998 20.0333 20.88 21.1531 19.5 21.1533H4.5C3.2062 21.1532 2.14091 20.1693 2.0127 18.9092L2 18.6533V5.33398C2 3.9538 3.11983 2.83407 4.5 2.83398H14.5L14.585 2.83789C15.0051 2.8806 15.333 3.23555 15.333 3.66699C15.3328 4.09829 15.005 4.4534 14.585 4.49609L14.5 4.5H4.5C4.04031 4.50008 3.66602 4.87427 3.66602 5.33398V18.5586L10.4541 7.39355L10.5059 7.31836C10.6338 7.15201 10.821 7.03859 11.0303 7.00391C11.2698 6.96435 11.515 7.03176 11.7012 7.1875L15.8496 10.6582L17.9951 5.96777C17.5817 5.54678 17.3262 4.96964 17.3262 4.33301C17.3263 3.04449 18.3716 2 19.6602 2Z">
                                </path>
                              </svg><span class="w-full overflow-hidden whitespace-nowrap">Finance</span><span
                                style="mask-image:linear-gradient(to left, black 60%, transparent)"
                                class="pl-md bg-offsetPlus -right-xs absolute top-1/2 -translate-y-1/2 opacity-0 duration-200 group-hover:opacity-100"><span>
                                  <div class="hover:bg-text/5 p-xs rounded duration-150"><svg
                                      xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                      fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
                                      stroke-linejoin="round"
                                      class="tabler-icon tabler-icon-dots text-text-200/50 hover:text-text-200 duration-150">
                                      <path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                      <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                      <path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    </svg></div>
                                </span></span></div>
                          </div>
                        </a><a class="group relative block" href="https://www.perplexity.ai/travel">
                          <div class="group relative block">
                            <div
                              class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                            </div>
                            <div
                              class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                color="currentColor" class="size-[18px] -translate-y-px" fill="currentColor"
                                fill-rule="evenodd">
                                <path
                                  d="M13.7718 4C18.3247 4 21.9993 7.68969 21.9993 12.2275C21.9992 16.7663 18.3105 20.4551 13.7718 20.4551C12.7177 20.455 11.6928 20.2498 10.7679 19.8828L10.7347 19.8701L10.6956 19.8506L10.6888 19.8477L10.7005 19.8506C10.2597 19.7402 9.99093 19.2935 10.1009 18.8525C10.2112 18.4114 10.6588 18.1427 11.0999 18.2529C11.201 18.2782 11.2994 18.3177 11.3802 18.3545C12.1137 18.6448 12.931 18.8086 13.7718 18.8086C17.401 18.8086 20.3527 15.8568 20.3528 12.2275C20.3528 8.59729 17.4133 5.64648 13.7718 5.64648C10.1295 5.64662 7.17822 8.59799 7.17802 12.2402C7.17802 12.6986 7.22473 13.1431 7.31181 13.5674L7.4124 13.9834L7.43974 14.1484C7.4658 14.5306 7.22073 14.892 6.8372 14.998C6.45383 15.1037 6.05889 14.9192 5.88505 14.5781L5.82451 14.4229L5.69853 13.8975C5.58917 13.3644 5.53154 12.8096 5.53154 12.2402C5.53174 7.68849 9.22001 4.00013 13.7718 4ZM10.6888 19.8477C10.6862 19.8469 10.6839 19.8467 10.6839 19.8467H10.6868L10.6888 19.8477ZM16.2591 8.95508C16.2902 8.95596 16.3209 8.96043 16.3519 8.96484L16.4017 8.9707C16.4046 8.9713 16.4075 8.97202 16.4104 8.97266C16.4181 8.97431 16.4253 8.97762 16.4329 8.97949C16.4707 8.98881 16.5077 8.99934 16.5433 9.01367C16.5527 9.01748 16.5613 9.02317 16.5706 9.02734C16.6042 9.04238 16.6372 9.05793 16.6683 9.07715C16.6781 9.08327 16.6869 9.09107 16.6966 9.09766C16.7271 9.11839 16.7571 9.13953 16.7845 9.16406C16.786 9.16545 16.7878 9.16657 16.7894 9.16797C16.8129 9.18943 16.8343 9.21289 16.8548 9.23633C16.8667 9.24994 16.878 9.26394 16.889 9.27832C16.9079 9.30307 16.9259 9.32817 16.9417 9.35449C16.9476 9.36436 16.9528 9.37463 16.9583 9.38477C16.9747 9.41493 16.9889 9.44591 17.0013 9.47754C17.0042 9.48503 17.0073 9.49241 17.0101 9.5C17.0231 9.53641 17.0326 9.5737 17.0403 9.61133C17.0427 9.62301 17.0453 9.63463 17.0472 9.64648C17.0535 9.68596 17.0573 9.72569 17.0579 9.76562C17.058 9.76941 17.0589 9.77354 17.0589 9.77734V13.8877C17.0587 14.3423 16.6902 14.7109 16.2356 14.7109C15.7811 14.7109 15.4126 14.3423 15.4124 13.8877V11.8701C12.0054 15.4487 9.45177 17.4947 7.53252 18.5244C5.5087 19.61 3.92643 19.6815 2.8792 18.8291L2.67705 18.6465C2.04543 18.0149 1.92632 17.1236 2.03642 16.2793C2.14798 15.424 2.50735 14.468 3.03545 13.4951C3.25239 13.0955 3.75298 12.9462 4.15263 13.1631C4.55218 13.38 4.69947 13.8807 4.48271 14.2803C4.01013 15.1509 3.74555 15.9076 3.66923 16.4912C3.59173 17.0854 3.72193 17.3609 3.84111 17.4805C4.09158 17.7309 4.8066 18.118 6.7542 17.0732C8.47468 16.1502 10.9246 14.2091 14.345 10.6006H12.1253C11.6707 10.6006 11.3022 10.2319 11.302 9.77734C11.302 9.32259 11.6705 8.95312 12.1253 8.95312H16.2356L16.2591 8.95508Z">
                                </path>
                              </svg><span class="w-full overflow-hidden whitespace-nowrap">Travel</span><span
                                style="mask-image:linear-gradient(to left, black 60%, transparent)"
                                class="pl-md bg-offsetPlus -right-xs absolute top-1/2 -translate-y-1/2 opacity-0 duration-200 group-hover:opacity-100"><span>
                                  <div class="hover:bg-text/5 p-xs rounded duration-150"><svg
                                      xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                      fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
                                      stroke-linejoin="round"
                                      class="tabler-icon tabler-icon-dots text-text-200/50 hover:text-text-200 duration-150">
                                      <path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                      <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                      <path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    </svg></div>
                                </span></span></div>
                          </div>
                        </a><a class="group relative block" href="https://www.perplexity.ai/academic">
                          <div class="group relative block">
                            <div
                              class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                            </div>
                            <div
                              class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                color="currentColor" class="size-[18px] -translate-y-px" fill="currentColor"
                                fill-rule="evenodd">
                                <path
                                  d="M12 2C12.4599 2 12.833 2.37307 12.833 2.83301V18.5176L17.0029 14.9473V5.6748L15.918 6.75488C15.5918 7.07873 15.0644 7.0769 14.7402 6.75098C14.4163 6.42487 14.4182 5.89747 14.7441 5.57324L17.249 3.08105L17.3438 3.00098C17.5762 2.83052 17.8846 2.79036 18.1562 2.90332C18.4666 3.0325 18.6688 3.33569 18.6689 3.67188V15.3311C18.6689 15.5743 18.5618 15.8056 18.377 15.9639L14.2529 19.4951H20.334V6.16406C20.334 5.70426 20.7072 5.33127 21.167 5.33105C21.6269 5.33105 22 5.70412 22 6.16406V20.3281C21.9999 20.788 21.6269 21.1611 21.167 21.1611H2.83301C2.37314 21.1611 2.00012 20.788 2 20.3281V6.16406C2 5.70412 2.37307 5.33105 2.83301 5.33105C3.29279 5.33124 3.66504 5.70424 3.66504 6.16406V19.4951H9.74707L5.62207 15.9639C5.43739 15.8057 5.33105 15.5742 5.33105 15.3311V3.67188L5.33984 3.54688C5.38309 3.26217 5.57241 3.01627 5.84375 2.90332C6.1542 2.77424 6.51245 2.84416 6.75098 3.08105L9.25586 5.57324L9.36328 5.70312C9.57757 6.02568 9.54328 6.4656 9.25977 6.75098C8.97578 7.03648 8.53579 7.07335 8.21191 6.86035L8.08203 6.75488L6.99707 5.6748V14.9473L11.167 18.5176V2.83301C11.167 2.37308 11.5401 2.00002 12 2Z">
                                </path>
                              </svg><span class="w-full overflow-hidden whitespace-nowrap">Academic</span><span
                                style="mask-image:linear-gradient(to left, black 60%, transparent)"
                                class="pl-md bg-offsetPlus -right-xs absolute top-1/2 -translate-y-1/2 opacity-0 duration-200 group-hover:opacity-100"><span>
                                  <div class="hover:bg-text/5 p-xs rounded duration-150"><svg
                                      xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                      fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
                                      stroke-linejoin="round"
                                      class="tabler-icon tabler-icon-dots text-text-200/50 hover:text-text-200 duration-150">
                                      <path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                      <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                      <path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                    </svg></div>
                                </span></span></div>
                          </div>
                        </a></div>
                      <div class="bg-borderMain/50 h-px"></div>
                      <div class="group/header gap-xs flex h-6 items-center justify-between -mb-xs"><a
                          class="group relative block" href="https://www.perplexity.ai/library">
                          <div
                            class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                          </div><span class="gap-xs relative flex items-center">
                            <div
                              class="font-sans text-xs font-medium text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                              Library</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round"
                              class="tabler-icon tabler-icon-chevron-right text-text-200/50 group-hover:text-text-200 -mr-xs size-4 opacity-0 duration-200 group-hover/header:opacity-100">
                              <path d="M9 6l6 6l-6 6"></path>
                            </svg>
                          </span>
                        </a>
                        <div class="inline-flex" style="opacity:1;transform:none">
                          <div class="-mr-sm flex w-full flex-1 justify-end"><span>
                              <div
                                class="text-text flex h-[24px] w-[27px] items-center justify-center rounded-md duration-150 hover:bg-black/5 dark:hover:bg-white/5 text-text-200 hover:text-text duration-150 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                                <svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="plus"
                                  class="svg-inline--fa fa-plus fa-xs " role="img" xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 448 512">
                                  <path fill="currentColor"
                                    d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 160L40 232c-13.3 0-24 10.7-24 24s10.7 24 24 24l160 0 0 160c0 13.3 10.7 24 24 24s24-10.7 24-24l0-160 160 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-160 0 0-160z">
                                  </path>
                                </svg></div>
                            </span></div>
                        </div>
                      </div>
                      <div class="group/history">
                        <div style="opacity: 1; transform: none;">
                          <div
                            class="mb-md mt-xs space-y-xs border-borderMain/50 pr-sm dark:border-borderMainDark relative ml-[26px] border-l pl-[14px] duration-200 !m-0 !px-0 !border-0 flex flex-col gap-1.5 space-y-0 !-mt-[2px]"
                            style="transform: none; transform-origin: 50% 50% 0px;">
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/rerererererererererererererere-j.i.3kIXQmOLD.PAS_bZtQ">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">rererererererererererererererererererererererererererererererererererererererererererererererererere</span>
                                  </div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/hols-AiB3JUJjRzW3jYNWvtZ4yA">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">hols</span>
                                  </div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/hi-dySXVRw2REe6q5si5q4v2g">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">hi</span>
                                  </div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/can-you-read-the-contents-of-s-iolRUcmeRBmNassoibKoXw">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">Can you
                                      read the contents of sample.txt in my test files</span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/follow-along-and-build-the-pro-3cTVx5iDTRezUp.WoBPe8g">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">follow
                                      along and build the project</span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/can-you-please-tell-me-what-is-vNF.oCBLSqeJXJegWQLIAw">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">can you
                                      please tell me what is the recommended/best way to use git while coding big
                                      projects, but wi</span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/how-can-i-move-push-all-my-cha-M3x4utUAQVapQLna7_RcUw">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">how can
                                      i move/push all my changes on one branch to another branch locally? first give
                                      using cli com</span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/okay-so-you-know-what-perplexi-ov40T3UmTwugOtgmhrw0rA">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">okay,
                                      so you know what perplexity is right? I need you to build some sort of thing, that
                                      will allow </span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/okay-so-you-know-what-perplexi-0gMqpII3Sq6OZYbmOXA7mQ">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">okay,
                                      so you know what perplexity is right? I need you to build some sort of thing, that
                                      will allow </span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/prepare-a-slide-presentation-c-.3F9Iv4vQLy3IL43sDweIQ">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">Prepare
                                      a slide presentation comparing the range, pricing, and features of electric
                                      vehicles availab</span></div>
                                </div>
                              </a></div>
                          </div>
                        </div>
                      </div><a class="block" href="https://www.perplexity.ai/library">
                        <div
                          class="duration-200 hover:opacity-60 font-sans text-xs font-normal text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                          View All</div>
                      </a>
                      <div class="bg-borderMain/50 h-px"></div>
                      <div class="group/header gap-xs flex h-6 items-center justify-between -mb-xs"><a
                          class="group relative block"
                          href="https://www.perplexity.ai/collections/bookmarks-f1UvXcliTPC2GmxwWiZyCA">
                          <div
                            class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                          </div><span class="gap-xs relative flex items-center">
                            <div
                              class="font-sans text-xs font-medium text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                              Bookmarks</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round"
                              class="tabler-icon tabler-icon-chevron-right text-text-200/50 group-hover:text-text-200 -mr-xs size-4 opacity-0 duration-200 group-hover/header:opacity-100">
                              <path d="M9 6l6 6l-6 6"></path>
                            </svg>
                          </span>
                        </a></div>
                      <div class="group/history">
                        <div style="opacity: 1; transform: none;">
                          <div
                            class="mb-md mt-xs space-y-xs border-borderMain/50 pr-sm dark:border-borderMainDark relative ml-[26px] border-l pl-[14px] duration-200 !m-0 !px-0 !border-0 flex flex-col gap-1.5 space-y-0 !-mt-[2px]"
                            style="transform: none; transform-origin: 50% 50% 0px;">
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/find-me-fully-free-ai-video-qu-rziZZGpoSeKUzuyKQp2FDQ">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">find me
                                      fully free (AI) video quality enhancers that don't have watermarks</span></div>
                                </div>
                              </a></div>
                            <div style="opacity: 1; transform: none; transform-origin: 50% 50% 0px;"><a
                                class="group relative block"
                                href="https://www.perplexity.ai/search/give-me-some-videos-about-the-UY6LFMzSQXKu5xwY6dS1qg">
                                <div class="group relative block">
                                  <div
                                    class="-inset-y-xs -inset-x-sm absolute rounded-md opacity-0 duration-200 group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                                  </div>
                                  <div
                                    class="relative flex items-center gap-2 font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <span class="w-full overflow-hidden whitespace-nowrap"
                                      style="mask-image: linear-gradient(to right, black 85%, transparent 97%);">give me
                                      some videos about the see back effect</span></div>
                                </div>
                              </a></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="py-md relative flex h-full flex-col items-center border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent"
            style="width:72px"><span><a
                class="mt-xs active:ease-outExpo block duration-300 ease-out active:scale-[0.97] active:duration-150"
                href="https://www.perplexity.ai/">
                <div class="transition-all duration-300 ease-in-out hover:scale-105">
                  <div class="h-auto group  w-6 md:w-8"><svg viewBox="0 0 101 116" stroke="none" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        d="M86.4325 6.53418L50.4634 36.9696H86.4325V6.53418Z" stroke-width="5.53371"
                        stroke-miterlimit="10"></path>
                      <path d="M50.4625 36.9696L17.2603 6.53418V36.9696H50.4625Z"
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        stroke-width="5.53371" stroke-miterlimit="10"></path>
                      <path d="M50.4634 1L50.4634 114.441"
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        stroke-width="5.53371" stroke-miterlimit="10"></path>
                      <path d="M83.6656 70.172L50.4634 36.9697V79.3026L83.6656 108.908V70.172Z"
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        stroke-width="5.53371" stroke-miterlimit="10"></path>
                      <path d="M17.2603 70.172L50.4625 36.9697V78.4497L17.2603 108.908V70.172Z"
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        stroke-width="5.53371" stroke-miterlimit="10"></path>
                      <path d="M3.42627 36.9697V81.2394H17.2605V70.172L50.4628 36.9697H3.42627Z"
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        stroke-width="5.53371" stroke-miterlimit="10"></path>
                      <path d="M50.4634 36.9697L83.6656 70.172V81.2394H97.4999V36.9697L50.4634 36.9697Z"
                        class="stroke-textMain dark:stroke-textMainDark group-hover:stroke-superDuper dark:group-hover:stroke-superDark transition-colors duration-300"
                        stroke-width="5.53371" stroke-miterlimit="10"></path>
                    </svg></div>
                </div>
              </a></span>
            <div class="pt-sm relative flex w-full flex-col items-center"><button data-testid="sidebar-new-thread"
                aria-label="New Thread" type="button"
                class="bg-offsetPlus dark:bg-offsetPlusDark text-textMain dark:text-textMainDark  md:hover:text-textOff md:dark:hover:text-textOffDark my-lg hover:scale-105 font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-full cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-base h-10 aspect-square"
                data-state="closed">
                <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                  <div class="flex shrink-0 items-center justify-center size-5"><svg xmlns="http://www.w3.org/2000/svg"
                      width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.8"
                      stroke-linecap="round" stroke-linejoin="round" class="tabler-icon tabler-icon-plus ">
                      <path d="M12 5l0 14"></path>
                      <path d="M5 12l14 0"></path>
                    </svg></div>
                </div>
              </button><a data-testid="sidebar-home"
                class="p-sm group flex w-full flex-col items-center justify-center gap-0.5"
                href="https://www.perplexity.ai/">
                <div
                  class="grid size-[40px] place-items-center border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                  <div
                    class="size-[90%] rounded-md duration-150 [grid-area:1/-1] group-hover:opacity-100 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                  </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    color="currentColor"
                    class="relative duration-150 [grid-area:1/-1] group-hover:scale-110 text-text scale-110"
                    fill="currentColor" fill-rule="evenodd">
                    <path
                      d="M18.8809 18.8809C19.2013 18.5604 19.7091 18.5407 20.0527 18.8213L20.1191 18.8809L22.3086 21.0713L22.3691 21.1377C22.6493 21.4814 22.6289 21.9883 22.3086 22.3086C21.9883 22.6288 21.4813 22.6493 21.1377 22.3691L21.0713 22.3086L18.8809 20.1191L18.8213 20.0527C18.5407 19.7091 18.5605 19.2013 18.8809 18.8809ZM10.75 2.00488C15.5724 2.00494 19.5048 5.92589 19.5049 10.7598C19.5049 15.593 15.5832 19.5146 10.75 19.5146H9.875V19.4502C5.46095 19.01 1.9953 15.278 1.99512 10.7402C1.99512 5.90341 5.93043 2.00495 10.75 2.00488ZM10.75 3.75488C6.88968 3.75495 3.74512 6.87714 3.74512 10.7402C3.74531 14.6061 6.8927 17.7451 10.75 17.7451H11.25C14.8836 17.4884 17.7549 14.4584 17.7549 10.7598C17.7548 6.89383 14.6074 3.75494 10.75 3.75488ZM10.75 9C11.7165 9 12.5 9.7835 12.5 10.75C12.4999 11.7164 11.7165 12.5 10.75 12.5C9.78354 12.5 9.00006 11.7164 9 10.75C9 9.7835 9.7835 9 10.75 9Z">
                    </path>
                  </svg>
                </div>
                <div
                  class="font-sans text-xs font-medium text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                  Home</div>
              </a><a data-testid="sidebar-discover"
                class="p-sm group flex w-full flex-col items-center justify-center gap-0.5"
                href="https://www.perplexity.ai/discover/">
                <div
                  class="grid size-[40px] place-items-center border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                  <div
                    class="size-[90%] rounded-md duration-150 [grid-area:1/-1] group-hover:opacity-100 opacity-0 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                  </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    color="currentColor"
                    class="relative duration-150 [grid-area:1/-1] group-hover:scale-110 text-text-200"
                    fill="currentColor" fill-rule="evenodd">
                    <path
                      d="M12.3799 0.995117C18.6621 0.995117 23.7549 6.08788 23.7549 12.3701C23.7549 18.6524 18.6621 23.7451 12.3799 23.7451C6.0977 23.7451 1.00488 18.6523 1.00488 12.3701C1.00488 6.08792 6.0977 0.995179 12.3799 0.995117ZM12.3701 2.75488C11.8475 2.75509 11.1171 3.22655 10.4121 4.58887L10.2715 4.87402C10.065 5.3106 9.54321 5.49714 9.10645 5.29102C8.66967 5.08453 8.48308 4.56284 8.68945 4.12598L8.84277 3.81445C8.93133 3.64256 9.02498 3.47428 9.12305 3.31055C7.13221 4.02636 5.44689 5.38035 4.31055 7.125H12.3809L12.4697 7.12988C12.9108 7.17482 13.2558 7.54711 13.2559 8C13.2559 8.45294 12.9108 8.82517 12.4697 8.87012L12.3809 8.875H3.41211C2.98945 9.95869 2.75488 11.1368 2.75488 12.3701C2.75488 13.6071 2.99092 14.7886 3.41602 15.875H4.94043C5.42344 15.8752 5.81537 16.2669 5.81543 16.75C5.81543 17.2331 5.42348 17.6248 4.94043 17.625H4.31738C5.45343 19.3645 7.13555 20.7142 9.12207 21.4287C8.88505 21.0314 8.66832 20.5962 8.47461 20.1318C7.62775 18.1014 7.12501 15.3566 7.125 12.3701C7.125 12.083 7.12514 11.7675 7.13574 11.46C7.15246 10.9771 7.55716 10.5996 8.04004 10.6162C8.523 10.6329 8.90142 11.0375 8.88477 11.5205C8.87538 11.7927 8.875 12.0775 8.875 12.3701C8.87501 15.1832 9.3519 17.6886 10.0898 19.458C10.4596 20.3446 10.8783 21.0053 11.2979 21.4307C11.7142 21.8527 12.0793 21.995 12.3799 21.9951C12.9373 21.9951 13.731 21.4591 14.4785 19.877L14.5215 19.7979C14.7504 19.418 15.2339 19.2656 15.6436 19.459C16.0804 19.6654 16.2679 20.1871 16.0615 20.624L15.9072 20.9355C15.8202 21.1046 15.7271 21.2695 15.6309 21.4307C17.6203 20.7168 19.305 19.3664 20.4424 17.625H12.3799C11.8967 17.6249 11.5049 17.2332 11.5049 16.75C11.5049 16.2668 11.8967 15.8751 12.3799 15.875H21.3438C21.7689 14.7886 22.0049 13.6072 22.0049 12.3701C22.0049 11.1368 21.7703 9.95871 21.3477 8.875H19.8096C19.3265 8.87477 18.9346 8.48311 18.9346 8C18.9346 7.51695 19.3266 7.12523 19.8096 7.125H20.4492C19.309 5.3744 17.6159 4.01757 15.6162 3.30371C15.8576 3.70575 16.0787 4.14659 16.2754 4.61816C17.1223 6.64864 17.625 9.39331 17.625 12.3799C17.625 12.667 17.6258 12.9825 17.6152 13.29L17.6074 13.3789C17.5476 13.8184 17.1627 14.1501 16.71 14.1348C16.2272 14.1179 15.8496 13.7123 15.8662 13.2295L15.875 12.3799C15.875 9.56674 15.3982 7.0614 14.6602 5.29199C14.2904 4.40554 13.8727 3.7447 13.4531 3.31934C13.0368 2.89735 12.6707 2.75503 12.3701 2.75488ZM12.3799 10.6201C13.3464 10.6201 14.1299 11.4036 14.1299 12.3701C14.1299 13.3366 13.3464 14.1201 12.3799 14.1201C11.4134 14.1201 10.6299 13.3366 10.6299 12.3701C10.6299 11.4037 11.4134 10.6202 12.3799 10.6201Z">
                    </path>
                  </svg>
                </div>
                <div
                  class="font-sans text-xs font-normal text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                  Discover</div>
              </a><a data-testid="sidebar-spaces"
                class="p-sm group flex w-full flex-col items-center justify-center gap-0.5"
                href="https://www.perplexity.ai/spaces/templates/">
                <div
                  class="grid size-[40px] place-items-center border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                  <div
                    class="size-[90%] rounded-md duration-150 [grid-area:1/-1] group-hover:opacity-100 opacity-0 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offsetPlus dark:bg-offsetPlusDark">
                  </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    color="currentColor"
                    class="relative duration-150 [grid-area:1/-1] group-hover:scale-110 text-text-200"
                    fill="currentColor" fill-rule="evenodd">
                    <path
                      d="M22.1475 2.00586C22.1569 2.00616 22.1663 2.00721 22.1758 2.00781L22.209 2.00977C22.2435 2.01325 22.2773 2.01899 22.3105 2.02637C22.3158 2.02753 22.3209 2.02901 22.3262 2.03027C22.3568 2.03766 22.3858 2.04906 22.415 2.05957C22.5326 2.10177 22.6441 2.16668 22.7383 2.26074C22.7468 2.26924 22.7546 2.27835 22.7627 2.28711C22.7657 2.29033 22.7686 2.29361 22.7715 2.29688C22.8041 2.33334 22.8329 2.37193 22.8584 2.41211C22.8612 2.41654 22.8635 2.4213 22.8662 2.42578C22.8909 2.46628 22.9132 2.50759 22.9307 2.55078C22.972 2.65239 22.9951 2.76339 22.9951 2.87988V17.75C22.9951 18.2332 22.6033 18.6249 22.1201 18.625H7.6123L4.99219 21.2451H16.875C16.8776 20.7642 17.2684 20.3751 17.75 20.375C18.2332 20.3751 18.625 20.7668 18.625 21.25V22.1201C18.6249 22.6033 18.2332 22.9951 17.75 22.9951H2.87988C2.39673 22.9951 2.00495 22.6033 2.00488 22.1201V7.25L2.00977 7.16016C2.05476 6.71917 2.42703 6.37506 2.87988 6.375H17.3867L20.0068 3.75488H8.125C8.12231 4.23583 7.73158 4.625 7.25 4.625C6.76679 4.625 6.37506 4.2332 6.375 3.75V2.87988L6.37988 2.79004C6.42488 2.34901 6.7971 2.00488 7.25 2.00488H22.1201L22.1475 2.00586ZM18.625 7.6123V14.25C18.625 14.7332 18.2332 15.1249 17.75 15.125C17.2668 15.1249 16.875 14.7332 16.875 14.25V8.125H3.75488V20.0068L6.375 17.3867V10.75C6.375 10.2668 6.76675 9.875 7.25 9.875C7.73325 9.875 8.125 10.2668 8.125 10.75V16.875H21.2451V4.99219L18.625 7.6123ZM12.5 10.75C13.4665 10.75 14.25 11.5335 14.25 12.5C14.25 13.4665 13.4665 14.25 12.5 14.25C11.5335 14.25 10.75 13.4665 10.75 12.5C10.75 11.5335 11.5335 10.75 12.5 10.75Z">
                    </path>
                  </svg>
                </div>
                <div
                  class="font-sans text-xs font-normal text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                  Spaces</div>
              </a></div>
            <div class="pb-sm gap-md mt-auto flex flex-col items-center justify-center"><span class="inline-flex">
                <div class="inline-flex" data-testid="sidebar-popover-trigger-signed-in">
                  <div class="gap-xs group flex cursor-pointer flex-col items-center" data-state="closed">
                    <div class="relative select-none duration-150 group-hover:opacity-80">
                      <div class="text-offset relative">
                        <div
                          class="relative flex aspect-square shrink-0 items-center justify-center rounded-full size-8 ring-super dark:ring-superDark border-offset border-2 ring-[1.5px] border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-offset dark:bg-offsetDark">
                          <img alt="User avatar" class="size-full object-cover rounded-full"
                            src="./Perplexity_files/thumbnail">
                          <div class="absolute top-full -mt-[6px] w-[24px] rounded-r-full bg-current p-[1.5px]">
                            <div class="h-auto group w-full"><svg xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 1102.02 529.46">
                                <path class="fill-super"
                                  d="M1068.23,126.49c-23.5-41.68-58.35-75.11-100.77-96.67C928.5,10.04,884.37,0,836.29,0H66.16C29.56,0-.08,29.71,0,66.3l1,463.16h109.72V107.9h78.16v24.37c0,2.66,3.19,3.99,5.1,2.13,20.89-20.25,50.62-30.5,88.64-30.5,26.8,0,51.2,6.11,72.52,18.17,21.44,12.13,38.59,30.51,50.96,54.66h0c12.27,23.95,18.49,53.6,18.49,88.14s-6.22,63.91-18.49,87.85c-12.37,24.14-29.52,42.53-50.96,54.66-21.33,12.06-45.73,18.17-72.52,18.17-16.55,0-55.7-2.68-87.27-25.54-1.98-1.43-4.75-.01-4.75,2.43v127.02h645.68c48.09,0,92.22-10.03,131.18-29.83,42.41-21.55,77.26-54.98,100.78-96.69,22.41-39.76,33.78-86.22,33.78-138.07s-11.37-98.6-33.79-138.39ZM650.3,177.46h-33.53c-23.25,0-40.37,6.1-50.86,18.14-10.7,12.27-16.12,32.09-16.12,58.91v164.71h-79.89V107.9h77.01v20.91c0,2.68,3.28,4.04,5.13,2.11,4.56-4.76,9.87-8.79,15.89-12.06,11.94-6.49,28.27-9.77,48.55-9.77h33.81v68.37ZM967.21,352.21c-13.66,24.23-33.03,42.83-57.56,55.29-24.32,12.36-52.71,18.62-84.38,18.62s-60.35-6.27-84.67-18.62c-24.54-12.46-43.9-31.07-57.56-55.29-13.58-24.09-20.47-53.47-20.47-87.34s6.89-63.54,20.47-87.63c13.65-24.22,33.02-42.82,57.56-55.29,24.33-12.36,52.81-18.62,84.67-18.62s60.06,6.27,84.38,18.62c24.53,12.47,43.9,31.07,57.56,55.29,13.58,24.1,20.47,53.58,20.47,87.63s-6.89,63.25-20.47,87.34Z">
                                </path>
                                <path class="fill-super"
                                  d="M868.46,179.51c-12.16-7.28-26.7-10.97-43.2-10.97s-31.32,3.69-43.48,10.97c-12.07,7.23-21.57,18.05-28.23,32.16-6.79,14.4-10.23,32.3-10.23,53.2s3.45,38.57,10.24,53.07c6.66,14.2,16.19,25.06,28.35,32.28,12.26,7.29,26.85,10.98,43.36,10.98s31.04-3.69,43.2-10.97c12.07-7.23,21.56-18.09,28.22-32.29,6.79-14.5,10.24-32.35,10.24-53.07s-3.45-38.57-10.24-53.07c-6.66-14.2-16.15-25.07-28.22-32.29Z">
                                </path>
                                <path class="fill-super"
                                  d="M221.1,351.54c12.26,7.29,26.85,10.98,43.36,10.98s31.04-3.69,43.2-10.97c12.07-7.23,21.56-18.09,28.22-32.29,6.79-14.5,10.24-32.35,10.24-53.07s-3.45-38.57-10.24-53.07c-6.66-14.2-16.15-25.06-28.22-32.29-12.16-7.28-26.7-10.97-43.19-10.97s-31.32,3.69-43.48,10.97c-12.07,7.23-21.57,18.05-28.23,32.16-6.79,14.4-10.23,32.3-10.23,53.2s3.45,38.57,10.24,53.07c6.66,14.2,16.19,25.06,28.35,32.28Z">
                                </path>
                              </svg></div>
                          </div>
                        </div>
                      </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round"
                        class="tabler-icon tabler-icon-chevron-down text-text-200/50 group-hover:text-text-200 absolute right-0 top-1/2 size-3 -translate-y-1/2 translate-x-[calc(100%+2px)] duration-150">
                        <path d="M6 9l6 6l6 -6"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </span>
              <div class="gap-xs flex flex-col items-center" data-state="closed"><button type="button"
                  class="bg-offsetPlus dark:bg-offsetPlusDark text-textMain dark:text-textMainDark  md:hover:text-textOff md:dark:hover:text-textOffDark font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-full cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-square">
                  <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                    <div class="relative ml-auto"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round"
                        stroke-linejoin="round" class="tabler-icon tabler-icon-download size-4">
                        <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>
                        <path d="M7 11l5 5l5 -5"></path>
                        <path d="M12 4l0 12"></path>
                      </svg></div>
                  </div>
                </button></div>
            </div>
          </div>
        </div>
        <div
          class="erp-tab:p-0 erp-new_tab:p-0 md:gap-xs lg:py-sm lg:pr-sm isolate flex h-auto max-h-screen w-full min-w-0 grow flex-col"
          style="padding-left:200px">
          <div
            class="@container/main erp-tab:rounded-none erp-new_tab:rounded-none erp-tab:shadow-none erp-new_tab:shadow-none erp-tab:shadow-left-sm erp-new_tab:shadow-left-sm relative isolate flex-1 overflow-clip bg-clip-border shadow-sm lg:rounded-lg border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-background dark:bg-backgroundDark">
            <div class="max-w-screen md:max-w-auto mx-auto flex w-full flex-col h-full">
              <div class="scrollable-container scrollbar flex flex-1 basis-0 overflow-auto [scrollbar-gutter:stable]">
                <div class="mx-auto size-full max-w-screen-md px-md md:px-lg">
                  <div class="erp-sidecar:px-md sm:px-md md:px-lg isolate mx-auto size-full sm:max-w-screen-md">
                    <div class="relative flex h-full flex-col">
                      <div
                        class="py-md h-headerHeight flex items-center justify-between border-b md:hidden border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                        <div class="gap-x-xs -ml-sm flex items-center"><button type="button"
                            class="focus-visible:bg-offsetPlus dark:focus-visible:bg-offsetPlusDark hover:bg-offsetPlus text-textOff dark:text-textOffDark hover:text-textMain dark:hover:bg-offsetPlusDark dark:hover:text-textMainDark font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-full cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-base h-10 aspect-square">
                            <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                              <div class="flex shrink-0 items-center justify-center size-5"><svg
                                  xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                  fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round"
                                  stroke-linejoin="round" class="tabler-icon tabler-icon-menu-2 ">
                                  <path d="M4 6l16 0"></path>
                                  <path d="M4 12l16 0"></path>
                                  <path d="M4 18l16 0"></path>
                                </svg></div>
                            </div>
                          </button>
                          <div class="h-auto group w-24"><svg viewBox="0 0 400 91" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M196.978 27.8931H200.033V34.1872H196.079C192.979 34.1872 190.669 34.9333 189.14 36.4254C187.615 37.9176 186.85 40.3662 186.85 43.7711V64.401H180.606V28.0333H186.85V33.8367C186.85 34.1622 187.014 34.3274 187.337 34.3274C187.52 34.3274 187.659 34.2823 187.754 34.1872C187.848 34.0921 187.938 33.9068 188.032 33.6264C189.234 29.8058 192.219 27.8931 196.983 27.8931H196.978ZM237.763 35.6894C239.402 38.6036 240.227 42.1137 240.227 46.2146C240.227 50.3156 239.407 53.8257 237.763 56.7399C236.119 59.6541 233.993 61.8323 231.38 63.2794C228.767 64.7265 225.956 65.4476 222.951 65.4476C217.03 65.4476 212.868 63.0691 210.464 58.3122C210.28 57.9417 210.046 57.7514 209.768 57.7514C209.49 57.7514 209.351 57.8916 209.351 58.172V77.6853H203.107V28.0333H209.351V34.2573C209.351 34.5377 209.49 34.6779 209.768 34.6779C210.046 34.6779 210.275 34.4926 210.464 34.1171C212.868 29.3602 217.03 26.9817 222.951 26.9817C225.956 26.9817 228.767 27.7028 231.38 29.1499C233.993 30.597 236.119 32.7751 237.763 35.6894ZM233.983 46.2146C233.983 41.9234 232.841 38.5786 230.551 36.1801C228.261 33.7816 225.246 32.5799 221.496 32.5799C217.745 32.5799 214.73 33.7816 212.44 36.1801C210.151 38.5836 209.311 41.9284 209.311 46.2146C209.311 50.5009 210.151 53.8507 212.44 56.2492C214.73 58.6527 217.75 59.8494 221.496 59.8494C225.241 59.8494 228.261 58.6477 230.551 56.2492C232.841 53.8507 233.983 50.5009 233.983 46.2146ZM134.595 35.7445C136.235 38.6587 137.059 42.1688 137.059 46.2697C137.059 50.3707 136.24 53.8808 134.595 56.795C132.951 59.7092 130.825 61.8874 128.213 63.3345C125.6 64.7816 122.788 65.5026 119.783 65.5026C113.863 65.5026 109.7 63.1242 107.296 58.3673C107.112 57.9967 106.879 57.8065 106.601 57.8065C106.322 57.8065 106.183 57.9467 106.183 58.2271V77.7404H99.9446V28.0883H106.188V34.3124C106.188 34.5928 106.327 34.733 106.606 34.733C106.884 34.733 107.112 34.5477 107.301 34.1722C109.705 29.4153 113.867 27.0368 119.788 27.0368C122.793 27.0368 125.605 27.7579 128.218 29.205C130.83 30.6521 132.956 32.8302 134.6 35.7445H134.595ZM130.815 46.2697C130.815 41.9785 129.673 38.6336 127.383 36.2352C125.093 33.8367 122.078 32.6349 118.328 32.6349C114.578 32.6349 111.563 33.8367 109.273 36.2352C106.983 38.6387 106.144 41.9835 106.144 46.2697C106.144 50.5559 106.983 53.9058 109.273 56.3043C111.563 58.7078 114.578 59.9045 118.328 59.9045C122.078 59.9045 125.093 58.7028 127.383 56.3043C129.673 53.9058 130.815 50.5559 130.815 46.2697ZM169.112 52.8543H175.703C174.824 56.2592 173.031 59.2085 170.329 61.7021C167.622 64.1957 163.748 65.4425 158.706 65.4425C154.911 65.4425 151.573 64.6614 148.682 63.0991C145.791 61.5369 143.561 59.3137 141.986 56.4195C140.412 53.5303 139.627 50.1253 139.627 46.2096C139.627 42.294 140.392 38.889 141.917 35.9998C143.442 33.1106 145.583 30.8824 148.335 29.3201C151.086 27.7579 154.31 26.9767 158.011 26.9767C161.711 26.9767 164.776 27.7479 167.344 29.2851C169.912 30.8223 171.829 32.8653 173.101 35.404C174.372 37.9477 175.008 40.7317 175.008 43.7611V47.9572H146.219C146.447 51.5925 147.664 54.4867 149.859 56.6298C152.055 58.7729 155.005 59.8494 158.706 59.8494C161.711 59.8494 164.016 59.2335 165.61 57.9967C167.205 56.7599 168.372 55.0475 169.112 52.8543ZM146.288 42.7146H168.074C168.074 39.545 167.264 37.0614 165.645 35.2638C164.026 33.4712 161.483 32.5699 158.015 32.5699C154.777 32.5699 152.129 33.4461 150.073 35.1937C148.017 36.9412 146.755 39.4498 146.293 42.7096L146.288 42.7146ZM245.169 64.396H251.413V14.043H245.169V64.401V64.396ZM331.801 24.0625H339.093V16.1911H331.801V24.0625ZM357.526 58.9782C356.393 59.0933 355.708 59.1534 355.479 59.1534C355.156 59.1534 354.898 59.0583 354.714 58.873C354.531 58.6877 354.436 58.4324 354.436 58.1019C354.436 57.8716 354.496 57.1806 354.61 56.0389C354.724 54.8972 354.784 53.1347 354.784 50.7612V33.361H363.69L361.937 28.0333H354.789V18.2391H348.545V28.0283H341.755V33.356H348.545V52.5038C348.545 56.5146 349.519 59.4989 351.461 61.4568C353.403 63.4146 356.363 64.396 360.342 64.396H365.2V58.8029H362.771C360.412 58.8029 358.663 58.863 357.531 58.9782H357.526ZM394.059 28.0283L383.723 58.5425C383.583 58.9181 383.365 59.4088 382.655 59.4088C381.944 59.4088 381.726 58.9181 381.587 58.5425L371.25 28.0283H364.882L376.853 64.396H381.085C381.363 64.396 381.572 64.4211 381.711 64.4661C381.85 64.5112 381.964 64.6264 382.059 64.8166C382.242 65.097 382.217 65.5177 381.989 66.0735L380.047 71.3862C379.769 72.0872 379.237 72.4377 378.452 72.4377C378.174 72.4377 377.529 72.3776 376.51 72.2624C375.492 72.1473 374.176 72.0872 372.556 72.0872H367.49V77.6803H374.151C378.035 77.6803 380.375 77.0143 382.386 75.6874C384.398 74.3605 385.958 72.0171 387.07 68.6572L400 29.4203V28.0233H394.059V28.0283ZM311.406 41.5979L301.626 28.0283H294.756V29.4253L306.478 45.1631L292.188 62.999V64.396H299.197L310.576 49.7798L321.191 64.396H327.922V62.999L315.504 46.2146L328.965 29.5004V28.0333H321.956L311.411 41.603L311.406 41.5979ZM332.467 64.396H338.71V28.0333H332.467V64.401V64.396ZM292.471 52.8543C291.592 56.2592 289.799 59.2085 287.097 61.7021C284.39 64.1957 280.515 65.4425 275.474 65.4425C271.679 65.4425 268.341 64.6614 265.45 63.0991C262.559 61.5369 260.329 59.3137 258.754 56.4195C257.18 53.5303 256.395 50.1253 256.395 46.2096C256.395 42.294 257.16 38.889 258.685 35.9998C260.21 33.1106 262.35 30.8824 265.102 29.3201C267.854 27.7579 271.078 26.9767 274.778 26.9767C278.479 26.9767 281.543 27.7479 284.111 29.2851C286.679 30.8223 288.597 32.8653 289.868 35.404C291.14 37.9477 291.776 40.7317 291.776 43.7611V47.9572H262.986C263.215 51.5925 264.432 54.4867 266.627 56.6298C268.823 58.7729 271.773 59.8494 275.474 59.8494C278.479 59.8494 280.783 59.2335 282.378 57.9967C283.972 56.7599 285.14 55.0475 285.88 52.8543H292.471ZM263.056 42.7146H284.842C284.842 39.545 284.032 37.0614 282.413 35.2638C280.793 33.4712 278.25 32.5699 274.783 32.5699C271.545 32.5699 268.897 33.4461 266.841 35.1937C264.784 36.9412 263.523 39.4498 263.061 42.7096L263.056 42.7146Z"
                                class=" block fill-textMain dark:fill-textMainDark"></path>
                              <path d="M38.6936 29.9832L12.8633 5.88983V29.9832H38.6936Z"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                              <path d="M39.5005 29.9832L65.3308 5.88983V29.9832H39.5005Z"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                              <path d="M38.7227 2L38.7227 90.2534"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                              <path d="M64.5246 53.7584L38.6943 30.0068V62.9404L64.5246 85.9724V53.7584Z"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                              <path d="M12.8924 53.7584L38.7227 30.0068V62.9404L12.8924 85.9724V53.7584Z"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                              <path d="M2.28711 29.9832V64.4236H12.8863V53.7348L38.7226 29.9832H2.28711Z"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                              <path d="M38.6943 30.3L64.5246 54.0515V64.7403H75.2872V30.3L38.6943 30.3Z"
                                class="stroke-super dark:stroke-superDark transition-all duration-300"
                                stroke-width="4.30504" stroke-miterlimit="10"></path>
                            </svg></div>
                        </div>
                        <div class="gap-x-sm flex items-center"></div>
                      </div>
                      <div
                        class="static w-full grow flex-col items-center justify-center md:mt-0 md:flex z-10 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                        <div class="relative flex h-full w-full flex-col justify-center md:h-auto">
                          <div
                            class="mb-lg pb-xs bottom-0 flex w-full items-center justify-center text-center md:absolute">
                            <div style="opacity:1">
                              <div class="h-auto group w-64 "><svg xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 3913.07 632">
                                  <path class=" block fill-textMain dark:fill-textMainDark"
                                    d="M972.21,137.43h30.23v62.43h-39.13c-30.66,0-53.52,7.39-68.68,22.22-15.07,14.78-22.64,39.1-22.64,72.9v204.71h-61.76V138.85h61.76v57.58c0,3.25,1.63,4.85,4.8,4.85,1.81,0,3.17-.45,4.14-1.38.97-.94,1.8-2.76,2.77-5.57,11.89-37.9,41.46-56.87,88.55-56.87h-.04v-.04ZM1383.06,214.78c16.21,28.9,24.36,63.73,24.36,104.43s-8.11,75.53-24.36,104.43c-16.26,28.9-37.32,50.54-63.18,64.88-25.86,14.34-53.66,21.51-83.39,21.51-58.59,0-99.78-23.6-123.57-70.81-1.81-3.7-4.14-5.57-6.87-5.57s-4.14,1.38-4.14,4.19v193.63h-61.77V138.81h61.77v61.77c0,2.76,1.36,4.19,4.14,4.19s5.02-1.83,6.87-5.57c23.79-47.2,64.98-70.81,123.57-70.81,29.73,0,57.53,7.17,83.39,21.51,25.86,14.34,46.88,35.98,63.18,64.88ZM1345.7,319.21c0-42.57-11.32-75.75-33.97-99.57-22.64-23.83-52.46-35.72-89.6-35.72s-66.96,11.93-89.61,35.72c-22.68,23.82-30.96,57.04-30.96,99.57s8.32,75.75,30.96,99.57c22.65,23.87,52.51,35.72,89.61,35.72s66.96-11.93,89.6-35.72c22.65-23.78,33.97-57.04,33.97-99.57ZM342.87,215.31c16.21,28.9,24.36,63.73,24.36,104.43s-8.11,75.53-24.36,104.43c-16.26,28.9-37.31,50.55-63.17,64.88-25.86,14.34-53.66,21.51-83.39,21.51-58.59,0-99.78-23.6-123.57-70.81-1.8-3.7-4.14-5.57-6.87-5.57s-4.14,1.38-4.14,4.18v193.63H0V139.34h61.76v61.76c0,2.76,1.36,4.19,4.14,4.19s5.02-1.83,6.87-5.57c23.79-47.2,64.98-70.81,123.57-70.81,29.74,0,57.53,7.17,83.39,21.51,25.86,14.34,46.87,35.98,63.17,64.88h-.05ZM305.46,319.74c0-42.57-11.32-75.75-33.96-99.57-22.65-23.78-52.51-35.72-89.61-35.72s-66.96,11.93-89.6,35.72c-22.64,23.87-30.97,57.05-30.97,99.57s8.33,75.75,30.97,99.58c22.64,23.87,52.47,35.71,89.6,35.71s66.96-11.93,89.61-35.71c22.64-23.78,33.96-57.05,33.96-99.58ZM701.07,385.07h65.2c-8.68,33.8-26.43,63.05-53.17,87.77-26.79,24.76-65.11,37.1-115.02,37.1-37.53,0-70.57-7.75-99.16-23.25-28.59-15.5-50.66-37.54-66.26-66.26-15.59-28.68-23.35-62.43-23.35-101.31s7.58-72.63,22.64-101.31c15.07-28.68,36.26-50.77,63.48-66.26,27.23-15.5,59.12-23.25,95.73-23.25s66.96,7.66,92.33,22.89c25.42,15.27,44.36,35.54,56.96,60.7,12.6,25.25,18.85,52.86,18.85,82.92v41.64h-284.85c2.24,36.07,14.32,64.8,36.03,86.04,21.72,21.29,50.92,31.93,87.54,31.93,29.73,0,52.55-6.1,68.33-18.39,15.77-12.29,27.31-29.26,34.67-51.03l.04.09ZM473.18,284.47h215.55c0-31.44-8.02-56.11-24.05-73.92-16.03-17.77-41.19-26.72-75.51-26.72-32.03,0-58.24,8.68-78.59,26.05-20.35,17.32-32.82,42.22-37.4,74.59ZM1457.24,499.61h61.81V0h-61.81v499.65-.04ZM2325.88,99.4h72.17V21.24h-72.17v78.15ZM2592.35,445.86c-11.22,1.16-18.01,1.74-20.24,1.74-3.18,0-5.79-.93-7.58-2.81-1.82-1.83-2.77-4.36-2.77-7.66,0-2.27.59-9.13,1.76-20.48,1.09-11.31,1.71-28.81,1.71-52.37v-172.65h88.1l-17.36-52.86h-70.71V41.59h-61.74v97.13h-67.2v52.86h67.2v189.97c0,39.81,9.65,69.43,28.83,88.84,19.21,19.42,48.51,29.17,87.91,29.17h48.05v-55.49h-24.05c-23.34,0-40.63.58-51.85,1.74l-.06.05ZM2965.71,138.76l-102.29,302.77c-1.35,3.74-3.53,8.6-10.51,8.6s-9.21-4.85-10.6-8.6l-102.29-302.77h-62.99l118.46,360.85h41.9c2.72,0,4.81.27,6.22.71,1.36.45,2.5,1.6,3.43,3.47,1.79,2.76,1.57,6.95-.71,12.47l-19.21,52.73c-2.77,6.95-8.02,10.42-15.76,10.42-2.77,0-9.16-.58-19.22-1.74-10.08-1.16-23.07-1.73-39.1-1.73h-50.14v55.48h65.9c38.45,0,61.58-6.59,81.5-19.77,19.92-13.18,35.33-36.43,46.33-69.73l127.94-389.3v-13.85h-58.86ZM2116.82,273.38l-96.8-134.62h-67.96v13.85l115.98,156.13-141.42,176.97v13.85h69.35l112.59-145.04,105.03,145.04h66.61v-13.85l-122.92-166.55,133.24-165.84v-14.56h-69.35l-104.32,134.62h-.03ZM2332.45,499.61h61.8V138.81h-61.8v360.84-.04ZM1925.78,385.07c-8.72,33.8-26.41,63.05-53.15,87.77-26.8,24.76-65.12,37.1-115.04,37.1-37.52,0-70.57-7.75-99.16-23.25-28.64-15.5-50.65-37.54-66.25-66.26-15.55-28.68-23.31-62.43-23.31-101.31s7.58-72.63,22.65-101.31c15.12-28.68,36.26-50.77,63.49-66.26,27.2-15.5,59.1-23.25,95.76-23.25s66.96,7.66,92.37,22.89c25.38,15.27,44.38,35.54,56.96,60.7,12.61,25.25,18.86,52.86,18.86,82.92v41.64h-284.88c2.29,36.07,14.3,64.8,36.04,86.04,21.71,21.29,50.92,31.93,87.53,31.93,29.73,0,52.56-6.1,68.32-18.39,15.76-12.29,27.31-29.26,34.62-51.03h65.24l-.05.09ZM1634.25,284.47h215.55c0-31.44-8.02-56.11-24-73.92-16.06-17.77-41.19-26.72-75.52-26.72-32.04,0-58.23,8.68-78.59,26.05-20.35,17.32-32.85,42.22-37.39,74.59h-.05Z">
                                  </path>
                                  <g>
                                    <path class="fill-super"
                                      d="M3355.84,188.2c-34.07-64.39-131.07-79.63-178.65-27.45-1.45,1.53-4.03.51-4.03-1.6v-20.46s-51.68,0-51.68,0v360.92h51.68v-137.9c0-2.11,2.57-3.13,4.03-1.6,17.98,18.93,43.42,31.52,75.79,31.52,72.2.47,121.88-58.16,120.25-131.21,0-28.27-5.85-52.57-17.39-72.23ZM3321.56,260.43c0,26.4-7.05,50.24-20.94,64.92h0c-25.21,28.94-83.76,28.65-109.2,0-27.18-27.93-27.44-101.65,0-129.38,25.44-29.25,83.94-29.55,109.17-.04,13.94,14.4,20.97,38.06,20.98,64.5Z">
                                    </path>
                                    <path class="fill-super"
                                      d="M3678.1,499.61h-438.05v-48.22h438.05c33.41,0,64.39-7.45,92.07-22.16,29.4-15.46,53.58-39.15,70.02-68.56l.08-.14c16.31-28.72,24.58-62.4,24.58-100.11s-8.26-71.36-24.56-100.07l-.08-.13c-16.46-29.43-40.64-53.13-69.93-68.54-27.79-14.76-58.78-22.22-92.19-22.22h-555.91V21.24h555.91c41.39,0,79.99,9.36,114.73,27.81,37.45,19.7,68.41,49.99,89.43,87.55,20.45,36.05,30.82,77.7,30.82,123.82s-10.38,87.81-30.84,123.86c-21,37.53-51.96,67.81-89.53,87.57-34.63,18.4-73.22,27.75-114.61,27.75Z">
                                    </path>
                                    <path class="fill-super"
                                      d="M3784.19,189.62c-42.92-80.72-177.41-80.62-220.65.03-10.91,19.63-16.45,43.44-16.45,70.78-1.52,77.12,51.01,131.73,127,131.21,75.9.53,128.27-54.63,127-131.21,0-27.33-5.69-51.15-16.91-70.8ZM3749.42,260.43c0,26.4-7.05,50.24-20.94,64.92h0c-25.21,28.94-83.76,28.65-109.2,0-27.17-27.93-27.44-101.65,0-129.38,25.44-29.25,83.94-29.55,109.17-.04,13.94,14.4,20.97,38.06,20.98,64.5Z">
                                    </path>
                                    <path class="fill-super"
                                      d="M3515.38,135.16c-16.77,0-30.24,2.85-40.05,8.48-5.78,3.15-11.11,8.68-16.12,16.74-1.24,2-4.32,1.13-4.32-1.22v-20.46s-51.67,0-51.67,0v246.1h51.67v-140.34c0-20.57,4.43-35.36,13.17-43.97,16.17-16.75,48.45-12.5,72.26-13v-52.31h-24.94Z">
                                    </path>
                                  </g>
                                </svg></div>
                            </div>
                          </div>
                        </div>
                        <div class="w-full">
                          <div class="relative">
                            <div><span class="grow block">
                                <div class="rounded-2xl">
                                  <div
                                    class="bg-background w-full outline-none focus:outline-none focus:ring-borderMain font-sans flex items-center text-textMain placeholder-textOff border focus:ring-1 rounded-2xl dark:bg-offsetDark dark:text-textMainDark dark:placeholder-textOffDark selection:bg-superDuper selection:text-textMain duration-75 transition-all border-borderMain dark:border-textMain/10 shadow-sm dark:shadow-md shadow-textMain/5 dark:shadow-black/10 px-0 pt-3 pb-3 gap-y-md grid items-center">
                                    <div class="px-md grid-rows-1fr-auto grid grid-cols-3">
                                      <div
                                        class="col-start-1 col-end-4 pb-sm overflow-hidden relative flex h-full w-full"
                                        style="position: relative;"><textarea autofocus="" placeholder="Ask anything…"
                                          class="overflow-auto max-h-[45vh] lg:max-h-[40vh] sm:max-h-[25vh] outline-none w-full font-sans caret-superDuper resize-none selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark dark:bg-offsetDark dark:text-textMainDark dark:placeholder-textOffDark placeholder:select-none bg-background text-textMain placeholder-textOff scrollbar-thumb-idle dark:scrollbar-thumb-idleDark scrollbar-thin scrollbar-track-transparent"
                                          autocomplete="off" rows="2" id="ask-input" data-1p-ignore="true"
                                          style="height: 48px !important;"></textarea>
                                        <div class="mcp-prompt-indicator" id="prompt-indicator">
                                          <span class="mcp-indicator-icon">🔧</span>
                                          <span class="mcp-indicator-text">11 MCP tools available</span>
                                        </div>
                                      </div>
                                      <div
                                        class="bg-background dark:bg-offsetDark gap-sm flex rounded-l-lg col-start-1 row-start-2 -ml-1">
                                        <div class="gap-xs flex items-center">
                                          <div role="radiogroup" aria-required="false" dir="ltr" data-state="closed"
                                            class="group relative isolate flex h-fit focus:outline-none" tabindex="0"
                                            style="outline:none">
                                            <div
                                              class="bg-background-super-200/10 dark:bg-backgroundDark absolute inset-0 rounded-[10px] transition-colors duration-300">
                                            </div>
                                            <div class="p-two flex items-center"><span><button type="button"
                                                  role="radio" aria-checked="true" data-state="checked" value="search"
                                                  aria-label="Search"
                                                  class="segmented-control group/segmented-control relative focus:outline-none"
                                                  tabindex="-1" data-radix-collection-item="">
                                                  <div data-state="checked"
                                                    class="bg-background-100 dark:bg-superDark/10 dark:text-superDark border-super dark:border-super/30 shadow-super/30 dark:shadow-superDark/10 pointer-events-none absolute inset-0 block rounded-lg border shadow-[0_1px_3px_0] transition-colors duration-300 group-focus-visible/segmented-control:border-dashed"
                                                    style="opacity: 1;"></div>
                                                  <div>
                                                    <div
                                                      class="relative z-10 flex h-8 min-w-9 items-center justify-center py-xs gap-xs px-2.5">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" color="currentColor"
                                                        class="transition-colors duration-300 text-super"
                                                        fill="currentColor" fill-rule="evenodd">
                                                        <path
                                                          d="M11 2.125a8.378 8.378 0 0 1 8.375 8.375c0 .767-.1 1.508-.304 2.22l-.029.085a.875.875 0 0 1-1.653-.566l.054-.206c.12-.486.182-.996.182-1.533A6.628 6.628 0 0 0 11 3.875 6.628 6.628 0 0 0 4.375 10.5a6.628 6.628 0 0 0 10.402 5.445c.943-.654 2.242-.664 3.153.109l.176.165.001.002 4.066 4.184a.875.875 0 0 1-1.256 1.22l-4.064-4.185-.104-.088c-.263-.183-.646-.197-.975.03l-.001.003A8.378 8.378 0 0 1 2.625 10.5 8.378 8.378 0 0 1 11 2.125Zm0 7.09a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6Z">
                                                        </path>
                                                      </svg></div>
                                                  </div>
                                                </button></span><span><button type="button" role="radio"
                                                  aria-checked="false" data-state="unchecked" value="research"
                                                  aria-label="Research"
                                                  class="segmented-control group/segmented-control relative focus:outline-none"
                                                  tabindex="-1" data-radix-collection-item="">
                                                  <div>
                                                    <div
                                                      class="relative z-10 flex h-8 min-w-9 items-center justify-center">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" color="currentColor"
                                                        class="transition-colors duration-300 text-text-100/70 group-hover/segmented-control:text-text-100"
                                                        fill="currentColor" fill-rule="evenodd">
                                                        <path
                                                          d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z">
                                                        </path>
                                                      </svg></div>
                                                  </div>
                                                </button></span><span><button type="button" role="radio"
                                                  aria-checked="false" data-state="unchecked" value="studio"
                                                  aria-label="Labs"
                                                  class="segmented-control group/segmented-control relative focus:outline-none"
                                                  tabindex="-1" data-radix-collection-item="">
                                                  <div>
                                                    <div
                                                      class="relative z-10 flex h-8 min-w-9 items-center justify-center">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" color="currentColor"
                                                        class="transition-colors duration-300 text-text-100/70 group-hover/segmented-control:text-text-100"
                                                        fill="currentColor" fill-rule="evenodd">
                                                        <path
                                                          d="M11.92 1.889a7.035 7.035 0 0 1 7.034 7.032 7.033 7.033 0 0 1-3.694 6.184v3.053a.876.876 0 0 1-.875.875h-4.052v1.317h4.052l.089.003a.876.876 0 0 1 0 1.742l-.09.005H9.459a.876.876 0 0 1-.875-.875v-3.067c0-.483.392-.875.875-.875h4.052v-2.722c0-.347.205-.662.523-.801l.343-.164a5.286 5.286 0 0 0 2.828-4.675 5.285 5.285 0 0 0-5.283-5.282A5.285 5.285 0 0 0 6.639 8.92a5.286 5.286 0 0 0 2.828 4.675l.342.164.08.04a.875.875 0 0 1-.698 1.595l-.084-.032-.23-.105A7.036 7.036 0 0 1 4.89 8.92a7.035 7.035 0 0 1 7.032-7.032Zm.014 5.732a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6Z">
                                                        </path>
                                                      </svg></div>
                                                  </div>
                                                </button></span></div>
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="bg-background dark:bg-offsetDark flex items-center justify-self-end rounded-full col-start-3 row-start-2 -mr-1">
                                        <div style="opacity:1"><span><button aria-label="GPT-4.1" type="button"
                                              class="focus-visible:bg-offsetPlus dark:focus-visible:bg-offsetPlusDark hover:bg-offsetPlus text-super dark:text-superDark dark:hover:bg-offsetPlusDark max-w-24 sm:max-w-none font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-lg cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-[9/8]"
                                              data-state="closed">
                                              <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                                                <div class="flex shrink-0 items-center justify-center size-4"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="1.7999999999999998" stroke-linecap="round"
                                                    stroke-linejoin="round" class="tabler-icon tabler-icon-cpu ">
                                                    <path
                                                      d="M5 5m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v12a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z">
                                                    </path>
                                                    <path d="M9 9h6v6h-6z"></path>
                                                    <path d="M3 10h2"></path>
                                                    <path d="M3 14h2"></path>
                                                    <path d="M10 3v2"></path>
                                                    <path d="M14 3v2"></path>
                                                    <path d="M21 10h-2"></path>
                                                    <path d="M21 14h-2"></path>
                                                    <path d="M14 21v-2"></path>
                                                    <path d="M10 21v-2"></path>
                                                  </svg></div>
                                              </div>
                                            </button></span></div><span><button type="button"
                                            class="focus-visible:bg-offsetPlus dark:focus-visible:bg-offsetPlusDark hover:bg-offsetPlus text-textOff dark:text-textOffDark hover:text-textMain dark:hover:bg-offsetPlusDark dark:hover:text-textMainDark px-[4px]  font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-lg cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-[9/8]"
                                            data-state="closed">
                                            <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                                              <div>
                                                <div class="my-xs flex items-center">
                                                  <div class="flex items-center gap-2">
                                                    <div
                                                      class="relative flex items-center justify-center rounded-full size-6"
                                                      style="z-index:0">
                                                      <div class="flex size-5 items-center justify-center"><svg
                                                          xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                          viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                          stroke-width="1.7999999999999998" stroke-linecap="round"
                                                          stroke-linejoin="round"
                                                          class="tabler-icon tabler-icon-world ">
                                                          <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                                                          <path d="M3.6 9h16.8"></path>
                                                          <path d="M3.6 15h16.8"></path>
                                                          <path d="M11.5 3a17 17 0 0 0 0 18"></path>
                                                          <path d="M12.5 3a17 17 0 0 1 0 18"></path>
                                                        </svg></div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </button></span>
                                        <div class="flex items-center">
                                          <div data-state="closed">
                                            <div style="opacity:1"><span><button aria-label="Attach files" type="button"
                                                  class="focus-visible:bg-offsetPlus dark:focus-visible:bg-offsetPlusDark hover:bg-offsetPlus text-textOff dark:text-textOffDark hover:text-textMain dark:hover:bg-offsetPlusDark dark:hover:text-textMainDark font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-lg cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-[9/8]"
                                                  data-state="closed">
                                                  <div
                                                    class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                                                    <div class="flex shrink-0 items-center justify-center size-4"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="1.7999999999999998" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="tabler-icon tabler-icon-paperclip ">
                                                        <path
                                                          d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5">
                                                        </path>
                                                      </svg></div>
                                                  </div>
                                                </button></span></div><input type="file" multiple=""
                                              accept=".pdf,.txt,.py,.ipynb,.js,.jsx,.html,.css,.java,.cs,.php,.c,.cpp,.cxx,.h,.hpp,.rs,.R,.Rmd,.swift,.go,.rb,.kt,.kts,.ts,.tsx,.m,.scala,.rs,.dart,.lua,.pl,.pm,.t,.sh,.bash,.zsh,.csv,.log,.ini,.config,.json,.yaml,.yml,.toml,.lua,.sql,.bat,.md,.coffee,.tex,.latex,.less,.pptx,.docx,.xlsx,.jpeg,.jpg,.jpe,.jp2,.png,.gif,.bmp,.tiff,.tif,.svg,.webp,.ico,.avif,.heic,.heif,.mp3,.wav,.aiff,.ogg,.flac,.mp4,.mpeg,.mov,.avi,.flv,.mpg,.webm,.wmv,.3gp"
                                              style="display:none">
                                          </div>
                                        </div>
                                        <div class="relative"><button aria-label="Dictation" type="button"
                                            class="focus-visible:bg-offsetPlus dark:focus-visible:bg-offsetPlusDark hover:bg-offsetPlus text-textOff dark:text-textOffDark hover:text-textMain dark:hover:bg-offsetPlusDark dark:hover:text-textMainDark font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-lg cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-[9/8]"
                                            data-state="closed">
                                            <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                                              <div class="flex shrink-0 items-center justify-center size-4"><svg
                                                  xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                  viewBox="0 0 24 24" fill="currentColor" stroke="none"
                                                  class="tabler-icon tabler-icon-microphone-filled ">
                                                  <path
                                                    d="M19 9a1 1 0 0 1 1 1a8 8 0 0 1 -6.999 7.938l-.001 2.062h3a1 1 0 0 1 0 2h-8a1 1 0 0 1 0 -2h3v-2.062a8 8 0 0 1 -7 -7.938a1 1 0 1 1 2 0a6 6 0 0 0 12 0a1 1 0 0 1 1 -1m-7 -8a4 4 0 0 1 4 4v5a4 4 0 1 1 -8 0v-5a4 4 0 0 1 4 -4">
                                                  </path>
                                                </svg></div>
                                            </div>
                                          </button></div>
                                        <div class="ml-2"><button aria-label="Voice mode" type="button"
                                            class="bg-super dark:bg-superDark dark:text-backgroundDark text-white hover:opacity-80 font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-lg cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-[9/8]"
                                            data-state="closed">
                                            <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
                                              <div class="flex shrink-0 items-center justify-center size-4"><svg
                                                  xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                  viewBox="0 0 24 24" color="currentColor" fill="currentColor"
                                                  fill-rule="evenodd">
                                                  <path
                                                    d="M0 12.6663C0 13.4018 0.59792 13.9997 1.33333 13.9997C2.06875 13.9997 2.66667 13.4018 2.66667 12.6663V11.333C2.66667 10.5975 2.06875 9.99967 1.33333 9.99967C0.59792 9.99967 0 10.5975 0 11.333V12.6663ZM6.66667 5.33301C7.40213 5.33301 8 5.93087 8 6.66634V17.333C8 18.0685 7.40213 18.6663 6.66667 18.6663C5.9312 18.6663 5.33333 18.0685 5.33333 17.333V6.66634C5.33333 5.93087 5.9312 5.33301 6.66667 5.33301ZM10.6667 21.333C10.6667 22.0685 11.2645 22.6663 12 22.6663C12.7355 22.6663 13.3333 22.0685 13.3333 21.333V2.66634C13.3333 1.93093 12.7355 1.33301 12 1.33301C11.2645 1.33301 10.6667 1.93093 10.6667 2.66634V21.333ZM17.3333 5.33301C18.0688 5.33301 18.6667 5.93087 18.6667 6.66634V17.333C18.6667 18.0685 18.0688 18.6663 17.3333 18.6663C16.5979 18.6663 16 18.0685 16 17.333V6.66634C16 5.93087 16.5979 5.33301 17.3333 5.33301ZM24 11.333C24 10.5975 23.4021 9.99967 22.6667 9.99967C21.9312 9.99967 21.3333 10.5975 21.3333 11.333V12.6663C21.3333 13.4018 21.9312 13.9997 22.6667 13.9997C23.4021 13.9997 24 13.4018 24 12.6663V11.333Z">
                                                  </path>
                                                </svg></div>
                                            </div>
                                          </button></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </span></div>
                          </div>
                        </div>
                        <div class="relative w-full">
                          <div class="mt-lg absolute w-full"><!--$-->
                            <div class="animate-in fade-in duration-300"></div><!--/$-->
                          </div>
                        </div>
                      </div>
                      <div class="pb-md hidden md:block">
                        <div>
                          <div
                            class="gap-x-md gap-y-sm px-sm pb-2xl flex flex-wrap items-center md:justify-center md:px-0 md:pb-0 border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
                            <a href="https://www.perplexity.ai/pro">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Pro</div>
                              </div>
                            </a><a target="_blank" href="https://www.perplexity.ai/enterprise">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Enterprise</div>
                              </div>
                            </a><a target="_blank" href="https://sonar.perplexity.ai/">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  API</div>
                              </div>
                            </a><a target="_blank" href="https://www.perplexity.ai/hub">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Blog</div>
                              </div>
                            </a><a target="_blank" href="https://www.perplexity.ai/hub/legal/privacy-policy">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Privacy</div>
                              </div>
                            </a><a target="_blank" href="https://www.perplexity.ai/hub/careers">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Careers</div>
                              </div>
                            </a><a target="_blank" href="https://www.perplexity.supply/">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Store</div>
                              </div>
                            </a><a target="_blank" href="https://www.perplexity.ai/finance">
                              <div>
                                <div
                                  class="decoration-textOff/40 dark:decoration-textOffDark/40 hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                  Finance</div>
                              </div>
                            </a>
                            <div
                              class="gap-xs decoration-textOff/40 dark:decoration-textOffDark/40 relative flex cursor-pointer items-center justify-stretch overflow-hidden hover:underline font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                              English<div class="absolute left-0 top-0 opacity-0">
                                <div>
                                  <div
                                    class="font-sans text-sm text-textMain dark:text-textMainDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                    <div class="relative flex items-center"><select id="interface-language-select"
                                        class="border-borderMain/75 bg-background p-sm pr-lg outline-super ring-borderMain/75 dark:border-borderMainDark dark:bg-offsetDark dark:outline-superDark dark:ring-borderMainDark w-full appearance-none rounded border transition duration-300 focus:outline-none cursor-pointer hover:ring-1"
                                        aria-label="Language:">
                                        <option value="en-US" selected="">English</option>
                                        <option value="fr-FR">français</option>
                                        <option value="de-DE">Deutsch</option>
                                        <option value="ja-JP">日本語</option>
                                        <option value="ko-KR">한국어</option>
                                        <option value="zh-CN">简体中文</option>
                                        <option value="zh-TW">繁體中文</option>
                                        <option value="es-ES">español</option>
                                        <option value="hi-IN">हिन्दी</option>
                                        <option value="it-IT">italiano</option>
                                        <option value="pt-BR">português (Brasil)</option>
                                        <option value="cs-CZ">čeština</option>
                                        <option value="hr-HR">hrvatski</option>
                                        <option value="hu-HU">magyar</option>
                                        <option value="pl-PL">polski</option>
                                        <option value="pt-PT">português europeu</option>
                                        <option value="sk-SK">slovenčina</option>
                                        <option value="sr-Cyrl-ME">српски (ћирилица, Црна Гора)</option>
                                        <option value="nl-NL">Nederlands</option>
                                        <option value="el-GR">Ελληνικά</option>
                                        <option value="ro-RO">română</option>
                                        <option value="id-ID">Indonesia</option>
                                      </select>
                                      <div
                                        class="right-sm pointer-events-none absolute font-sans text-sm text-textOff dark:text-textOffDark selection:bg-super/50 selection:text-textMain dark:selection:bg-superDuper/10 dark:selection:text-superDark">
                                        <svg aria-hidden="true" focusable="false" data-prefix="far"
                                          data-icon="chevron-down" class="svg-inline--fa fa-chevron-down " role="img"
                                          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                          <path fill="currentColor"
                                            d="M239 401c9.4 9.4 24.6 9.4 33.9 0L465 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 175c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 401z">
                                          </path>
                                        </svg></div>
                                    </div>
                                  </div>
                                </div>
                              </div><svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="chevron-down"
                                class="svg-inline--fa fa-chevron-down fa-fw fa-xs text-textOff dark:text-textOffDark"
                                role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                <path fill="currentColor"
                                  d="M239 401c9.4 9.4 24.6 9.4 33.9 0L465 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-175 175L81 175c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 401z">
                                </path>
                              </svg></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="md:hidden"></div><!--$--><!--/$--><!--$--><!--/$-->
            </div>
            <div
              class="rounded-inherit pointer-events-none absolute inset-0 z-20 hidden md:dark:block md:erp-tab:!border-y-0 md:erp-tab:!border-r-0 md:dark:border border-borderMain/50 ring-borderMain/50 divide-borderMain/50 dark:divide-borderMainDark/50 dark:ring-borderMainDark/50 dark:border-borderMainDark/50 bg-transparent">
            </div>
          </div>
        </div><!--$--><!--/$-->
      </div>
    </div>
    <div class="bottom-md right-md m-sm fixed hidden md:block">
      <div class="flex items-center gap-2"><span><button aria-label="Help menu" type="button"
            class="bg-offsetPlus dark:bg-offsetPlusDark text-textMain dark:text-textMainDark  md:hover:text-textOff md:dark:hover:text-textOffDark !bg-background dark:border-borderMain/25 hover:!text-text !text-text-200 dark:!bg-offset border shadow-subtle border-borderMain/50 font-sans focus:outline-none outline-none outline-transparent transition duration-300 ease-out font-sans  select-none items-center relative group/button  justify-center text-center items-center rounded-full cursor-pointer active:scale-[0.97] active:duration-150 active:ease-outExpo origin-center whitespace-nowrap inline-flex text-sm h-8 aspect-square">
            <div class="flex items-center min-w-0 font-medium gap-1.5 justify-center">
              <div class="flex shrink-0 items-center justify-center size-4"><svg xmlns="http://www.w3.org/2000/svg"
                  width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                  stroke-width="1.7999999999999998" stroke-linecap="round" stroke-linejoin="round"
                  class="tabler-icon tabler-icon-question-mark ">
                  <path d="M8 8a3.5 3 0 0 1 3.5 -3h1a3.5 3 0 0 1 3.5 3a3 3 0 0 1 -2 3a3 4 0 0 0 -2 4"></path>
                  <path d="M12 19l0 .01"></path>
                </svg></div>
            </div>
          </button></span></div>
    </div>
  </main>
  <script src="./Perplexity_files/webpack-8ceae12d4aebc5a8.js.download" async=""></script>
  <script>(self.__next_f = self.__next_f || []).push([0])</script>
  <script>self.__next_f.push([1, "1:\"$Sreact.fragment\"\n7:I[92738,[],\"MetadataBoundary\"]\n9:I[92738,[],\"OutletBoundary\"]\nc:I[68114,[],\"AsyncMetadataOutlet\"]\ne:I[87069,[],\"\"]\nf:I[89289,[],\"\"]\n14:I[92738,[],\"ViewportBoundary\"]\n16:I[65781,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"34219\",\"static/chunks/app/global-error-df6a13be8a2d8d8a.js\"],\"default\"]\n17:I[34430,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"10252\",\"static/chunks/10252-522ef8d04104fe0d.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877"])</script>
  <script>self.__next_f.push([1, "\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"74413\",\"static/chunks/app/(client)/(with-sidebar)/layout-d9efac596b131fd5.js\"],\"Wrapper\"]\n"])</script>
  <script>self.__next_f.push([1, "18:I[27405,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"81214\",\"static/chunks/81214-3257f492513c717b.js\",\"59273\",\"static/chunks/59273-536804a2287e6e11.js\",\"5926\",\"static/chunks/5926-231dbd8144c8f944.js\",\"71102\",\"static/chunks/71102-2a50fa66c4ede2ac.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"62072\",\"static/chunks/62072-bf535963648c9c50.js\",\"25929\",\"static/chunks/25929-215e5f1fa294f903.js\",\"86786\",\"static/chunks/86786-82ba5c4b462974ca.js\",\"43807\",\"static/chunks/43807-c1b14eb60e06dd29.js\",\"64646\",\"static/chunks/64646-65e5bc6fa1d2aa40.js\",\"13847\",\"static/chunks/13847-2284e62e9b406169.js\",\"23105\",\"static/chunks/23105-558f6361c141be03.js\",\"10999\",\"static/chunks/10999-5dbd6f0c95afff99.js\",\"78370\",\"static/chunks/78370-3f1104c89c9c1543.js\",\"2529\",\"static/chunks/2529-141ff58e43a9c532.js\",\"29660\",\"static/chunks/app/(client)/(with-sidebar)/(search)/page-c0aaa35c98883cc4.js\"],\"HydrationBoundary\"]\n"])</script>
  <script>self.__next_f.push([1, "19:I[2311,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"10252\",\"static/chunks/10252-522ef8d04104fe0d.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"74413\",\"static/chunks/app/(client)/(with-sidebar)/layout-d9efac596b131fd5.js\"],\"LayoutGroup\"]\n1a:I[34430,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform"])</script>
  <script>self.__next_f.push([1, "-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"10252\",\"static/chunks/10252-522ef8d04104fe0d.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"74413\",\"static/chunks/app/(client)/(with-sidebar)/layout-d9efac596b131fd5.js\"],\"Sidebar\"]\n1b:I[70858,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/609"])</script>
  <script>self.__next_f.push([1, "18-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"10252\",\"static/chunks/10252-522ef8d04104fe0d.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"74413\",\"static/chunks/app/(client)/(with-sidebar)/layout-d9efac596b131fd5.js\"],\"SearchSidebar\"]\n1c:I[34430,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/ch"])</script>
  <script>self.__next_f.push([1, "unks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"10252\",\"static/chunks/10252-522ef8d04104fe0d.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"74413\",\"static/chunks/app/(client)/(with-sidebar)/layout-d9efac596b131fd5.js\"],\"ContentWrapper\"]\n1d:I[85619,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"41538\",\""])</script>
  <script>self.__next_f.push([1, "static/chunks/41538-658884d547c09a73.js\",\"99409\",\"static/chunks/app/(client)/(with-sidebar)/template-30be025d2387095d.js\"],\"default\"]\n1e:I[88053,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"68269\",\"static/chunks/app/(client)/(with-sidebar)/not-found-0660f34e13aa816e.js\"],\"NotFoundComponent\"]\n20:I[10036,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"76719\",\"static/chunks/app/(client)/(with-sidebar)/(search)/layout-9f3a24e1e74ef5fb.js\"],\"default\"]\n21:\"$Sreact.suspense\"\n22:I[68114,[],\"AsyncMetadata\"]\n26:I[5942,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\""])</script>
  <script>self.__next_f.push([1, ",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"49370\",\"static/chunks/49370-44622f5d3cf7b73f.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"76447\",\"static/chunks/76447-6a06b600177364e4.js\",\"4747\",\"static/chunks/app/(client)/(with-sidebar)/%40aside/page-7575f34ce140a652.js\"],\"SplashScreen\"]\n27:I[85443,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432d"])</script>
  <script>self.__next_f.push([1, "e57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"28164\",\"static/chunks/28164-df40cf3ab104eacb.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"85443\",\"static/chunks/85443-30bacf8f0cba587e.js\",\"39170\",\"static/chunks/39170-bd963ba309c7e5be.js\",\"86522\",\"static/chunks/app/(client)/layout-f618c8343e23520a.js\"],\"ClientProviders\"]\n28:I[86990,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"28164\",\"static/chunks/28164-df40cf3ab104eacb.js\",\"41538\",\"static/chunks/41538-658884d547c09a73"])</script>
  <script>self.__next_f.push([1, ".js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"85443\",\"static/chunks/85443-30bacf8f0cba587e.js\",\"39170\",\"static/chunks/39170-bd963ba309c7e5be.js\",\"86522\",\"static/chunks/app/(client)/layout-f618c8343e23520a.js\"],\"FloatingComponents\"]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/18a8383f82b767c1-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/24437d246c9412ba-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/2ba03bcf4600f506-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/3c0a20a48548ae37-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/404d16a48cfd5dcd-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/80d90e1e722a80dc-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/b06d54d0a237533a-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/b8107bede19baa40-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/media/e8d05d87005ce07f-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/css/5d9e8c7911b8604f.css\",\"style\"]\n:HL[\"https://pplx-next-static-public.perplexity.ai/_next/static/css/908b88a79b4232ac.css\",\"style\"]\n:HL"])</script>
  <script>self.__next_f.push([1, "[\"https://pplx-next-static-public.perplexity.ai/_next/static/css/e4e6af58e8daa9de.css\",\"style\"]\n"])</script>
  <script>self.__next_f.push([1, "0:{\"P\":null,\"b\":\"Wl_pOb5gFV7bTVSd7hy7B\",\"p\":\"https://pplx-next-static-public.perplexity.ai\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(client)\",{\"children\":[\"(with-sidebar)\",{\"children\":[\"(search)\",{\"children\":[\"__PAGE__\",{}]}],\"aside\":[\"__PAGE__\",{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://pplx-next-static-public.perplexity.ai/_next/static/css/5d9e8c7911b8604f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://pplx-next-static-public.perplexity.ai/_next/static/css/908b88a79b4232ac.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://pplx-next-static-public.perplexity.ai/_next/static/css/e4e6af58e8daa9de.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L2\"]}],{\"children\":[\"(client)\",[\"$\",\"$1\",\"c\",{\"children\":[null,\"$L3\"]}],{\"children\":[\"(with-sidebar)\",[\"$\",\"$1\",\"c\",{\"children\":[null,\"$L4\"]}],{\"children\":[\"(search)\",[\"$\",\"$1\",\"c\",{\"children\":[null,\"$L5\"]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L6\",[\"$\",\"$L7\",null,{\"children\":\"$L8\"}],null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false],\"aside\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Le\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L10\",[\"$\",\"$L7\",null,{\"children\":\"$L11\"}],null,[\"$\",\"$L9\",null,{\"children\":[\"$L12\",\"$L13\",\"$undefined\"]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"E33HJuzx68wAeJPZowrhm\",{\"children\":[[\"$\",\"$L14\",null,{\"children\":\"$L15\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$16\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://pplx-next-static-public.perplexity.ai/_next/static/css/5d9e8c7911b8604f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://pplx-next-static-public.perplexity.ai/_next/static/css/908b88a79b4232ac.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://pplx-next-static-public.perplexity.ai/_next/static/css/e4e6af58e8daa9de.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]]],\"s\":false,\"S\":false}\n"])</script>
  <script>self.__next_f.push([1, "4:[\"$\",\"$L17\",null,{\"children\":[\"$\",\"$L18\",null,{\"children\":[[\"$\",\"$L19\",null,{\"children\":[[\"$\",\"$L1a\",null,{\"children\":[\"$\",\"$L1b\",null,{}]}],[\"$\",\"$L1c\",null,{\"hasSidebar\":true,\"children\":[\"$\",\"$Le\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L1d\",null,{\"children\":[\"$\",\"$Lf\",null,{}]}],\"templateStyles\":[],\"templateScripts\":[],\"notFound\":[[\"$\",\"$L1e\",null,{}],[\"$\",\"$L7\",null,{\"children\":\"$L1f\"}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}],[\"$\",\"$Le\",null,{\"parallelRouterKey\":\"aside\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L1d\",null,{\"children\":[\"$\",\"$Lf\",null,{}]}],\"templateStyles\":\"$4:props:children:props:children:0:props:children:1:props:children:props:templateStyles\",\"templateScripts\":\"$4:props:children:props:children:0:props:children:1:props:children:props:templateScripts\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]}]\n5:[\"$\",\"$L20\",null,{\"children\":[\"$\",\"$Le\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]\n8:[\"$\",\"$21\",null,{\"fallback\":null,\"children\":[\"$\",\"$L22\",null,{\"promise\":\"$@23\"}]}]\n11:[\"$\",\"$21\",null,{\"fallback\":null,\"children\":[\"$\",\"$L22\",null,{\"promise\":\"$@24\"}]}]\nb:null\n12:null\n13:null\n1f:[\"$\",\"$21\",null,{\"fallback\":null,\"children\":[\"$\",\"$L22\",null,{\"promise\":\"$@25\"}]}]\n10:[\"$\",\"$L26\",null,{}]\n3:[\"$\",\"$L27\",null,{\"isMobileUserAgent\":false,\"hostname\":\"www.perplexity.ai\",\"version\":\"11088d5\",\"cfCountry\":\"FI\",\"spaRoutes\":[\"/library\",\"/search/*\",\"/b/home\",\"/b/speak\",\"/\"],\"children\":[[\"$\",\"$Le\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"template"])</script>
  <script>self.__next_f.push([1, "Scripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L28\",null,{}]]}]\n15:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\na:null\n23:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Perplexity\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"robots\",\"content\":\"index\"}],[\"$\",\"link\",\"3\",{\"rel\":\"canonical\",\"href\":\"https://www.perplexity.ai\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"Perplexity\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:description\",\"content\":\"Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:url\",\"content\":\"https://www.perplexity.ai\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:site_name\",\"content\":\"Perplexity AI\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image\",\"content\":\"https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:site\",\"content\":\"@perplexity_ai\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:title\",\"content\":\"Perplexity\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:description\",\"content\":\"Perplexity is a free AI-powered answer engine that provides accurate, trusted, and real-time answers to any question.\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:image\",\"content\":\"https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png\"}]],\"error\":null,\"digest\":\"$undefined\"}\nd:{\"metadata\":\"$23:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n24:{\"metadata\":\"$23:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n25:{\"metadata\":\"$23:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script>
  <script>self.__next_f.push([1, "29:I[9229,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"TranslationsProvider\"]\n2a:I[86897,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"AppRouterSessionProvider\"]\n2b:I[78927,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1"])</script>
  <script>self.__next_f.push([1, "490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"EnvProvider\"]\n2c:I[70472,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"DeviceProvider\"]\n2d:I[79465,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"PerformanceProvider\"]\n2e:I[69584,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"sta"])</script>
  <script>self.__next_f.push([1, "tic/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"LocalUserSettingsProvider\"]\n2f:I[4916,[\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"46397\",\"static/chunks/46397-cbdc9f1490caf6f3.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"7177\",\"static/chunks/app/layout-21d4c68965419bd6.js\"],\"DebugModeProvider\"]\n"])</script>
  <script>self.__next_f.push([1, "2:[\"$\",\"html\",null,{\"lang\":\"en\",\"data-color-scheme\":\"$undefined\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"script\",null,{\"src\":\"https://accounts.google.com/gsi/client\",\"data-client_id\":\"***********-30175ip7vg79fobh0rk1sur3pdutj9l1.apps.googleusercontent.com\",\"data-use_fedcm_for_prompt\":\"true\",\"async\":true,\"defer\":true}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"media\":\"(prefers-color-scheme: light)\",\"content\":\"#FCFCF9\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"media\":\"(prefers-color-scheme: dark)\",\"content\":\"#100E12\"}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n            window.__PPL_CONFIG__={\\\"env\\\":\\\"beta\\\",\\\"version\\\":\\\"11088d5\\\",\\\"country\\\":\\\"FI\\\",\\\"isInternal\\\":false,\\\"noDDSampling\\\":false,\\\"blockO11y\\\":false};\\n            const isComet = document.cookie.includes('comet_browser=');\\n            let erp;\\n            if (window.location.pathname.startsWith('/sidecar')) {\\n              erp = 'sidecar';\\n            } else if (isComet) {\\n              erp = 'tab';\\n            }\\n            if (erp) {\\n              window.__PPL_CONFIG__.erp = erp;\\n              document.documentElement.dataset.erp = erp;\\n              if (erp === 'sidecar') {\\n                document.head.insertAdjacentHTML('beforeend', '\u003cmeta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\\\" /\u003e');\\n              }\\n            }\\n          \"}}]]}],[\"$\",\"body\",null,{\"id\":\"__next\",\"className\":\"__variable_598ab8 __variable_dd3642 __variable_8a67e8 __variable_1826c3 __variable_f8d077 bg-transparent md:bg-offset dark:md:bg-offsetDark\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"main\",null,{\"children\":[\"$\",\"$L29\",null,{\"locale\":\"en-US\",\"messages\":{},\"children\":[\"$\",\"$L2a\",null,{\"session\":{\"user\":{\"name\":\"Sukarth Acharya\",\"email\":\"<EMAIL>\",\"image\":\"https://imagedelivery.net/MPdwyYSWT8IY7lxgN3x3Uw/68696eb1-9a76-4ac1-35ea-2e284110c200/thumbnail\",\"id\":\"e7abc89a-805c-4e85-bce6-e823e83363af\",\"username\":\"sukarth\",\"subscription_status\":\"active\",\"subscription_source\":\"stripe\",\"payment_tier\":\"free\",\"subscription_tier\":\"$undefined\",\"org_uuid\":\"none\"},\"preventUsernameRedirect\":false},\"children\":[\"$\",\"$L2b\",null,{\"hostname\":\"www.perplexity.ai\",\"children\":[\"$\",\"$L2c\",null,{\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"children\":[\"$\",\"$L2d\",null,{\"children\":[\"$\",\"$L2e\",null,{\"initialLocalUserSettings\":{\"colorScheme\":null,\"isCollapsed\":false,\"isSidebarPinned\":true,\"sidebarHiddenHubs\":[],\"isIncognitoLocal\":false,\"isDiscoverInterestBoxDismissed\":false,\"isEnterpriseAdDismissed\":false,\"isSplashPageDismissed\":false,\"isSuggestionsDisabled\":false,\"isHomepageWidgetsDisabled\":false,\"isSportsIosCtaDismissed\":false,\"prefersMetric\":true,\"preferredSearchMode\":\"search\",\"preferredSearchModels\":{\"pro\":\"experimental\",\"deepResearch\":\"pplx_pro\",\"research\":\"pplx_pro\",\"search\":\"gpt41\",\"studio\":\"pplx_beta\"},\"welcomeBackGateImpressions\":0,\"shareGateImpressions\":0,\"isSidebarConnectorsAdDismissed\":false,\"isStudentReferralBannerDismissed\":false,\"searchModeSelectorVariant\":\"studio-enabled\"},\"children\":[\"$\",\"$L2f\",null,{\"children\":[\"$\",\"$Le\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L1e\",null,{}],[\"$\",\"$L7\",null,{\"children\":\"$L30\"}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]}]}]}]}]}]}]]}]\n"])</script>
  <script>self.__next_f.push([1, "30:[\"$\",\"$21\",null,{\"fallback\":null,\"children\":[\"$\",\"$L22\",null,{\"promise\":\"$@31\"}]}]\n"])</script>
  <script>self.__next_f.push([1, "32:I[27642,[\"18096\",\"static/chunks/vendors-f40c91eab496ff19.js\",\"12646\",\"static/chunks/platform-62f251fa873007be.js\",\"48889\",\"static/chunks/48889-2ea45587017b0b00.js\",\"60918\",\"static/chunks/60918-41acced2566469f2.js\",\"51595\",\"static/chunks/51595-062a07070b16189c.js\",\"61180\",\"static/chunks/61180-68c59335fab2442e.js\",\"86779\",\"static/chunks/86779-1c26cd061baf0551.js\",\"19374\",\"static/chunks/19374-b4a4aeee41e8518c.js\",\"64969\",\"static/chunks/64969-ff1d19fdb5861153.js\",\"73519\",\"static/chunks/73519-8676b4034213c528.js\",\"26245\",\"static/chunks/26245-42f37aa8f4114a17.js\",\"94310\",\"static/chunks/94310-eb4a9b9fe06f7076.js\",\"30570\",\"static/chunks/30570-1b00b65676c8fb6c.js\",\"22895\",\"static/chunks/22895-03aee5d495ec0f94.js\",\"82025\",\"static/chunks/82025-e641c922a2afd4b6.js\",\"40253\",\"static/chunks/40253-a5d50f028130ccb0.js\",\"72580\",\"static/chunks/72580-342e2d115343050f.js\",\"49550\",\"static/chunks/49550-a55583ffaf24f915.js\",\"43674\",\"static/chunks/43674-c12eb33bd63d2eec.js\",\"2606\",\"static/chunks/2606-5ab8d707d432de57.js\",\"45453\",\"static/chunks/45453-afb382740628104d.js\",\"99216\",\"static/chunks/99216-1dc8d87a624248fc.js\",\"7772\",\"static/chunks/7772-58bbccbc8c9121ef.js\",\"86021\",\"static/chunks/86021-4e307c93b9f9fbad.js\",\"69560\",\"static/chunks/69560-db42ef9bea916342.js\",\"72338\",\"static/chunks/72338-b384540928dd7db8.js\",\"81214\",\"static/chunks/81214-3257f492513c717b.js\",\"59273\",\"static/chunks/59273-536804a2287e6e11.js\",\"5926\",\"static/chunks/5926-231dbd8144c8f944.js\",\"71102\",\"static/chunks/71102-2a50fa66c4ede2ac.js\",\"41538\",\"static/chunks/41538-658884d547c09a73.js\",\"26163\",\"static/chunks/26163-81537aabf94fb292.js\",\"20056\",\"static/chunks/20056-eefe8fcb680399d1.js\",\"64877\",\"static/chunks/64877-4ec7cf58494facd4.js\",\"75860\",\"static/chunks/75860-e82ef11b9f8eacb4.js\",\"34816\",\"static/chunks/34816-d40a1ec463d8adf4.js\",\"84753\",\"static/chunks/84753-5333e064967b5460.js\",\"62785\",\"static/chunks/62785-de81aed81fd487e0.js\",\"51932\",\"static/chunks/51932-011e021506795fa6.js\",\"926\",\"static/chunks/926-63c94a45c94246ba.js\",\"62072\",\"static/chunks/62072-bf535963648c9c50.js\",\"25929\",\"static/chunks/25929-215e5f1fa294f903.js\",\"86786\",\"static/chunks/86786-82ba5c4b462974ca.js\",\"43807\",\"static/chunks/43807-c1b14eb60e06dd29.js\",\"64646\",\"static/chunks/64646-65e5bc6fa1d2aa40.js\",\"13847\",\"static/chunks/13847-2284e62e9b406169.js\",\"23105\",\"static/chunks/23105-558f6361c141be03.js\",\"10999\",\"static/chunks/10999-5dbd6f0c95afff99.js\",\"78370\",\"static/chunks/78370-3f1104c89c9c1543.js\",\"2529\",\"static/chunks/2529-141ff58e43a9c532.js\",\"29660\",\"static/chunks/app/(client)/(with-sidebar)/(search)/page-c0aaa35c98883cc4.js\"],\"default\"]\n"])</script>
  <script>self.__next_f.push([1, "31:{\"metadata\":\"$23:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n6:[\"$\",\"$L32\",null,{\"widgets\":\"$@33\",\"fetchWidgetsClientside\":false}]\n"])</script>
  <script>self.__next_f.push([1, "33:[]\n"])</script>
  <script>$RS = function (a, b) { a = document.getElementById(a); b = document.getElementById(b); for (a.parentNode.removeChild(a); a.firstChild;)b.parentNode.insertBefore(a.firstChild, b); b.parentNode.removeChild(b) }; $RS("S:1", "P:1")</script>
  <script>$RC = function (b, c, e) { c = document.getElementById(c); c.parentNode.removeChild(c); var a = document.getElementById(b); if (a) { b = a.previousSibling; if (e) b.data = "$!", a.setAttribute("data-dgst", e); else { e = b.parentNode; a = b.nextSibling; var f = 0; do { if (a && 8 === a.nodeType) { var d = a.data; if ("/$" === d) if (0 === f) break; else f--; else "$" !== d && "$?" !== d && "$!" !== d || f++ } d = a.nextSibling; e.removeChild(a); a = d } while (a); for (; c.firstChild;)e.insertBefore(c.firstChild, a); b.data = "$" } b._reactRetry && b._reactRetry() } }; $RC("B:0", "S:0")</script>
  <script>(function () { function c() { var b = a.contentDocument || a.contentWindow.document; if (b) { var d = b.createElement('script'); d.innerHTML = "window.__CF$cv$params={r:'948366facaca8da4',t:'MTc0ODY2MjMyNy4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);"; b.getElementsByTagName('head')[0].appendChild(d) } } if (document.body) { var a = document.createElement('iframe'); a.height = 1; a.width = 1; a.style.position = 'absolute'; a.style.top = 0; a.style.left = 0; a.style.border = 'none'; a.style.visibility = 'hidden'; document.body.appendChild(a); if ('loading' !== document.readyState) c(); else if (window.addEventListener) document.addEventListener('DOMContentLoaded', c); else { var e = document.onreadystatechange || function () { }; document.onreadystatechange = function (b) { e(b); 'loading' !== document.readyState && (document.onreadystatechange = e, c()) } } } })();</script>
  <iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;"
    src="./Perplexity_files/saved_resource.html"></iframe><textarea tabindex="-1" aria-hidden="true"
    style="min-height: 0px !important; max-height: none !important; height: 0px !important; visibility: hidden !important; overflow: hidden !important; position: absolute !important; z-index: -1000 !important; top: 0px !important; right: 0px !important; display: block !important; border-width: 0px; box-sizing: border-box; font-family: fkGroteskNeue, &quot;fkGroteskNeue Fallback&quot;, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: normal; line-height: 24px; padding: 0px; tab-size: 4; text-indent: 0px; text-rendering: auto; text-transform: none; width: 606px; word-break: normal; word-spacing: 0px; scrollbar-gutter: auto;"></textarea>
  <script src="./Perplexity_files/api.js.download" id="google-drive-picker-gapi-script" gapi_processed="true"></script>
  <next-route-announcer style="position: absolute;"><template shadowrootmode="open">
      <div aria-live="assertive" id="__next-route-announcer__" role="alert"
        style="position: absolute; border: 0px; height: 1px; margin: -1px; padding: 0px; width: 1px; clip: rect(0px, 0px, 0px, 0px); overflow: hidden; white-space: nowrap; overflow-wrap: normal;">
      </div>
    </template></next-route-announcer>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div data-type="portal"></div>
  <div id="mcp-panel" class="mcp-panel">
    <div class="mcp-header">
      <span class="mcp-title">🔧 MCP Tools</span>
      <div class="mcp-status" id="mcp-status">
        <span class="status-indicator connected"></span>
        <span class="status-text">Connected</span>
      </div>
    </div>
    <div class="mcp-content">
      <div class="mcp-servers" id="mcp-servers">
        <div class="mcp-server" data-server-id="filesystem">
          <div class="server-header">
            <span class="server-name">filesystem</span>
            <span class="server-status running">running</span>
          </div>
          <div class="server-actions">
            <button class="server-btn test-connection-btn" data-server-id="filesystem">
              Test Connection
            </button>
          </div>
        </div>
      </div>
    </div>
  </div><button id="mcp-toggle" class="mcp-toggle-btn" title="Toggle MCP Tools">🔧</button>
  <div data-type="portal"></div>
  <script src="./Perplexity_files/client" id="google-drive-picker-gis-script"></script>
</body>

</html>