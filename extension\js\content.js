// Content script for Perplexity Web MCP Bridge
// This script runs in the page context and communicates with the bridge

(function () {
  'use strict';

  // Prevent multiple initializations
  if (window.mcpContentScriptLoaded) {
    console.log('[Perplexity MCP] Content script already loaded, skipping');
    return;
  }
  window.mcpContentScriptLoaded = true;

  // Check if already initialized
  if (window.mcpClient && window.mcpClient.isInitialized) {
    console.log('[Perplexity MCP] Client already initialized, skipping');
    return;
  }

  class PerplexityMcpClient {
    constructor() {
      this.ws = null;
      this.isConnected = false;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.maxReconnectAttempts = 5;
      this.mcpServers = [];
      this.pendingRequests = new Map();
      this.requestId = 0;
      this.isInitialized = false;

      // MCP Settings
      this.settings = {
        mcpEnabled: true,
        alwaysInject: false,
        serverSettings: new Map() // serverId -> { enabled: true }
      };

      // Response monitoring
      this.responseObserver = null;
      this.lastProcessedResponseCount = 0;

      this.init();
    }

    async init() {
      if (this.isInitialized) {
        console.log('[Perplexity MCP] Already initialized, skipping');
        return;
      }

      console.log('[Perplexity MCP] Initializing...');
      this.isInitialized = true;

      // Only connect to WebSocket and setup global interface - no MCP panel
      this.connectToWebSocket();
      // this.observePerplexityChanges();
      this.setupGlobalMcpInterface();

      // Add status indicators and response monitoring
      setTimeout(() => {
        this.addStatusIndicators();
        this.startResponseMonitoring();
      }, 1000);

      // Wait a bit for connection, then set up prompt enhancement
      setTimeout(() => {
        this.injectPromptEnhancement();
      }, 2000);

      // Start response monitoring for MCP tool execution
      setTimeout(() => {
        this.startResponseMonitoring();
      }, 3000);
    }

    connectToWebSocket() {
      try {
        console.log('[Perplexity MCP] Connecting to bridge...');
        this.isConnecting = true;
        this.isConnected = false;
        this.updateConnectionStatus('connecting');

        this.ws = new WebSocket('ws://localhost:54319');

        this.ws.onopen = () => {
          console.log('[Perplexity MCP] Connected to bridge');
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.updateConnectionStatus('connected');
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('[Perplexity MCP] Message parse error:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('[Perplexity MCP] Connection closed');
          this.isConnected = false;
          this.isConnecting = false;
          this.updateConnectionStatus('disconnected');
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('[Perplexity MCP] WebSocket error:', error);
          this.isConnected = false;
          this.isConnecting = false;
          this.updateConnectionStatus('disconnected');
        };

      } catch (error) {
        console.error('[Perplexity MCP] Failed to connect:', error);
        this.isConnected = false;
        this.isConnecting = false;
        this.updateConnectionStatus('disconnected');
        this.attemptReconnect();
      }
    }

    attemptReconnect() {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 10000);
        console.log(`[Perplexity MCP] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
        setTimeout(() => this.connectToWebSocket(), delay);
      } else {
        console.log('[Perplexity MCP] Max reconnect attempts reached');
        this.isConnecting = false;
        this.updateConnectionStatus('disconnected');
      }
    }

    handleMessage(message) {
      switch (message.type) {
        case 'servers':
          this.mcpServers = message.servers;
          this.updateServerList();
          this.fetchServerTools(); // Fetch tools for prompt integration
          this.updateServerControls(); // Update debug panel server controls
          this.updateDebugStatus(); // Update debug panel status
          break;
        case 'mcp_response':
          this.handleMcpResponse(message);
          break;
        default:
          console.log('[Perplexity MCP] Unknown message type:', message.type);
      }
    }

    handleMcpResponse(message) {
      const pending = this.pendingRequests.get(message.id);
      if (pending) {
        this.pendingRequests.delete(message.id);
        pending.resolve(message.result || message.error);
      }
    }

    async callMcpTool(serverId, method, params = {}) {
      if (!this.isConnected) {
        throw new Error('Not connected to MCP bridge');
      }

      const id = ++this.requestId;
      const request = {
        id: id,
        type: 'mcp_request',
        serverId: serverId,
        request: {
          jsonrpc: '2.0',
          method: method,
          params: params,
          id: id
        }
      };

      return new Promise((resolve, reject) => {
        this.pendingRequests.set(id, { resolve, reject });
        this.ws.send(JSON.stringify(request));

        // Timeout after 5 seconds for better UX
        setTimeout(() => {
          if (this.pendingRequests.has(id)) {
            this.pendingRequests.delete(id);
            reject(new Error('Request timeout'));
          }
        }, 5000);
      });
    }

    // injectMcpInterface() {
    //   // Create MCP panel
    //   const mcpPanel = document.createElement('div');
    //   mcpPanel.id = 'mcp-panel';
    //   mcpPanel.className = 'mcp-panel';
    //   mcpPanel.innerHTML = `
    //   <div class="mcp-header">
    //     <span class="mcp-title">🔧 MCP Tools</span>
    //     <div class="mcp-status" id="mcp-status">
    //       <span class="status-indicator"></span>
    //       <span class="status-text">Connecting...</span>
    //     </div>
    //   </div>
    //   <div class="mcp-content">
    //     <div class="mcp-servers" id="mcp-servers">
    //       <div class="loading">Loading MCP servers...</div>
    //     </div>
    //   </div>
    // `;

    //   // Find insertion point in Perplexity's layout
    //   this.insertMcpPanel(mcpPanel);

    //   // Add event listeners for the panel
    //   this.setupPanelEventListeners();

    //   // Add toggle button
    //   this.addMcpToggleButton();
    // }

    insertMcpPanel(panel) {
      // For fixed positioning, always append to body to avoid container interference
      document.body.appendChild(panel);
      console.log('[Perplexity MCP] Panel inserted');
    }

    setupPanelEventListeners() {
      // Use event delegation to handle dynamically created buttons
      document.addEventListener('click', (event) => {
        if (event.target.classList.contains('test-connection-btn')) {
          const serverId = event.target.getAttribute('data-server-id');
          if (serverId) {
            this.testServer(serverId);
          }
        }
      });
    }

    addMcpToggleButton() {
      const toggleBtn = document.createElement('button');
      toggleBtn.id = 'mcp-toggle';
      toggleBtn.className = 'mcp-toggle-btn';
      toggleBtn.innerHTML = '🔧';
      toggleBtn.title = 'Toggle MCP Tools';
      toggleBtn.onclick = () => this.toggleMcpPanel();

      // Insert toggle button
      const header = document.querySelector('header') || document.querySelector('nav') || document.body;
      header.appendChild(toggleBtn);
    }

    toggleMcpPanel() {
      const panel = document.getElementById('mcp-panel');
      if (panel) {
        panel.classList.toggle('hidden');
      }
    }

    updateConnectionStatus(state) {
      const statusEl = document.getElementById('mcp-status');
      if (statusEl) {
        const indicator = statusEl.querySelector('.status-indicator');
        const text = statusEl.querySelector('.status-text');

        if (state === 'connected') {
          indicator.className = 'status-indicator connected';
          text.textContent = 'Connected';
        } else if (state === 'connecting') {
          indicator.className = 'status-indicator connecting';
          text.textContent = 'Connecting...';
        } else {
          indicator.className = 'status-indicator disconnected';
          text.textContent = 'Disconnected';
        }
      }

      // Update debug panel if it exists
      this.updateDebugStatus();

      // Update status indicators
      this.updateStatusIndicators();
    }

    updateServerList() {
      const serversEl = document.getElementById('mcp-servers');
      if (!serversEl) return;

      if (this.mcpServers.length === 0) {
        serversEl.innerHTML = '<div class="no-servers">No MCP servers configured</div>';
        return;
      }

      serversEl.innerHTML = this.mcpServers.map(server => `
      <div class="mcp-server" data-server-id="${server.id}">
        <div class="server-header">
          <span class="server-name">${server.name}</span>
          <span class="server-status ${server.status}">${server.status}</span>
        </div>
        <div class="server-actions">
          <button class="server-btn test-connection-btn" data-server-id="${server.id}">
            Test Connection
          </button>
        </div>
      </div>
    `).join('');
    }

    async testServer(serverId) {
      const button = document.querySelector(`[data-server-id="${serverId}"]`);
      const originalText = button ? button.textContent : '';
      let testSucceeded = false;

      try {
        // Update button to show testing state
        if (button) {
          button.textContent = 'Testing...';
          button.disabled = true;
        }

        console.log('[Perplexity MCP] Testing server:', serverId);
        this.showNotification(`Testing connection to ${serverId}...`, 'info');

        const result = await this.callMcpTool(serverId, 'tools/list', {});
        console.log('[Perplexity MCP] Server test result:', result);

        // Check if we got tools back (successful test)
        if (result && result.tools && Array.isArray(result.tools)) {
          testSucceeded = true;
          this.showNotification(`Server ${serverId} is working! Found ${result.tools.length} tools.`, 'success');
        } else {
          this.showNotification(`Server ${serverId} responded but no tools found.`, 'warning');
        }
      } catch (error) {
        console.error('[Perplexity MCP] Server test failed:', error);

        // Handle specific error types
        if (error.message === 'Request timeout') {
          this.showNotification(`Server ${serverId}: No response (timeout) - this is expected for test-echo server`, 'warning');
        } else {
          this.showNotification(`Server ${serverId} test failed: ${error.message}`, 'error');
        }
      } finally {
        // Update button state based on test result
        if (button) {
          if (testSucceeded) {
            button.textContent = '✅ Connected & Working';
            button.classList.add('mcp-test-success');
            // Auto-restore after 5 seconds
            setTimeout(() => {
              button.textContent = originalText;
              button.classList.remove('mcp-test-success');
            }, 5000);
          } else {
            button.textContent = originalText;
          }
          button.disabled = false;
        }
      }
    }

    // observePerplexityChanges() {
    //   // Watch for changes in Perplexity's interface
    //   const observer = new MutationObserver((mutations) => {
    //     for (const mutation of mutations) {
    //       if (mutation.type === 'childList') {
    //         // Check if our MCP panel still exists
    //         if (!document.getElementById('mcp-panel')) {
    //           this.injectMcpInterface();
    //         }
    //       }
    //     }
    //   });

    //   observer.observe(document.body, {
    //     childList: true,
    //     subtree: true
    //   });
    // }

    showNotification(message, type = 'info') {
      // Create notification element
      const notification = document.createElement('div');
      notification.className = `mcp-notification ${type}`;
      notification.textContent = message;

      document.body.appendChild(notification);

      // Auto-remove after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Expose methods for use in Perplexity's interface
    getAvailableTools() {
      return this.mcpServers;
    }

    async executeToolInContext(serverId, toolName, params) {
      try {
        const result = await this.callMcpTool(serverId, `tools/call`, {
          name: toolName,
          arguments: params
        });
        return result;
      } catch (error) {
        console.error('[Perplexity MCP] Tool execution failed:', error);
        throw error;
      }
    }

    // New methods for prompt injection and tool integration
    generateMcpSystemPrompt() {
      if (!this.mcpServers || this.mcpServers.length === 0) {
        console.log('[Perplexity MCP] No servers available for system prompt');
        return '';
      }

      const availableTools = [];
      for (const server of this.mcpServers) {
        // Only include tools from enabled servers
        const serverEnabled = this.settings.serverSettings.get(server.id)?.enabled !== false;

        if (server.status === 'running' && server.tools && serverEnabled) {
          for (const tool of server.tools) {
            availableTools.push({
              serverId: server.id,
              serverName: server.name,
              name: tool.name,
              description: tool.description,
              inputSchema: tool.inputSchema
            });
          }
        }
      }

      if (availableTools.length === 0) {
        console.log('[Perplexity MCP] No tools available for system prompt');
        return '';
      }

      console.log(`[Perplexity MCP] Generating system prompt with ${availableTools.length} tools`);

      return `

## Available MCP Tools

You have access to the following MCP (Model Context Protocol) tools that can help you provide more accurate and helpful responses:

${availableTools.map(tool => `
### ${tool.name} (${tool.serverName})
**Description:** ${tool.description}
**Parameters:** ${JSON.stringify(tool.inputSchema, null, 2)}
**Usage:** To use this tool, call mcpExecuteTool("${tool.serverId}", "${tool.name}", parameters)
`).join('')}

## CRITICAL MCP TOOL USAGE RULES:

1. **ONE TOOL PER RESPONSE**: You must only call ONE MCP tool per response, never multiple tools in the same response.

2. **WAIT FOR TOOL RESULTS**: After calling an MCP tool, STOP your response immediately. Do not continue with analysis or conclusions.

3. **TOOL RESULT WORKFLOW**: 
   - Call the tool: mcpExecuteTool("serverId", "toolName", parameters)
   - End your response immediately after the tool call
   - Wait for the user's next message which will contain the tool results
   - Then provide analysis based on those results

4. **WHEN TO USE TOOLS**: Only use MCP tools when they can provide essential information that you cannot answer without them.

5. **TOOL CALL FORMAT**: Always use the exact format: mcpExecuteTool("serverId", "toolName", {parameter: "value"})

**Example Workflow:**
User: "What files are in my current directory?"
Assistant: I'll check the files in your current directory.
mcpExecuteTool("filesystem", "list_directory", {"path": "."})

[User will then provide tool results in next message]

**Remember: ONE tool call per response, then WAIT for results!**

`;
    }

    injectPromptEnhancement() {
      console.log('[Perplexity MCP] Starting prompt enhancement injection...');

      // Find Perplexity's input elements
      this.findAndEnhancePromptInputs();

      // Set up observers to watch for new input elements
      this.observeForNewInputs();

      // Retry injection periodically in case page structure changes
      setTimeout(() => {
        if (!this.promptInput || !document.contains(this.promptInput)) {
          console.log('[Perplexity MCP] Prompt input lost, re-injecting...');
          this.findAndEnhancePromptInputs();
        }
      }, 5000);
    }

    findAndEnhancePromptInputs() {
      console.log('[Perplexity MCP] Searching for input elements...');

      // Updated selectors for Perplexity's input elements with more specific targeting
      const inputSelectors = [
        // Primary Perplexity selectors (most specific first)
        'textarea#ask-input',
        // 'textarea[placeholder*="Ask anything"]',
        // 'textarea[placeholder*="ask anything"]',
        // // Secondary Perplexity selectors
        // 'textarea[data-testid*="search"]',
        // 'textarea[data-testid*="input"]',
        // 'div[role="textbox"]',
        // // Fallback general selectors
        // 'textarea[placeholder*="Ask"]',
        // 'textarea[placeholder*="ask"]',
        // 'input[placeholder*="Ask"]',
        // 'input[placeholder*="ask"]',
        // 'div[contenteditable="true"]',
        // 'textarea[class*="input"]',
        // 'textarea[class*="prompt"]',
        // // Additional fallback patterns
        // 'textarea[aria-label*="search"]',
        // 'textarea[aria-label*="ask"]',
        // 'input[aria-label*="search"]',
        // 'input[aria-label*="ask"]'
      ];

      let foundInput = null;
      let matchedSelector = null;

      // // Log all available inputs on the page for debugging
      // const allInputs = document.querySelectorAll('textarea, input[type="text"], div[contenteditable="true"]');
      // console.log(`[Perplexity MCP] Found ${allInputs.length} total input elements on page`);

      // allInputs.forEach((input, index) => {
      //   const info = {
      //     tagName: input.tagName,
      //     id: input.id,
      //     className: input.className,
      //     placeholder: input.placeholder,
      //     'aria-label': input.getAttribute('aria-label'),
      //     'data-testid': input.getAttribute('data-testid'),
      //     contentEditable: input.contentEditable
      //   };
      //   console.log(`[Perplexity MCP] Input ${index}:`, info);
      // });

      for (const selector of inputSelectors) {
        const element = document.querySelector(selector);
        if (element) {
          console.log(`[Perplexity MCP] ✅ Found input element with selector: ${selector}`);
          foundInput = element;
          matchedSelector = selector;
          break;
        } else {
          console.log(`[Perplexity MCP] ❌ No match for selector: ${selector}`);
        }
      }

      if (foundInput) {
        console.log(`[Perplexity MCP] Enhancing input found with: ${matchedSelector}`);
        this.enhancePromptInput(foundInput);
      } else {
        console.log('[Perplexity MCP] ⚠️ No input element found, will retry in 2 seconds');
        // Retry after a delay since Perplexity loads dynamically
        setTimeout(() => this.findAndEnhancePromptInputs(), 2000);
      }
    }

    enhancePromptInput(inputElement) {
      console.log(`[Perplexity MCP] Enhancing prompt input:`, {
        tagName: inputElement.tagName,
        id: inputElement.id,
        className: inputElement.className,
        placeholder: inputElement.placeholder,
        alreadyEnhanced: !!inputElement.mcpEnhanced
      });

      // Store reference to the input element
      this.promptInput = inputElement;

      // Add MCP enhancement indicator
      this.addMcpPromptIndicator(inputElement);

      // Intercept form submissions
      this.interceptPromptSubmission(inputElement);
    }

    addMcpPromptIndicator(inputElement) {
      // Don't add multiple indicators
      if (inputElement.parentElement && inputElement.parentElement.querySelector('.mcp-prompt-indicator')) {
        console.log('[Perplexity MCP] Indicator already exists, skipping');
        return;
      }

      // Create MCP indicator
      const indicator = document.createElement('div');
      indicator.className = 'mcp-prompt-indicator';
      indicator.innerHTML = `
      <span class="mcp-indicator-icon">🔧</span>
      <span class="mcp-indicator-text">${this.getConnectedToolsCount()} MCP tools available</span>
    `;

      // Insert indicator near the input
      const parent = inputElement.closest('form') || inputElement.parentElement;
      if (parent) {
        parent.style.position = 'relative';
        parent.appendChild(indicator);
        console.log('[Perplexity MCP] ✅ Added MCP indicator');
      } else {
        console.log('[Perplexity MCP] ⚠️ Could not find parent for indicator');
      }
    }

    getConnectedToolsCount() {
      let totalTools = 0;
      for (const server of this.mcpServers) {
        if (server.status === 'running' && server.tools) {
          totalTools += server.tools.length;
        }
      }
      return totalTools;
    }

    interceptPromptSubmission(inputElement) {
      // Mark this input as enhanced to avoid duplicate processing
      if (inputElement.mcpEnhanced) {
        console.log('[Perplexity MCP] Input already enhanced, skipping');
        return;
      }
      inputElement.mcpEnhanced = true;

      console.log('[Perplexity MCP] Setting up submission interception...');

      // Find the form or submission mechanism
      const form = inputElement.closest('form');

      // Enhanced submit button detection with Perplexity-specific selectors
      const submitSelectors = [
        'button[aria-label="Submit"]',  // Perplexity's main submit button
        'button[type="submit"]',
        'button[class*="submit"]',
        'button[class*="send"]',
        'button[class*="search"]',
        'button[class*="ask"]',
        'button[class*="query"]',
        'svg[class*="send"]',
        'svg[class*="submit"]',
        'svg[class*="arrow"]',
        // Additional Perplexity patterns
        'button[data-testid*="submit"]',
        'button[data-testid*="send"]',
        'button[data-testid*="search"]',
        // Generic patterns for submit functionality
        'button:has(svg)',
        '[role="button"]',
        'div[class*="submit"]',
        'div[class*="send"]'
      ];

      let submitButton = null;
      for (const selector of submitSelectors) {
        const button = form ? form.querySelector(selector) : document.querySelector(selector);
        if (button) {
          submitButton = button;
          console.log(`[Perplexity MCP] Found submit button with selector: ${selector}`);
          break;
        }
      }

      // If no button found, search more broadly near the input
      if (!submitButton) {
        const inputContainer = inputElement.closest('div[class*="input"]') ||
          inputElement.closest('div[class*="search"]') ||
          inputElement.closest('div[class*="ask"]') ||
          inputElement.closest('div[class*="query"]') ||
          inputElement.parentElement;
        if (inputContainer) {
          for (const selector of submitSelectors) {
            const button = inputContainer.querySelector(selector);
            if (button) {
              submitButton = button;
              console.log(`[Perplexity MCP] Found submit button in container with selector: ${selector}`);
              break;
            }
          }
        }
      }

      // Search even more broadly if still not found
      if (!submitButton) {
        console.log('[Perplexity MCP] Searching more broadly for submit button...');
        for (const selector of submitSelectors) {
          const buttons = document.querySelectorAll(selector);
          for (const button of buttons) {
            // Check if button is visible and likely a submit button
            const rect = button.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              submitButton = button;
              console.log(`[Perplexity MCP] Found visible submit button with selector: ${selector}`);
              break;
            }
          }
          if (submitButton) break;
        }
      }

      console.log(`[Perplexity MCP] Found submission elements:`, {
        hasForm: !!form,
        hasSubmitButton: !!submitButton,
        formId: form?.id,
        buttonClass: submitButton?.className,
        buttonAriaLabel: submitButton?.getAttribute('aria-label'),
        buttonDataTestId: submitButton?.getAttribute('data-testid')
      });

      // Enhanced submission handling with better timing
      const handleSubmission = (e) => {
        console.log(`[Perplexity MCP] Submission triggered by: ${e.type}`);

        // For immediate triggers (click, submit), enhance right away
        if (e.type === 'click' || e.type === 'submit') {
          this.handlePromptSubmission(e, inputElement);
        } else {
          // For other events, add a small delay
          setTimeout(() => {
            this.handlePromptSubmission(e, inputElement);
          }, 10);
        }
      };

      // Add multiple event listeners for better coverage
      if (form) {
        form.addEventListener('submit', (e) => {
          console.log('[Perplexity MCP] Form submit event');
          // Prevent default temporarily to inject prompt
          e.preventDefault();
          this.handlePromptSubmission(e, inputElement);
          // Re-submit after injection
          setTimeout(() => {
            const newEvent = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(newEvent);
          }, 50);
        });
        console.log('[Perplexity MCP] ✅ Added form submit listener');
      }

      if (submitButton) {
        // Add both click and mousedown for better coverage
        submitButton.addEventListener('click', (e) => {
          console.log('[Perplexity MCP] Submit button clicked');
          this.handlePromptSubmission(e, inputElement);
        });

        submitButton.addEventListener('mousedown', (e) => {
          console.log('[Perplexity MCP] Submit button mousedown');
          // Trigger enhancement slightly before click
          setTimeout(() => this.handlePromptSubmission(e, inputElement), 5);
        });

        console.log('[Perplexity MCP] ✅ Added button click and mousedown listeners');
      }

      // Listen for Enter key with immediate handling
      inputElement.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
          console.log('[Perplexity MCP] Enter key detected - enhancing immediately');
          this.handlePromptSubmission(e, inputElement);
        }
      });

      // Also add keyup handler as backup
      inputElement.addEventListener('keyup', (e) => {
        if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
          console.log('[Perplexity MCP] Enter keyup detected - backup enhancement');
          setTimeout(() => this.handlePromptSubmission(e, inputElement), 5);
        }
      });

      // // Watch for focus loss (when user clicks submit button)
      // inputElement.addEventListener('blur', (e) => {
      //   // Only enhance if blur is caused by submission activity
      //   if (this.lastInputValue && this.lastInputValue.trim()) {
      //     console.log('[Perplexity MCP] Input blur detected - potential submission');
      //     setTimeout(() => {
      //       // Check if input still has the same value (indicating submission)
      //       const currentValue = inputElement.value || inputElement.textContent;
      //       if (currentValue === this.lastInputValue) {
      //         this.handlePromptSubmission(e, inputElement);
      //       }
      //     }, 10);
      //   }
      // });

      // Watch for input changes to detect programmatic submissions
      inputElement.addEventListener('input', () => {
        // Store the current value for potential enhancement
        this.lastInputValue = inputElement.value || inputElement.textContent;
      });

      console.log('[Perplexity MCP] ✅ Submission interception setup complete');
    }

    handlePromptSubmission(event, inputElement) {
      const userPrompt = inputElement.value || inputElement.textContent || '';

      console.log(`[Perplexity MCP] Handling prompt submission:`, {
        promptLength: userPrompt.length,
        hasTools: this.mcpServers.length > 0,
        isConnected: this.isConnected,
        alreadyEnhanced: userPrompt.includes('Available MCP Tools')
      });

      // Only enhance if we have a meaningful prompt and haven't already enhanced it
      if (userPrompt.trim() && !userPrompt.includes('Available MCP Tools') && this.shouldEnhancePrompt(userPrompt)) {
        console.log('[Perplexity MCP] ✅ Enhancing prompt with MCP system information');
        this.injectMcpSystemPrompt(inputElement, userPrompt);
      } else {
        if (!userPrompt.trim()) {
          console.log('[Perplexity MCP] ⚠️ Empty prompt, skipping enhancement');
        } else if (userPrompt.includes('Available MCP Tools')) {
          console.log('[Perplexity MCP] ⚠️ Prompt already enhanced, skipping');
        } else if (!this.shouldEnhancePrompt(userPrompt)) {
          console.log('[Perplexity MCP] ⚠️ Prompt does not match MCP keywords, skipping enhancement');
        }
      }
    }

    shouldEnhancePrompt(prompt) {
      // Check if MCP is globally enabled
      if (!this.settings.mcpEnabled) {
        console.log('[Perplexity MCP] MCP globally disabled, skipping enhancement');
        return false;
      }

      // Check if we have any enabled servers
      const hasEnabledServers = this.mcpServers.some(server =>
        this.settings.serverSettings.get(server.id)?.enabled !== false
      );

      if (!hasEnabledServers) {
        console.log('[Perplexity MCP] No enabled servers, skipping enhancement');
        return false;
      }

      // Don't enhance if already enhanced
      if (prompt.includes('Available MCP Tools') || prompt.includes('mcpExecuteTool')) {
        return false;
      }

      // If always inject is enabled, always enhance (skip keyword analysis)
      if (this.settings.alwaysInject) {
        console.log('[Perplexity MCP] Always inject enabled, enhancing prompt');
        return true;
      }

      // Keywords that suggest MCP tools might be useful
      const mcpKeywords = [
        'file', 'read', 'write', 'directory', 'folder', 'path',
        'api', 'data', 'search', 'database', 'github', 'git',
        'code', 'repository', 'analysis', 'recent', 'latest',
        'current', 'real-time', 'live', 'browse', 'fetch',
        'execute', 'run', 'script', 'command', 'terminal',
        'project', 'workspace', 'development', 'debug',
        'list', 'show', 'find', 'open', 'create', 'delete'
      ];

      const lowerPrompt = prompt.toLowerCase();
      const matchedKeywords = mcpKeywords.filter(keyword => lowerPrompt.includes(keyword));

      console.log(`[Perplexity MCP] Keyword analysis:`, {
        prompt: prompt.substring(0, 100) + '...',
        matchedKeywords,
        shouldEnhance: matchedKeywords.length > 0,
        alwaysInject: this.settings.alwaysInject,
        mcpEnabled: this.settings.mcpEnabled
      });

      return matchedKeywords.length > 0;
    }

    injectMcpSystemPrompt(inputElement, userPrompt) {
      console.log('[Perplexity MCP] Starting system prompt injection...');

      const systemPrompt = this.generateMcpSystemPrompt();
      if (!systemPrompt) {
        console.log('[Perplexity MCP] ❌ No system prompt generated - no tools available');
        return;
      }

      try {
        // Create enhanced prompt with clear separation
        const enhancedPrompt = `${systemPrompt}

## User Query
${userPrompt}`;

        console.log(`[Perplexity MCP] Generated enhanced prompt:`, {
          originalLength: userPrompt.length,
          enhancedLength: enhancedPrompt.length,
          elementType: inputElement.tagName,
          isContentEditable: inputElement.contentEditable
        });

        // More aggressive React state update approach
        if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
          const oldValue = inputElement.value;

          // Method 1: Direct value setting with descriptor manipulation
          const descriptor = Object.getOwnPropertyDescriptor(HTMLTextAreaElement.prototype, 'value') ||
            Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
          if (descriptor && descriptor.set) {
            descriptor.set.call(inputElement, enhancedPrompt);
          } else {
            inputElement.value = enhancedPrompt;
          }

          // Method 2: Trigger comprehensive React events
          const events = [
            new Event('input', { bubbles: true }),
            new Event('change', { bubbles: true }),
            new KeyboardEvent('keyup', { bubbles: true }),
            new KeyboardEvent('keydown', { bubbles: true }),
            new FocusEvent('focus', { bubbles: true }),
            new Event('compositionend', { bubbles: true })
          ];

          events.forEach(event => {
            try {
              inputElement.dispatchEvent(event);
            } catch (e) {
              console.log(`[Perplexity MCP] Event ${event.type} failed:`, e);
            }
          });

          // Method 3: Force React re-render by simulating user input
          const reactInternalKey = Object.keys(inputElement).find(key => key.startsWith('__reactInternalInstance') || key.startsWith('__reactFiber'));
          if (reactInternalKey) {
            console.log('[Perplexity MCP] Found React internal key, attempting state update');
            // Trigger React's internal change detection
            inputElement[reactInternalKey].memoizedProps.onChange?.({
              target: { value: enhancedPrompt },
              type: 'change'
            });
          }

          console.log(`[Perplexity MCP] Updated textarea/input value:`, {
            oldLength: oldValue.length,
            newLength: inputElement.value.length,
            valueSet: inputElement.value === enhancedPrompt
          });

        } else if (inputElement.contentEditable === 'true') {
          const oldContent = inputElement.textContent;
          inputElement.textContent = enhancedPrompt;

          // Comprehensive events for contentEditable
          const events = [
            new Event('input', { bubbles: true }),
            new Event('textInput', { bubbles: true }),
            new Event('blur', { bubbles: true }),
            new Event('focus', { bubbles: true }),
            new Event('compositionend', { bubbles: true })
          ];

          events.forEach(event => {
            try {
              inputElement.dispatchEvent(event);
            } catch (e) {
              console.log(`[Perplexity MCP] ContentEditable event ${event.type} failed:`, e);
            }
          });

          console.log(`[Perplexity MCP] Updated contentEditable:`, {
            oldLength: oldContent.length,
            newLength: inputElement.textContent.length,
            contentSet: inputElement.textContent === enhancedPrompt
          });
        }

        // Force cursor to end for better UX
        if (inputElement.setSelectionRange) {
          const length = enhancedPrompt.length;
          inputElement.setSelectionRange(length, length);
        }

        // Also try to trigger focus to ensure the input is active
        inputElement.focus();

        console.log('[Perplexity MCP] ✅ Successfully injected system prompt');

        // Log the final state for debugging with multiple checks
        setTimeout(() => {
          const finalValue = inputElement.value || inputElement.textContent;
          console.log(`[Perplexity MCP] Final input state after 100ms:`, {
            length: finalValue.length,
            containsSystemPrompt: finalValue.includes('Available MCP Tools'),
            preview: finalValue.substring(0, 200) + '...'
          });
        }, 100);

        // Additional verification after 500ms
        setTimeout(() => {
          const finalValue = inputElement.value || inputElement.textContent;
          if (!finalValue.includes('Available MCP Tools')) {
            console.log('[Perplexity MCP] ⚠️ Prompt injection may have been overridden by React');
            // Try one more time with direct manipulation
            if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
              inputElement.value = enhancedPrompt;
              inputElement.dispatchEvent(new Event('input', { bubbles: true }));
            }
          }
        }, 500);

      } catch (error) {
        console.error('[Perplexity MCP] ❌ Failed to inject system prompt:', error);
      }
    }

    observeForNewInputs() {
      // Watch for dynamically created input elements
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if new input elements were added
                const inputs = node.querySelectorAll ?
                  node.querySelectorAll('textarea, input[type="text"], div[contenteditable="true"]') : [];

                for (const input of inputs) {
                  if (input.placeholder && input.placeholder.toLowerCase().includes('ask')) {
                    this.enhancePromptInput(input);
                    break;
                  }
                }
              }
            }
          }
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // Add global function for Perplexity to call MCP tools
    setupGlobalMcpInterface() {
      window.mcpExecuteTool = async (serverId, toolName, parameters) => {
        try {
          console.log(`[Perplexity MCP] Executing tool: ${toolName} on server: ${serverId}`);
          const result = await this.executeToolInContext(serverId, toolName, parameters);
          console.log(`[Perplexity MCP] Tool execution result:`, result);
          return result;
        } catch (error) {
          console.error(`[Perplexity MCP] Tool execution failed:`, error);
          throw error;
        }
      };

      // Also expose the client for debugging
      window.mcpClient = this;
    }

    // Fetch tools from all connected servers
    async fetchServerTools() {
      for (const server of this.mcpServers) {
        if (server.status === 'running') {
          try {
            const result = await this.callMcpTool(server.id, 'tools/list', {});
            if (result && result.tools) {
              server.tools = result.tools;
              console.log(`[Perplexity MCP] Fetched ${result.tools.length} tools from ${server.name}`);
            }
          } catch (error) {
            console.warn(`[Perplexity MCP] Failed to fetch tools from ${server.name}:`, error);
          }
        }
      }

      // Update prompt indicator if it exists
      this.updatePromptIndicator();
    }

    updatePromptIndicator() {
      const indicator = document.querySelector('.mcp-prompt-indicator .mcp-indicator-text');
      if (indicator) {
        indicator.textContent = `${this.getConnectedToolsCount()} MCP tools available`;
      }
    }

    // Add debug panel for testing
    addDebugPanel() {
      if (document.getElementById('mcp-debug-panel')) {
        return; // Already exists
      }

      const debugPanel = document.createElement('div');
      debugPanel.id = 'mcp-debug-panel';
      debugPanel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
        font-family: monospace;
        font-size: 12px;
        max-width: 300px;
        border: 2px solid #4CAF50;
      `;

      debugPanel.innerHTML = `
        <div style="margin-bottom: 10px;">
          <strong>🔧 MCP Debug Panel</strong>
          <button id="mcp-debug-close" style="float: right; background: red; color: white; border: none; border-radius: 3px; cursor: pointer;">✖</button>
        </div>
        <div id="mcp-debug-status">Loading...</div>
        <div style="margin-top: 10px;">
          <button id="mcp-debug-scan" style="background: #2196F3; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Scan Inputs</button>
          <button id="mcp-debug-inject" style="background: #FF9800; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Test Inject</button>
        </div>
        <div style="margin-top: 10px; border-top: 1px solid rgba(255,255,255,0.3); padding-top: 10px;">
          <div style="margin-bottom: 5px;">
            <label style="display: flex; align-items: center; font-size: 11px;">
              <input type="checkbox" id="mcp-debug-enabled" style="margin-right: 5px;">
              MCP Bridge Enabled
            </label>
          </div>
          <div style="margin-bottom: 5px;">
            <label style="display: flex; align-items: center; font-size: 11px;">
              <input type="checkbox" id="mcp-debug-always-inject" style="margin-right: 5px;">
              Always Inject Prompts
            </label>
          </div>
          <div style="margin-bottom: 5px;">
            <label style="display: flex; align-items: center; font-size: 11px;">
              <input type="checkbox" id="mcp-debug-monitor" style="margin-right: 5px;">
              Monitor Responses
            </label>
          </div>
          <div id="mcp-debug-servers" style="font-size: 10px; margin-top: 5px;">
            <div style="font-weight: bold; margin-bottom: 3px;">Server Controls:</div>
          </div>
        </div>
        <div id="mcp-debug-log" style="margin-top: 10px; max-height: 200px; overflow-y: auto; font-size: 10px; background: rgba(255,255,255,0.1); padding: 5px; border-radius: 3px;"></div>
      `;

      document.body.appendChild(debugPanel);

      // Add event listeners
      document.getElementById('mcp-debug-close').onclick = () => debugPanel.remove();
      document.getElementById('mcp-debug-scan').onclick = () => this.debugScanInputs();
      document.getElementById('mcp-debug-inject').onclick = () => this.debugTestInject();

      // Setup settings controls
      this.setupDebugSettingsControls();

      this.updateDebugStatus();

      // Start response monitoring by default
      this.startResponseMonitoring();
    }

    // Add clean MCP status indicators (no debug panel)
    addStatusIndicators() {
      // Add clean MCP Tools status to top-right area (includes the tools count badge)
      this.addMcpToolsStatus();

      // No longer need separate badge since it's included in the status above
      // this.addMcpToolsBadge();
    }

    addMainStatusIndicator() {
      // Try to find the best location in Perplexity's header
      const headerSelectors = [
        'header',
        'nav[role="navigation"]',
        '.header',
        '[data-testid="header"]',
        'div[class*="header"]'
      ];

      let headerElement = null;
      for (const selector of headerSelectors) {
        headerElement = document.querySelector(selector);
        if (headerElement) break;
      }

      // Fallback to creating our own header indicator
      if (!headerElement) {
        headerElement = document.body;
      }

      const statusIndicator = document.createElement('div');
      statusIndicator.id = 'mcp-header-status';
      statusIndicator.className = 'mcp-header-status';
      statusIndicator.innerHTML = `
        <div class="mcp-status-icon" title="MCP Tools Status">
          <span class="status-dot" id="mcp-status-dot"></span>
          <span class="status-text">🔧</span>
        </div>
        <div class="mcp-status-tooltip" id="mcp-status-tooltip">
          <div class="tooltip-header">MCP Bridge Status</div>
          <div class="tooltip-content" id="mcp-tooltip-content">
            <div class="status-line">
              <span class="label">Connection:</span>
              <span class="value" id="tooltip-connection">Connecting...</span>
            </div>
            <div class="status-line">
              <span class="label">Servers:</span>
              <span class="value" id="tooltip-servers">0</span>
            </div>
            <div class="status-line">
              <span class="label">Tools:</span>
              <span class="value" id="tooltip-tools">0</span>
            </div>
            <div class="server-list" id="tooltip-server-list"></div>
          </div>
        </div>
      `;

      headerElement.appendChild(statusIndicator);
      console.log('[Perplexity MCP] ✅ Added header status indicator');
    }

    addFloatingStatusPanel() {
      const floatingPanel = document.createElement('div');
      floatingPanel.id = 'mcp-floating-status';
      floatingPanel.className = 'mcp-floating-status';
      floatingPanel.innerHTML = `
        <div class="floating-status-content">
          <div class="status-header">
            <span class="status-title">MCP</span>
            <span class="status-count" id="floating-tool-count">0</span>
          </div>
          <div class="quick-actions">
            <button class="quick-action-btn" id="floating-toggle-panel" title="Toggle Tools Panel">
              <span>📋</span>
            </button>
            <button class="quick-action-btn" id="floating-toggle-debug" title="Toggle Debug Panel">
              <span>🔧</span>
            </button>
          </div>
        </div>
        <div class="floating-tooltip" id="floating-tooltip">
          <div class="tooltip-content">
            <div class="status-overview" id="floating-overview"></div>
            <div class="server-overview" id="floating-servers"></div>
          </div>
        </div>
      `;

      document.body.appendChild(floatingPanel);

      // Add event listeners
      document.getElementById('floating-toggle-panel').onclick = () => this.toggleMcpPanel();
      document.getElementById('floating-toggle-debug').onclick = () => this.toggleDebugPanel();

      console.log('[Perplexity MCP] ✅ Added floating status panel');
    }

    toggleDebugPanel() {
      const debugPanel = document.getElementById('mcp-debug-panel');
      if (debugPanel) {
        debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
      } else {
        this.addDebugPanel();
      }
    }

    addMcpToolsStatus() {
      // Add clean "MCP Tools" status to top-right area
      if (document.getElementById('mcp-tools-status')) {
        return; // Already exists
      }

      const statusElement = document.createElement('div');
      statusElement.id = 'mcp-tools-status';
      statusElement.className = 'mcp-tools-status';
      statusElement.innerHTML = `
        <span class="mcp-label" style="padding-bottom: 9px;">Perplexity Web MCP Bridge</span>
        <div class="mcp-tools-header">
          <span class="status-indicator disconnected" id="mcp-connection-dot"></span>
          <span class="status-text disconnected" id="mcp-connection-text">Disconnected</span>
          <span class="tools-count-badge" id="mcp-tools-count-badge">0 MCP tools available</span>
        </div>
        <div class="mcp-tools-tooltip" id="mcp-tools-tooltip">
          <div class="tooltip-header">Available MCP Tools</div>
          <div class="tooltip-tools-list" id="mcp-tooltip-tools-list">
            <div class="loading">Loading tools...</div>
          </div>
        </div>
      `;

      // Place it in the body (will be positioned via CSS)
      document.body.appendChild(statusElement);

      console.log('[Perplexity MCP] ✅ Added unified MCP Tools status');
    }

    addMcpToolsBadge() {
      // Add the "X MCP tools available" badge near search area
      if (document.getElementById('mcp-tools-badge')) {
        return; // Already exists
      }

      const badgeElement = document.createElement('div');
      badgeElement.id = 'mcp-tools-badge';
      badgeElement.className = 'mcp-tools-badge';
      badgeElement.innerHTML = `
        <span class="badge-text" id="mcp-badge-text">11 MCP tools available</span>
        <div class="badge-tooltip" id="mcp-badge-tooltip">
          <div class="tooltip-header">Available MCP Tools</div>
          <div class="tooltip-tools-list" id="tooltip-tools-list">
            <div class="loading">Loading tools...</div>
          </div>
        </div>
      `;

      // Place it in the body (will be positioned via CSS)
      document.body.appendChild(badgeElement);

      console.log('[Perplexity MCP] ✅ Added MCP Tools badge');
    }

    updateStatusIndicators() {
      this.updateMcpToolsStatus();
      // No longer need separate badge update since it's included in the status above
      // this.updateMcpToolsBadge();
    }

    updateMcpToolsStatus() {
      const connectionDot = document.getElementById('mcp-connection-dot');
      const connectionText = document.getElementById('mcp-connection-text');
      const toolsCountBadge = document.getElementById('mcp-tools-count-badge');
      const toolsList = document.getElementById('mcp-tooltip-tools-list');

      // Update connection status using preserved class names with proper state handling
      let state, text;
      if (this.isConnecting) {
        state = 'connecting';
        text = 'Connecting...';
      } else if (this.isConnected) {
        state = 'connected';
        text = 'Connected';
      } else {
        state = 'disconnected';
        text = 'Disconnected';
      }

      if (connectionDot) {
        connectionDot.className = `status-indicator ${state}`;
      }

      if (connectionText) {
        connectionText.textContent = text;
        connectionText.className = `status-text ${state}`;
      }

      // Update tools count badge
      const toolCount = this.getConnectedToolsCount();
      if (toolsCountBadge) {
        toolsCountBadge.textContent = `${toolCount} MCP tools available`;
      }

      // Update tooltip with available tools
      if (toolsList) {
        if (this.mcpServers.length === 0) {
          toolsList.innerHTML = '<div class="no-tools">No servers connected</div>';
        } else {
          const allTools = [];
          this.mcpServers.forEach(server => {
            if (server.tools && server.status === 'running') {
              server.tools.forEach(tool => {
                allTools.push({
                  serverName: server.name || server.id,
                  toolName: tool.name,
                  description: tool.description || 'No description'
                });
              });
            }
          });

          if (allTools.length === 0) {
            toolsList.innerHTML = '<div class="no-tools">No tools available</div>';
          } else {
            // Limit to first 10 tools and add ellipsis if more
            const displayTools = allTools.slice(0, 10);
            const hasMore = allTools.length > 10;

            toolsList.innerHTML = displayTools.map(tool => {
              const shortDesc = tool.description.length > 50
                ? tool.description.substring(0, 50) + '...'
                : tool.description;
              return `
                <div class="tool-item">
                  <div class="tool-name">${tool.toolName}</div>
                  <div class="tool-server">${tool.serverName}</div>
                  <div class="tool-description">${shortDesc}</div>
                </div>
              `;
            }).join('') + (hasMore ? `<div class="more-tools">... and ${allTools.length - 10} more tools</div>` : '');
          }
        }
      }
    }

    updateMcpToolsBadge() {
      const badgeText = document.getElementById('mcp-badge-text');
      const toolsList = document.getElementById('tooltip-tools-list');

      const toolCount = this.getConnectedToolsCount();

      if (badgeText) {
        badgeText.textContent = `${toolCount} MCP tools available`;
      }

      if (toolsList) {
        if (this.mcpServers.length === 0) {
          toolsList.innerHTML = '<div class="no-tools">No servers connected</div>';
        } else {
          const allTools = [];
          this.mcpServers.forEach(server => {
            if (server.tools && server.status === 'running') {
              server.tools.forEach(tool => {
                allTools.push({
                  serverName: server.name || server.id,
                  toolName: tool.name,
                  description: tool.description || 'No description'
                });
              });
            }
          });

          if (allTools.length === 0) {
            toolsList.innerHTML = '<div class="no-tools">No tools available</div>';
          } else {
            toolsList.innerHTML = allTools.map(tool => {
              const shortDesc = tool.description.length > 50
                ? tool.description.substring(0, 50) + '...'
                : tool.description;
              return `
                <div class="tool-item">
                  <div class="tool-name">${tool.toolName}</div>
                  <div class="tool-server">${tool.serverName}</div>
                  <div class="tool-description">${shortDesc}</div>
                </div>
              `;
            }).join('');
          }
        }
      }
    }

    updateHeaderStatus() {
      const statusDot = document.getElementById('mcp-status-dot');
      const tooltipConnection = document.getElementById('tooltip-connection');
      const tooltipServers = document.getElementById('tooltip-servers');
      const tooltipTools = document.getElementById('tooltip-tools');
      const serverList = document.getElementById('tooltip-server-list');

      if (!statusDot) return;

      // Update status dot
      statusDot.className = `status-dot ${this.isConnected ? 'connected' : 'disconnected'}`;

      // Update tooltip content
      if (tooltipConnection) {
        tooltipConnection.textContent = this.isConnected ? 'Connected' : 'Disconnected';
        tooltipConnection.className = `value ${this.isConnected ? 'connected' : 'disconnected'}`;
      }

      if (tooltipServers) {
        tooltipServers.textContent = this.mcpServers.length;
      }

      if (tooltipTools) {
        tooltipTools.textContent = this.getConnectedToolsCount();
      }

      // Update server list
      if (serverList) {
        serverList.innerHTML = this.mcpServers.map(server => {
          const toolCount = server.tools ? server.tools.length : 0;
          const isEnabled = this.settings.serverSettings.get(server.id)?.enabled !== false;
          return `
            <div class="server-item ${isEnabled ? 'enabled' : 'disabled'}">
              <span class="server-name">${server.name || server.id}</span>
              <span class="server-tools">${toolCount} tools</span>
              <span class="server-status ${isEnabled ? 'active' : 'inactive'}">
                ${isEnabled ? '●' : '○'}
              </span>
            </div>
          `;
        }).join('');
      }
    }

    updateFloatingStatus() {
      const toolCount = document.getElementById('floating-tool-count');
      const overview = document.getElementById('floating-overview');
      const servers = document.getElementById('floating-servers');

      if (toolCount) {
        toolCount.textContent = this.getConnectedToolsCount();
      }

      if (overview) {
        overview.innerHTML = `
          <div class="overview-item">
            <span class="overview-label">Status:</span>
            <span class="overview-value ${this.isConnected ? 'connected' : 'disconnected'}">
              ${this.isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <div class="overview-item">
            <span class="overview-label">Total Tools:</span>
            <span class="overview-value">${this.getConnectedToolsCount()}</span>
          </div>
        `;
      }

      if (servers) {
        servers.innerHTML = `
          <div class="servers-header">Servers (${this.mcpServers.length})</div>
          ${this.mcpServers.map(server => {
          const toolCount = server.tools ? server.tools.length : 0;
          const isEnabled = this.settings.serverSettings.get(server.id)?.enabled !== false;
          return `
              <div class="server-summary ${isEnabled ? 'enabled' : 'disabled'}">
                <span class="server-name">${server.name || server.id}</span>
                <span class="server-info">${toolCount} tools</span>
              </div>
            `;
        }).join('')}
        `;
      }
    }

    updateDebugStatus() {
      const statusEl = document.getElementById('mcp-debug-status');
      if (!statusEl) return;

      statusEl.innerHTML = `
        Connected: ${this.isConnected ? '✅' : '❌'}<br>
        Servers: ${this.mcpServers.length}<br>
        Tools: ${this.getConnectedToolsCount()}<br>
        Input: ${this.promptInput ? '✅' : '❌'}
      `;
    }

    setupDebugSettingsControls() {
      // Set initial checkbox states
      const enabledCheckbox = document.getElementById('mcp-debug-enabled');
      const alwaysInjectCheckbox = document.getElementById('mcp-debug-always-inject');
      const monitorCheckbox = document.getElementById('mcp-debug-monitor');

      if (enabledCheckbox) {
        enabledCheckbox.checked = this.settings.mcpEnabled;
        enabledCheckbox.onchange = (e) => {
          this.settings.mcpEnabled = e.target.checked;
          console.log(`[Perplexity MCP] MCP Bridge ${e.target.checked ? 'enabled' : 'disabled'}`);
          this.logToDebugPanel(`MCP Bridge ${e.target.checked ? 'enabled' : 'disabled'}`);
        };
      }

      if (alwaysInjectCheckbox) {
        alwaysInjectCheckbox.checked = this.settings.alwaysInject;
        alwaysInjectCheckbox.onchange = (e) => {
          this.settings.alwaysInject = e.target.checked;
          console.log(`[Perplexity MCP] Always inject ${e.target.checked ? 'enabled' : 'disabled'}`);
          this.logToDebugPanel(`Always inject ${e.target.checked ? 'enabled' : 'disabled'}`);
        };
      }

      if (monitorCheckbox) {
        monitorCheckbox.checked = !!this.responseObserver;
        monitorCheckbox.onchange = (e) => {
          if (e.target.checked) {
            this.startResponseMonitoring();
            this.logToDebugPanel('Response monitoring enabled');
          } else {
            this.stopResponseMonitoring();
            this.logToDebugPanel('Response monitoring disabled');
          }
        };
      }

      // Update server controls
      this.updateServerControls();
    }

    updateServerControls() {
      const serversContainer = document.getElementById('mcp-debug-servers');
      if (!serversContainer) return;

      const serverControlsHtml = this.mcpServers.map(server => {
        const isEnabled = this.settings.serverSettings.get(server.id)?.enabled !== false;
        return `
          <div style="margin-bottom: 2px;">
            <label style="display: flex; align-items: center; font-size: 10px;">
              <input type="checkbox" ${isEnabled ? 'checked' : ''} 
                     onchange="window.mcpClient.toggleServer('${server.id}', this.checked)"
                     style="margin-right: 3px;">
              ${server.name || server.id}
            </label>
          </div>
        `;
      }).join('');

      serversContainer.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 3px;">Server Controls:</div>
        ${serverControlsHtml || '<div style="color: #999;">No servers connected</div>'}
      `;
    }

    toggleServer(serverId, enabled) {
      if (!this.settings.serverSettings.has(serverId)) {
        this.settings.serverSettings.set(serverId, {});
      }
      this.settings.serverSettings.get(serverId).enabled = enabled;

      console.log(`[Perplexity MCP] Server ${serverId} ${enabled ? 'enabled' : 'disabled'}`);
      this.logToDebugPanel(`Server ${serverId} ${enabled ? 'enabled' : 'disabled'}`);
    }

    logToDebugPanel(message) {
      const logEl = document.getElementById('mcp-debug-log');
      if (logEl) {
        const timestamp = new Date().toLocaleTimeString();
        logEl.innerHTML += `[${timestamp}] ${message}<br>`;
        logEl.scrollTop = logEl.scrollHeight;

        // Keep log size manageable
        const lines = logEl.innerHTML.split('<br>');
        if (lines.length > 50) {
          logEl.innerHTML = lines.slice(-40).join('<br>');
        }
      }
    }

    startResponseMonitoring() {
      if (this.responseObserver) {
        console.log('[Perplexity MCP] Response monitoring already active');
        return;
      }

      console.log('[Perplexity MCP] Starting response monitoring...');

      // Monitor for new response elements
      this.responseObserver = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                this.checkNodeForResponses(node);
              }
            }
          }
        }
      });

      this.responseObserver.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Also check existing responses
      this.checkForNewResponses();
    }

    stopResponseMonitoring() {
      if (this.responseObserver) {
        this.responseObserver.disconnect();
        this.responseObserver = null;
        console.log('[Perplexity MCP] Response monitoring stopped');
      }
    }

    checkNodeForResponses(node) {
      // Look for response containers that might contain MCP tool calls
      const responseSelectors = [
        '[data-testid*="response"]',
        '[class*="response"]',
        '[class*="answer"]',
        '[class*="message"]',
        '.prose',
        'article',
        'div[role="article"]'
      ];

      for (const selector of responseSelectors) {
        const elements = node.querySelectorAll ? node.querySelectorAll(selector) : [];
        for (const element of elements) {
          this.processResponseElement(element);
        }

        // Also check if the node itself matches
        if (node.matches && node.matches(selector)) {
          this.processResponseElement(node);
        }
      }
    }

    checkForNewResponses() {
      const responseSelectors = [
        '[data-testid*="response"]',
        '[class*="response"]',
        '[class*="answer"]',
        '[class*="message"]',
        '.prose',
        'article',
        'div[role="article"]'
      ];

      for (const selector of responseSelectors) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
          this.processResponseElement(element);
        }
      }
    }

    processResponseElement(element) {
      // Skip if already processed
      if (element.dataset.mcpProcessed) return;
      element.dataset.mcpProcessed = 'true';

      const text = element.textContent || '';

      // Look for MCP tool call patterns
      const toolCallPatterns = [
        // mcpExecuteTool function calls
        /mcpExecuteTool\s*\(\s*["']([^"']+)["']\s*,\s*["']([^"']+)["']\s*,?\s*(\{[^}]*\})?\s*\)/g,
        // Code blocks that might be tool calls
        /```(\w+)\s*\n(.*?)```/gs,
        // MCP XML tags
        /<mcp:(\w+)>(.*?)<\/mcp:\1>/gs,
        // MCP bracket format
        /\[MCP:(\w+)\](.*?)\[\/MCP:\1\]/gs
      ];

      for (const pattern of toolCallPatterns) {
        const matches = text.matchAll(pattern);
        for (const match of matches) {
          this.handleDetectedToolCall(match, element, pattern);
        }
      }

      // Also check for structured tool calls in JSON format
      try {
        const jsonPattern = /\{[^}]*"tool"[^}]*\}/g;
        const jsonMatches = text.matchAll(jsonPattern);
        for (const match of jsonMatches) {
          try {
            const toolCall = JSON.parse(match[0]);
            if (toolCall.tool && toolCall.parameters) {
              this.executeDetectedToolCall(toolCall, element);
            }
          } catch (e) {
            // Not valid JSON, ignore
          }
        }
      } catch (e) {
        // Error parsing, continue
      }
    }

    handleDetectedToolCall(match, element, pattern) {
      console.log('[Perplexity MCP] Detected potential tool call:', match);
      this.logToDebugPanel(`Detected tool call: ${match[1] || match[2] || 'unknown'}`);

      // Try to extract tool information and execute
      let toolName, serverId, parameters = {};

      // Check if this is an mcpExecuteTool function call (first pattern)
      if (pattern.source.includes('mcpExecuteTool')) {
        serverId = match[1]; // First parameter is server ID
        toolName = match[2]; // Second parameter is tool name
        if (match[3]) {
          try {
            parameters = JSON.parse(match[3]);
          } catch (e) {
            console.warn('[Perplexity MCP] Failed to parse parameters:', match[3]);
            parameters = {};
          }
        }

        console.log('[Perplexity MCP] Parsed mcpExecuteTool call:', { serverId, toolName, parameters });
        this.executeDetectedToolCall({ tool: toolName, server: serverId, parameters }, element);
      }
      // Handle other patterns
      else if (match[1] && match[2]) {
        // Extract from pattern match
        toolName = match[1];
        try {
          parameters = JSON.parse(match[2]);
        } catch (e) {
          parameters = { query: match[2] };
        }

        // Find appropriate server for this tool
        serverId = this.findServerForTool(toolName);

        if (serverId) {
          this.executeDetectedToolCall({ tool: toolName, server: serverId, parameters }, element);
        } else {
          console.warn('[Perplexity MCP] No server found for tool:', toolName);
          this.logToDebugPanel(`No server found for tool: ${toolName}`);
        }
      }
    }

    findServerForTool(toolName) {
      // Find which server has this tool
      for (const server of this.mcpServers) {
        if (server.tools && server.tools.some(tool => tool.name === toolName)) {
          return server.id;
        }
      }
      return null;
    }

    async executeDetectedToolCall(toolCall, responseElement) {
      try {
        console.log('[Perplexity MCP] Executing detected tool call:', toolCall);
        this.logToDebugPanel(`Executing ${toolCall.tool}...`);

        // Check if MCP bridge is enabled
        if (!this.settings.mcpEnabled) {
          console.log('[Perplexity MCP] MCP bridge is disabled, skipping tool execution');
          this.logToDebugPanel('Tool execution skipped - MCP bridge disabled');
          return;
        }

        // Check if server is enabled
        const serverSettings = this.settings.serverSettings.get(toolCall.server);
        if (serverSettings && serverSettings.enabled === false) {
          console.log(`[Perplexity MCP] Server ${toolCall.server} is disabled, skipping tool execution`);
          this.logToDebugPanel(`Tool execution skipped - server ${toolCall.server} disabled`);
          return;
        }

        // Check if connected to MCP bridge
        if (!this.isConnected) {
          console.log('[Perplexity MCP] Not connected to MCP bridge, skipping tool execution');
          this.logToDebugPanel('Tool execution failed - not connected to MCP bridge');
          this.injectToolError('Not connected to MCP bridge', responseElement);
          return;
        }

        const result = await this.executeToolInContext(
          toolCall.server || this.mcpServers[0]?.id,
          toolCall.tool,
          toolCall.parameters || {}
        );

        console.log('[Perplexity MCP] Tool execution result:', result);
        this.logToDebugPanel(`Tool ${toolCall.tool} completed successfully`);

        // Inject result back into the conversation
        this.injectToolResult(result, responseElement, toolCall);

      } catch (error) {
        console.error('[Perplexity MCP] Tool execution failed:', error);
        this.logToDebugPanel(`Tool ${toolCall.tool} failed: ${error.message}`);
        this.injectToolError(error.message, responseElement, toolCall);
      }
    }

    injectToolResult(result, responseElement, toolCall) {
      // Create a result container
      const resultContainer = document.createElement('div');
      resultContainer.className = 'mcp-tool-result';
      resultContainer.style.cssText = `
        margin: 15px 0;
        padding: 15px;
        background: linear-gradient(90deg, rgba(0, 150, 136, 0.1), rgba(0, 150, 136, 0.05));
        border: 1px solid #009688;
        border-left: 4px solid #009688;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        max-height: 300px;
        overflow-y: auto;
      `;

      const formattedResult = this.formatToolResult(result);
      const preview = formattedResult.length > 200 ? formattedResult.substring(0, 200) + '...' : formattedResult;

      resultContainer.innerHTML = `
        <div style="font-weight: bold; color: #009688; margin-bottom: 8px; display: flex; align-items: center; justify-content: space-between;">
          <span>🔧 MCP Tool Result: ${toolCall.server}/${toolCall.tool}</span>
          <button onclick="this.parentElement.parentElement.style.display='none'" 
                  style="background: #f44336; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 11px;">✖</button>
        </div>
        <div style="background: rgba(0,0,0,0.05); padding: 10px; border-radius: 4px; margin-bottom: 10px;">
          <strong>Status:</strong> ✅ Success<br>
          <strong>Executed:</strong> ${new Date().toLocaleTimeString()}
        </div>
        <details ${formattedResult.length <= 200 ? 'open' : ''}>
          <summary style="cursor: pointer; font-weight: bold; margin-bottom: 5px;">
            Result (${formattedResult.length} characters)
          </summary>
          <pre style="white-space: pre-wrap; margin: 5px 0 0 0; background: rgba(0,0,0,0.05); padding: 8px; border-radius: 4px; overflow-x: auto;">${formattedResult}</pre>
        </details>
      `;

      // Insert after the response element
      responseElement.parentNode.insertBefore(resultContainer, responseElement.nextSibling);

      // Also try to inject into the input for the next query
      this.injectFollowUpPrompt(result, toolCall);

      // Scroll to show the result
      resultContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    injectToolError(errorMessage, responseElement, toolCall) {
      // Create an error container
      const errorContainer = document.createElement('div');
      errorContainer.className = 'mcp-tool-error';
      errorContainer.style.cssText = `
        margin: 15px 0;
        padding: 15px;
        background: linear-gradient(90deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.05));
        border: 1px solid #f44336;
        border-left: 4px solid #f44336;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      `;

      errorContainer.innerHTML = `
        <div style="font-weight: bold; color: #f44336; margin-bottom: 8px; display: flex; align-items: center; justify-content: space-between;">
          <span>❌ MCP Tool Error: ${toolCall ? `${toolCall.server}/${toolCall.tool}` : 'Unknown'}</span>
          <button onclick="this.parentElement.parentElement.style.display='none'" 
                  style="background: #666; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 11px;">✖</button>
        </div>
        <div style="background: rgba(0,0,0,0.05); padding: 10px; border-radius: 4px; margin-bottom: 10px;">
          <strong>Status:</strong> ❌ Failed<br>
          <strong>Time:</strong> ${new Date().toLocaleTimeString()}
        </div>
        <div style="background: rgba(0,0,0,0.05); padding: 8px; border-radius: 4px;">
          <strong>Error:</strong> ${errorMessage}
        </div>
      `;

      // Insert after the response element
      responseElement.parentNode.insertBefore(errorContainer, responseElement.nextSibling);

      // Scroll to show the error
      errorContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    formatToolResult(result) {
      if (typeof result === 'string') return result;
      if (result && result.content) {
        if (Array.isArray(result.content)) {
          return result.content.map(item =>
            typeof item === 'string' ? item : JSON.stringify(item, null, 2)
          ).join('\n');
        }
        return result.content;
      }
      return JSON.stringify(result, null, 2);
    }

    injectFollowUpPrompt(toolResult, toolCall) {
      // Try to find the current input and add context about the tool result
      const input = this.findActiveInput();
      if (input) {
        const toolInfo = toolCall ? `${toolCall.server}/${toolCall.tool}` : 'MCP tool';
        const resultPreview = this.formatToolResult(toolResult).substring(0, 150);
        const contextPrompt = `\n\n[Previous ${toolInfo} result: ${resultPreview}${resultPreview.length >= 150 ? '...' : ''}]`;

        setTimeout(() => {
          const currentValue = input.value || input.textContent || '';
          if (!currentValue.includes('[Previous ') && !currentValue.includes('MCP tool result:')) {
            if (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT') {
              input.value = currentValue + contextPrompt;
              input.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
              input.textContent = currentValue + contextPrompt;
              input.dispatchEvent(new Event('input', { bubbles: true }));
            }
            console.log('[Perplexity MCP] Injected follow-up context for next query');
          }
        }, 1000);
      }
    }

    getConnectedToolsCount() {
      return this.mcpServers.reduce((count, server) => {
        return count + (server.tools ? server.tools.length : 0);
      }, 0);
    }

    findActiveInput() {
      // Try to find the currently active input
      return this.promptInput || document.querySelector('textarea:focus, input[type="text"]:focus, div[contenteditable="true"]:focus') ||
        document.querySelector('textarea#ask-input, input#ask-input, div[contenteditable="true"]');
    }

    // ...existing code...
  }

  // Initialize MCP client only if not already present
  if (!window.mcpClient) {
    const mcpClient = new PerplexityMcpClient();
    // Make it globally available
    window.mcpClient = mcpClient;
    console.log('[Perplexity MCP] Content script loaded and initialized');
  } else {
    console.log('[Perplexity MCP] Client already exists, skipping initialization');
  }

})(); // End IIFE