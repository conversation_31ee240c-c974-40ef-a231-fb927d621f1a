// Popup script for Perplexity Web MCP Bridge
// Manages the extension popup interface

class PopupManager {
    constructor() {
        this.updateInterval = null;
        this.isVisible = true;
        this.lastStatus = null;
        this.init();
    }

    async init() {
        await this.loadStatus();
        this.bindEvents();
        this.startRealTimeUpdates();
        
        // Handle visibility changes
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                this.loadStatus();
            }
        });
    }    async loadStatus() {
        try {
            // Get extension status
            const extensionStatus = await this.sendMessage({ type: 'get_status' });

            // Check bridge connection
            const bridgeStatus = await this.sendMessage({ type: 'bridge_test' });

            // Get MCP tools if connected
            let toolsData = null;
            if (bridgeStatus.success) {
                try {
                    toolsData = await this.sendMessage({ type: 'get_tools' });
                } catch (error) {
                    console.warn('Failed to load tools:', error);
                }
            }

            this.renderStatus(extensionStatus, bridgeStatus, toolsData);
            this.lastStatus = { extensionStatus, bridgeStatus, toolsData };

        } catch (error) {
            console.error('Failed to load status:', error);
            this.renderError('Failed to load status');
        }
    }    renderStatus(extensionStatus, bridgeStatus, toolsData) {
        const statusSection = document.getElementById('statusSection');
        const actionsSection = document.getElementById('actionsSection');

        // Calculate total tools
        let totalTools = 0;
        let serverCount = 0;
        let toolsBreakdown = '';
        
        if (bridgeStatus.success && bridgeStatus.servers) {
            serverCount = bridgeStatus.servers.length;
            bridgeStatus.servers.forEach(server => {
                if (server.tools) {
                    totalTools += server.tools.length;
                }
            });
            
            if (serverCount > 0) {
                toolsBreakdown = `
                    <div class="tools-breakdown">
                        <div class="tools-summary">
                            <span class="tools-count">${totalTools}</span>
                            <span class="tools-label">tools from ${serverCount} server${serverCount !== 1 ? 's' : ''}</span>
                        </div>
                        <div class="servers-list">
                            ${bridgeStatus.servers.map(server => `
                                <div class="server-item">
                                    <span class="server-name">${server.name || server.id}</span>
                                    <span class="server-tools">${server.tools?.length || 0} tools</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
        }

        statusSection.innerHTML = `
            <div class="status-item">
                <span class="status-label">Extension</span>
                <div class="status-indicator">
                    <div class="status-dot connected"></div>
                    <span>Active</span>
                </div>
            </div>
            <div class="status-item">
                <span class="status-label">Bridge Connection</span>
                <div class="status-indicator">
                    <div class="status-dot ${bridgeStatus.success ? 'connected' : 'disconnected'}"></div>
                    <span>${bridgeStatus.success ? 'Connected' : 'Disconnected'}</span>
                    ${bridgeStatus.success ? '<span class="status-time">' + new Date().toLocaleTimeString() + '</span>' : ''}
                </div>
            </div>
            ${bridgeStatus.success ? `
                <div class="status-item">
                    <span class="status-label">MCP Servers</span>
                    <div class="status-indicator">
                        <span>${serverCount} active</span>
                    </div>
                </div>
                <div class="status-item">
                    <span class="status-label">Available Tools</span>
                    <div class="status-indicator">
                        <span>${totalTools} ready</span>
                    </div>
                </div>
                <div class="status-item">
                    <span class="status-label">Browser Clients</span>
                    <div class="status-indicator">
                        <span>${bridgeStatus.clients || 0} connected</span>
                    </div>
                </div>
                ${toolsBreakdown}
            ` : `
                <div class="connection-help">
                    <div class="help-title">Bridge Not Connected</div>
                    <div class="help-text">Make sure the bridge is running:</div>
                    <div class="help-command">npx perplexity-web-mcp-bridge</div>
                </div>
            `}
        `;

        actionsSection.style.display = 'block';
    }

    renderError(message) {
        const statusSection = document.getElementById('statusSection');
        statusSection.innerHTML = `
      <div style="text-align: center; color: #ff4444; padding: 20px;">
        ⚠️ ${message}
      </div>
    `;
    }    bindEvents() {
        // Settings button in header
        const settingsBtn = document.querySelector('.settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.openSettings();
            });
        }

        document.getElementById('testBridgeBtn').addEventListener('click', () => {
            this.testBridge();
        });

        document.getElementById('openPerplexityBtn').addEventListener('click', () => {
            chrome.tabs.create({ url: 'https://perplexity.ai' });
        });

        document.getElementById('configBtn').addEventListener('click', () => {
            this.openSettings();
        });

        // Add refresh button handler
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshStatus();
            });
        }

        // Add quick tools button handler
        const quickToolsBtn = document.getElementById('quickToolsBtn');
        if (quickToolsBtn) {
            quickToolsBtn.addEventListener('click', () => {
                this.showQuickTools();
            });
        }
    }

    startRealTimeUpdates() {
        // Update every 5 seconds when visible
        this.updateInterval = setInterval(() => {
            if (this.isVisible) {
                this.loadStatus();
            }
        }, 5000);
    }

    async refreshStatus() {
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.textContent = 'Refreshing...';
        }

        await this.loadStatus();

        if (refreshBtn) {
            refreshBtn.textContent = 'Refreshed!';
            setTimeout(() => {
                refreshBtn.disabled = false;
                refreshBtn.textContent = 'Refresh Status';
            }, 1000);
        }
    }

    async showQuickTools() {
        if (!this.lastStatus?.bridgeStatus?.success) {
            alert('Bridge not connected. Please connect first.');
            return;
        }

        const toolsWindow = window.open('', 'mcpTools', 'width=600,height=400');
        
        let toolsHtml = `
            <html>
                <head>
                    <title>MCP Tools Overview</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
                        .tool { padding: 10px; border: 1px solid #333; margin: 5px 0; border-radius: 5px; }
                        .tool-name { font-weight: bold; color: #00ff88; }
                        .tool-desc { font-size: 0.9em; color: #ccc; margin-top: 5px; }
                        .server-header { background: #333; padding: 10px; margin: 10px 0; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <h2>🛠️ Available MCP Tools</h2>
        `;

        if (this.lastStatus.bridgeStatus.servers) {
            this.lastStatus.bridgeStatus.servers.forEach(server => {
                toolsHtml += `<div class="server-header"><strong>${server.name || server.id}</strong> (${server.tools?.length || 0} tools)</div>`;
                
                if (server.tools) {
                    server.tools.forEach(tool => {
                        toolsHtml += `
                            <div class="tool">
                                <div class="tool-name">${tool.name}</div>
                                <div class="tool-desc">${tool.description || 'No description'}</div>
                            </div>
                        `;
                    });
                }
            });
        }

        toolsHtml += '</body></html>';
        toolsWindow.document.write(toolsHtml);
    }

    openSettings() {
        // Open settings page in a new tab
        chrome.tabs.create({ 
            url: chrome.runtime.getURL('settings.html') 
        });
    }

    async testBridge() {
        const btn = document.getElementById('testBridgeBtn');
        btn.disabled = true;
        btn.textContent = 'Testing...';

        try {
            await this.loadStatus();
            btn.textContent = 'Test Completed';
            setTimeout(() => {
                btn.disabled = false;
                btn.textContent = 'Test Bridge Connection';
            }, 2000);
        } catch (error) {
            btn.textContent = 'Test Failed';
            setTimeout(() => {
                btn.disabled = false;
                btn.textContent = 'Test Bridge Connection';
            }, 2000);
        }
    }

    sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(response);
                }
            });
        });
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const popup = new PopupManager();
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (popup.updateInterval) {
            clearInterval(popup.updateInterval);
        }
    });
});
