:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Success state for test buttons */
.btn.mcp-test-success {
  background-color: var(--color-success) !important;
  color: var(--color-btn-primary-text) !important;
  border-color: var(--color-success) !important;
  animation: successPulse 0.5s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--color-success-rgb), 0.7);
  }

  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 6px rgba(var(--color-success-rgb), 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--color-success-rgb), 0);
  }
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(var(--color-success-rgb, 33, 128, 141),
      var(--status-bg-opacity));
  color: var(--color-success);
  border: 1px solid rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(var(--color-error-rgb, 192, 21, 47),
      var(--status-bg-opacity));
  color: var(--color-error);
  border: 1px solid rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(var(--color-warning-rgb, 168, 75, 47),
      var(--status-bg-opacity));
  color: var(--color-warning);
  border: 1px solid rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(var(--color-info-rgb, 98, 108, 113),
      var(--status-bg-opacity));
  color: var(--color-info);
  border: 1px solid rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--space-4);
}

.gap-8 {
  gap: var(--space-8);
}

.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}

.mt-8 {
  margin-top: var(--space-8);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}

.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}

.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}

.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}

.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}

.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://www.perplexity.ai/fonts/FKGroteskNeue.woff2') format('woff2');
}


/* MCP Interface Styles for Perplexity */

.mcp-panel {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  width: 320px !important;
  max-height: 600px !important;
  background: var(--color-surface, #ffffff) !important;
  border: 1px solid var(--color-card-border, #e0e0e0) !important;
  border-radius: var(--radius-lg, 12px) !important;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1)) !important;
  z-index: 10000 !important;
  font-family: var(--font-family-base, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  color: var(--color-text, #333333) !important;
  backdrop-filter: blur(10px) !important;
  transition: all var(--duration-normal, 250ms) var(--ease-standard, ease) !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  display: block !important;
}

.mcp-panel.hidden {
  opacity: 0;
  transform: translateX(100%);
  pointer-events: none;
}

.mcp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-20);
  border-bottom: 1px solid var(--color-card-border-inner);
  background: var(--color-surface);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.mcp-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.mcp-status {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  font-size: var(--font-size-sm);
}

/* Enhanced status-indicator styles to match connection-dot */
.status-indicator {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  transition: all var(--duration-fast) var(--ease-standard) !important;
  background: var(--color-text-secondary) !important;
  animation: pulse 2s infinite;
}

.status-indicator.connected {
  background-color: var(--color-success) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-success-rgb), 0.2) !important;
  animation: statusPulse 2s ease-in-out infinite !important;
}

.status-indicator.disconnected {
  background-color: var(--color-error) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-error-rgb), 0.2) !important;
}

.status-indicator.connecting {
  background-color: var(--color-warning) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-warning-rgb), 0.2) !important;
  animation: statusPulse 1s infinite !important;
}

/* Enhanced status-text styles to match connection-text */
.status-text {
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  flex-shrink: 0 !important;
  color: var(--color-text-secondary) !important;
}

.mcp-tools-status .status-text.connected,
.status-text.connected {
  color: var(--color-success) !important;
}

.mcp-tools-status .status-text.disconnected,
.status-text.disconnected {
  color: var(--color-error) !important;
}

.mcp-tools-status .status-text.connecting,
.status-text.connecting {
  color: var(--color-warning) !important;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.mcp-content {
  padding: var(--space-16);
  max-height: 500px;
  overflow-y: auto;
}

.mcp-content::-webkit-scrollbar {
  width: var(--space-6);
}

.mcp-content::-webkit-scrollbar-track {
  background: var(--color-secondary);
  border-radius: var(--radius-sm);
}

.mcp-content::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: var(--radius-sm);
}

.mcp-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

.mcp-servers {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.mcp-server {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--space-12);
  transition: all var(--duration-fast) var(--ease-standard);
}

.mcp-server:hover {
  border-color: var(--color-primary);
  background: var(--color-secondary);
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.server-name {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
}

.server-status {
  font-size: var(--font-size-xs);
  padding: var(--space-2) var(--space-8);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-weight-semibold);
}

.server-status.running {
  background-color: rgba(var(--color-success-rgb), var(--status-bg-opacity));
  color: var(--color-success);
  border: 1px solid rgba(var(--color-success-rgb), var(--status-border-opacity));
}

.server-status.connected {
  background-color: rgba(var(--color-info-rgb), var(--status-bg-opacity));
  color: var(--color-info);
  border: 1px solid rgba(var(--color-info-rgb), var(--status-border-opacity));
}

.server-status.disconnected {
  background-color: rgba(var(--color-error-rgb), var(--status-bg-opacity));
  color: var(--color-error);
  border: 1px solid rgba(var(--color-error-rgb), var(--status-border-opacity));
}

.server-actions {
  display: flex;
  gap: var(--space-8);
}

.server-btn {
  background: var(--color-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  font-weight: var(--font-weight-medium);
}

.server-btn:hover {
  background: var(--color-secondary-hover);
  border-color: var(--color-primary);
}

.server-btn:active {
  transform: translateY(1px);
  background: var(--color-secondary-active);
}

.server-btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.loading,
.no-servers {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  padding: var(--space-20);
  font-style: italic;
}

.mcp-toggle-btn {
  position: fixed;
  top: var(--space-20);
  right: 360px;
  width: 40px;
  height: 40px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  color: var(--color-text);
  font-size: var(--font-size-lg);
  cursor: pointer;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-fast) var(--ease-standard);
  backdrop-filter: blur(10px);
}

.mcp-toggle-btn:hover {
  background: var(--color-secondary);
  border-color: var(--color-primary);
  transform: scale(1.05);
}

.mcp-toggle-btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.mcp-notification {
  position: fixed;
  top: 80px;
  right: var(--space-20);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-12) var(--space-16);
  color: var(--color-text);
  font-size: var(--font-size-md);
  z-index: 10001;
  max-width: 300px;
  box-shadow: var(--shadow-md);
  animation: slideIn var(--duration-normal) var(--ease-standard);
}

.mcp-notification.success {
  border-color: var(--color-success);
  background-color: rgba(var(--color-success-rgb), var(--status-bg-opacity));
}

.mcp-notification.error {
  border-color: var(--color-error);
  background-color: rgba(var(--color-error-rgb), var(--status-bg-opacity));
}

.mcp-notification.info {
  border-color: var(--color-info);
  background-color: rgba(var(--color-info-rgb), var(--status-bg-opacity));
}

.mcp-notification.warning {
  border-color: var(--color-warning);
  background-color: rgba(var(--color-warning-rgb), var(--status-bg-opacity));
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mcp-panel {
    width: calc(100vw - 40px);
    right: var(--space-20);
    left: var(--space-20);
    max-width: none;
  }

  .mcp-toggle-btn {
    right: var(--space-20);
  }
}

/* Mobile portrait responsive styles */
@media (max-width: 480px) {
  .mcp-panel {
    top: var(--space-16);
    right: var(--space-16);
    left: var(--space-16);
    width: calc(100vw - 32px);
  }

  .mcp-header {
    padding: var(--space-12) var(--space-16);
  }

  .mcp-content {
    padding: var(--space-12);
  }

  .server-actions {
    flex-wrap: wrap;
  }

  .mcp-toggle-btn {
    top: var(--space-16);
    right: var(--space-16);
    width: 36px;
    height: 36px;
    font-size: var(--font-size-md);
  }

  .mcp-notification {
    right: var(--space-16);
    max-width: calc(100vw - 32px);
  }
}

/* Integration with Perplexity's existing UI */
.mcp-panel * {
  box-sizing: border-box;
}

/* MCP Prompt Enhancement Styles */
.mcp-prompt-indicator {
  position: absolute !important;
  right: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  background: var(--color-success) !important;
  color: var(--color-btn-primary-text) !important;
  padding: 4px 8px !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  z-index: 9998 !important;
  opacity: 0.9 !important;
  transition: opacity var(--duration-fast) var(--ease-standard) !important;
  pointer-events: none !important;
}

.mcp-prompt-indicator:hover {
  opacity: 1 !important;
}

.mcp-indicator-icon {
  font-size: 12px !important;
}

.mcp-indicator-text {
  white-space: nowrap !important;
}

#prompt-indicator {
  top: 0 !important;
}

/* Ensure our elements don't interfere with Perplexity's layout */
.mcp-panel,
.mcp-toggle-btn,
.mcp-notification {
  all: initial;
  position: fixed !important;
  z-index: 10000 !important;
  font-family: var(--font-family-base) !important;
}

/* Status Indicator Styles */
.mcp-header-status {
  position: fixed !important;
  top: 20px !important;
  left: 20px !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) !important;
  padding: 8px 12px !important;
  box-shadow: var(--shadow-md) !important;
  font-family: var(--font-family-base) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text) !important;
  transition: all var(--duration-normal) var(--ease-standard) !important;
  cursor: pointer !important;
  backdrop-filter: blur(10px) !important;
  max-width: 300px !important;
}

.mcp-header-status:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-1px) !important;
}

.mcp-status-dot {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  transition: all var(--duration-fast) var(--ease-standard) !important;
}

.mcp-status-dot.connected {
  background-color: var(--color-success) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-success-rgb), 0.2) !important;
  animation: statusPulse 2s ease-in-out infinite !important;
}

.mcp-status-dot.disconnected {
  background-color: var(--color-error) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-error-rgb), 0.2) !important;
}

.mcp-status-dot.connecting {
  background-color: var(--color-warning) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-warning-rgb), 0.2) !important;
  animation: statusPulse 1s infinite !important;
}

@keyframes statusPulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.mcp-status-text {
  font-size: var(--font-size-sm) !important;
  color: var(--color-text) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.mcp-status-badge {
  background: var(--color-primary) !important;
  color: var(--color-btn-primary-text) !important;
  padding: 2px 6px !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  min-width: 16px !important;
  text-align: center !important;
}

/* Status Tooltip */
.mcp-status-tooltip {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  margin-top: 8px !important;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) !important;
  padding: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 10001 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateY(-5px) !important;
  transition: all var(--duration-normal) var(--ease-standard) !important;
  min-width: 280px !important;
  max-width: 400px !important;
  backdrop-filter: blur(10px) !important;
  font-family: var(--font-family-base) !important;
}

.mcp-header-status:hover .mcp-status-tooltip {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateY(0) !important;
}

.mcp-tooltip-title {
  font-size: var(--font-size-base) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-text) !important;
  margin-bottom: 8px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.mcp-tooltip-content {
  font-size: var(--font-size-sm) !important;
  color: var(--color-text-secondary) !important;
  line-height: var(--line-height-normal) !important;
}

.mcp-tooltip-section {
  margin-bottom: 8px !important;
}

.mcp-tooltip-section:last-child {
  margin-bottom: 0 !important;
}

.mcp-server-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 4px 0 !important;
  border-bottom: 1px solid var(--color-border) !important;
}

.mcp-server-item:last-child {
  border-bottom: none !important;
}

.mcp-server-name {
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text) !important;
}

.mcp-server-tools {
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-secondary) !important;
}

/* Floating Status Panel */
.mcp-floating-status {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 9998 !important;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-lg) !important;
  padding: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  font-family: var(--font-family-base) !important;
  backdrop-filter: blur(10px) !important;
  transition: all var(--duration-normal) var(--ease-standard) !important;
  min-width: 160px !important;
}

.mcp-floating-status:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.mcp-floating-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 8px !important;
}

.mcp-floating-title {
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-text) !important;
  margin: 0 !important;
}

.mcp-floating-count {
  background: var(--color-primary) !important;
  color: var(--color-btn-primary-text) !important;
  padding: 2px 6px !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-bold) !important;
  min-width: 20px !important;
  text-align: center !important;
}

.mcp-floating-actions {
  display: flex !important;
  gap: 6px !important;
}

.mcp-floating-btn {
  background: var(--color-secondary) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-sm) !important;
  padding: 4px 8px !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text) !important;
  cursor: pointer !important;
  transition: all var(--duration-fast) var(--ease-standard) !important;
  text-decoration: none !important;
}

.mcp-floating-btn:hover {
  background: var(--color-secondary-hover) !important;
  transform: translateY(-1px) !important;
}

.mcp-floating-btn:active {
  background: var(--color-secondary-active) !important;
  transform: translateY(0) !important;
}

/* Floating Status Tooltip */
.mcp-floating-tooltip {
  position: absolute !important;
  bottom: 100% !important;
  right: 0 !important;
  margin-bottom: 8px !important;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) !important;
  padding: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 10001 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateY(5px) !important;
  transition: all var(--duration-normal) var(--ease-standard) !important;
  min-width: 200px !important;
  max-width: 300px !important;
  backdrop-filter: blur(10px) !important;
  font-family: var(--font-family-base) !important;
  white-space: nowrap !important;
}

.mcp-floating-status:hover .mcp-floating-tooltip {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateY(0) !important;
}

/* Clean MCP Tools Status (Top-Right Area) - Unified Design */
.mcp-tools-status {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) !important;
  padding: 8px 12px !important;
  box-shadow: var(--shadow-md) !important;
  font-family: var(--font-family-base) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text) !important;
  transition: all var(--duration-normal) var(--ease-standard) !important;
  cursor: pointer !important;
  backdrop-filter: blur(10px) !important;
  min-width: 280px !important;
  flex-direction: column;
}

.mcp-tools-status:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-1px) !important;
}

.mcp-tools-header {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  white-space: nowrap !important;
}

.mcp-icon {
  font-size: var(--font-size-base) !important;
  flex-shrink: 0 !important;
}

.mcp-label {
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-text) !important;
  flex-shrink: 0 !important;
}

.connection-dot {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  transition: all var(--duration-fast) var(--ease-standard) !important;
}

.connection-dot.connected {
  background-color: var(--color-success) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-success-rgb), 0.2) !important;
  animation: statusPulse 2s ease-in-out infinite !important;
}

.connection-dot.disconnected {
  background-color: var(--color-error) !important;
  box-shadow: 0 0 0 2px rgba(var(--color-error-rgb), 0.2) !important;
}

/* Status pulse animation for connected state */
@keyframes statusPulse {
  0% {
    box-shadow: 0 0 0 2px rgba(var(--color-success-rgb), 0.2) !important;
  }

  50% {
    box-shadow: 0 0 0 4px rgba(var(--color-success-rgb), 0.4) !important;
  }

  100% {
    box-shadow: 0 0 0 2px rgba(var(--color-success-rgb), 0.2) !important;
  }
}

.connection-text {
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  flex-shrink: 0 !important;
}

.connection-text.connected {
  color: var(--color-success) !important;
}

.connection-text.disconnected {
  color: var(--color-error) !important;
}

.connection-text.connecting {
  color: var(--color-warning) !important;
}

.tools-count-badge {
  background: var(--color-primary) !important;
  color: var(--color-btn-primary-text) !important;
  padding: 3px 8px !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  margin-left: 4px !important;
  flex-shrink: 0 !important;
}

/* MCP Tools Tooltip */
.mcp-tools-tooltip {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  margin-top: 8px !important;
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) !important;
  padding: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 10001 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateY(-5px) !important;
  transition: all var(--duration-normal) var(--ease-standard) !important;
  min-width: 280px !important;
  max-width: 400px !important;
  backdrop-filter: blur(10px) !important;
  font-family: var(--font-family-base) !important;
}

.mcp-tools-status:hover .mcp-tools-tooltip {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateY(0) !important;
}

.mcp-tools-tooltip .tooltip-header {
  font-size: var(--font-size-base) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-text) !important;
  margin-bottom: 8px !important;
  border-bottom: 1px solid var(--color-border) !important;
  padding-bottom: 6px !important;
}

.mcp-tools-tooltip .tooltip-tools-list {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.mcp-tools-tooltip .tool-item {
  padding: 6px 0 !important;
  border-bottom: 1px solid var(--color-border) !important;
}

.mcp-tools-tooltip .tool-item:last-child {
  border-bottom: none !important;
}

.mcp-tools-tooltip .tool-name {
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-text) !important;
  font-size: var(--font-size-sm) !important;
  margin-bottom: 2px !important;
}

.mcp-tools-tooltip .tool-server {
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-secondary) !important;
  margin-bottom: 2px !important;
}

.mcp-tools-tooltip .tool-description {
  font-size: var(--font-size-xs) !important;
  color: var(--color-text-secondary) !important;
  line-height: var(--line-height-normal) !important;
}

.mcp-tools-tooltip .no-tools,
.mcp-tools-tooltip .loading {
  text-align: center !important;
  color: var(--color-text-secondary) !important;
  font-size: var(--font-size-sm) !important;
  padding: 12px !important;
  font-style: italic !important;
}

.mcp-tools-tooltip .more-tools {
  text-align: center !important;
  color: var(--color-text-secondary) !important;
  font-size: var(--font-size-xs) !important;
  padding: 6px 0 !important;
  margin-top: 6px !important;
  border-top: 1px solid var(--color-border) !important;
  font-style: italic !important;
}

/* Responsive design for the unified status */
@media (max-width: 768px) {
  .mcp-tools-status {
    top: 10px !important;
    right: 10px !important;
    padding: 6px 10px !important;
    font-size: var(--font-size-xs) !important;
    max-width: 400px !important;
  }

  .mcp-tools-header {
    gap: 6px !important;
  }

  .tools-count-badge {
    padding: 2px 6px !important;
    font-size: 10px !important;
  }

  .mcp-tools-tooltip {
    min-width: 250px !important;
    max-width: calc(100vw - 40px) !important;
  }
}

@media (max-width: 480px) {
  .mcp-tools-status {
    position: relative !important;
    top: auto !important;
    right: auto !important;
    margin: 10px !important;
    max-width: calc(100vw - 20px) !important;
  }

  .mcp-tools-header {
    flex-wrap: wrap !important;
    gap: 4px !important;
  }

  .tools-count-badge {
    margin-left: 0 !important;
    margin-top: 4px !important;
    flex-basis: 100% !important;
    text-align: center !important;
  }
}