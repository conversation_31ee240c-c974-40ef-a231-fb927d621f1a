<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Injection Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .input-test {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
            font-family: inherit;
        }

        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #005a87;
        }

        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>

<body>
    <h1>🔧 MCP Injection Test Page</h1>
    <p>This page simulates Perplexity's input elements for testing MCP injection logic.</p>

    <div class="test-section">
        <h3>Test 1: Textarea with "Ask anything" placeholder</h3>
        <form id="form1">
            <textarea id="ask-input" placeholder="Ask anything..." class="input-test"></textarea>
            <button type="submit">Submit</button>
        </form>
    </div>

    <div class="test-section">
        <h3>Test 2: Contenteditable div</h3>
        <form id="form2">
            <div contenteditable="true" role="textbox" class="input-test" style="border: 1px solid #ddd; padding: 10px;"
                data-placeholder="Ask me anything..."></div>
            <button type="submit">Submit</button>
        </form>
    </div>

    <div class="test-section">
        <h3>Test 3: Regular textarea</h3>
        <form id="form3">
            <textarea placeholder="Ask a question..." class="input-test"></textarea>
            <button type="submit">Submit</button>
        </form>
    </div>

    <div class="test-section">
        <h3>Manual Test Controls</h3>
        <button onclick="testPromptInjection()">Test MCP Injection</button>
        <button onclick="scanInputs()">Scan for Inputs</button>
        <button onclick="clearLog()">Clear Log</button>
        <div id="test-log" class="log">
            <div><strong>Test Log:</strong></div>
        </div>
    </div>

    <script>
        // Test functions
        function log(message) {
            const logEl = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${time}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function testPromptInjection() {
            log('Testing MCP prompt injection...');

            // Test with file-related prompt
            const testPrompts = [
                'list files in current directory',
                'read a specific file',
                'search for code in repository',
                'just a normal question'
            ];

            testPrompts.forEach((prompt, index) => {
                const textarea = document.querySelector(`#form${index + 1} textarea, #form${index + 1} div[contenteditable]`);
                if (textarea) {
                    if (textarea.tagName === 'TEXTAREA') {
                        textarea.value = prompt;
                    } else {
                        textarea.textContent = prompt;
                    }

                    // Trigger events
                    textarea.dispatchEvent(new Event('input', { bubbles: true }));

                    log(`Set prompt in form ${index + 1}: "${prompt}"`);
                } else {
                    log(`No input found in form ${index + 1}`);
                }
            });
        }

        function scanInputs() {
            log('Scanning for input elements...');

            const selectors = [
                'textarea#ask-input',
                'textarea[placeholder*="Ask anything"]',
                'div[contenteditable="true"]',
                'textarea[placeholder*="Ask"]',
                'input[placeholder*="ask"]'
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                log(`Selector "${selector}": found ${elements.length} elements`);
            });

            // Check if MCP client is available
            if (window.mcpClient) {
                log('✅ MCP Client is available globally');
                log(`Connected: ${window.mcpClient.isConnected}`);
                log(`Servers: ${window.mcpClient.mcpServers.length}`);
            } else {
                log('❌ MCP Client not found globally');
            }
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '<div><strong>Test Log:</strong></div>';
        }

        // Set up form submission handlers
        document.querySelectorAll('form').forEach((form, index) => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const input = form.querySelector('textarea, div[contenteditable]');
                const value = input.value || input.textContent;
                log(`Form ${index + 1} submitted with: "${value}"`);

                // Test if MCP enhancement was applied
                if (value.includes('Available MCP Tools')) {
                    log('✅ MCP enhancement detected!');
                } else {
                    log('❌ No MCP enhancement found');
                }
            });
        });

        // Initial scan
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, scanning for MCP integration...');
                scanInputs();
            }, 1000);
        });
    </script>
</body>

</html>