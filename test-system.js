#!/usr/bin/env node

/**
 * Test script for Perplexity Web MCP System
 * This script tests the complete MCP integration workflow
 */

const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

class McpSystemTester {
    constructor() {
        this.bridgeUrl = 'ws://localhost:54319';
        this.testResults = [];
    }

    async runTests() {
        console.log('🧪 Starting Perplexity Web MCP System Tests...\n');

        try {
            await this.testBridgeConnection();
            await this.testMcpServers();
            await this.testFileOperations();
            await this.testPromptInjection();

            this.printResults();
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    async testBridgeConnection() {
        console.log('1. Testing WebSocket Bridge Connection...');

        return new Promise((resolve, reject) => {
            const ws = new WebSocket(this.bridgeUrl);

            const timeout = setTimeout(() => {
                ws.terminate();
                this.addResult('Bridge Connection', false, 'Connection timeout');
                reject(new Error('Connection timeout'));
            }, 5000);

            ws.on('open', () => {
                clearTimeout(timeout);
                this.addResult('Bridge Connection', true, 'Successfully connected to bridge');
                console.log('   ✅ Bridge connection established');
                ws.close();
                resolve();
            });

            ws.on('error', (error) => {
                clearTimeout(timeout);
                this.addResult('Bridge Connection', false, `Connection failed: ${error.message}`);
                console.log('   ❌ Bridge connection failed:', error.message);
                reject(error);
            });
        });
    }

    async testMcpServers() {
        console.log('\n2. Testing MCP Server Discovery...');

        return new Promise((resolve) => {
            const ws = new WebSocket(this.bridgeUrl);

            ws.on('open', () => {
                // Request server list
                ws.send(JSON.stringify({
                    type: 'get_servers'
                }));
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.type === 'servers') {
                        const serverCount = message.servers ? message.servers.length : 0;

                        if (serverCount > 0) {
                            this.addResult('MCP Server Discovery', true, `Found ${serverCount} MCP servers`);
                            console.log(`   ✅ Found ${serverCount} MCP servers`);

                            // Test individual servers
                            message.servers.forEach(server => {
                                console.log(`      - ${server.name} (${server.status})`);
                            });
                        } else {
                            this.addResult('MCP Server Discovery', false, 'No MCP servers found');
                            console.log('   ⚠️  No MCP servers configured');
                        }

                        ws.close();
                        resolve();
                    }
                } catch (error) {
                    this.addResult('MCP Server Discovery', false, `Parse error: ${error.message}`);
                    console.log('   ❌ Server discovery failed:', error.message);
                    ws.close();
                    resolve();
                }
            });

            ws.on('error', (error) => {
                this.addResult('MCP Server Discovery', false, `WebSocket error: ${error.message}`);
                console.log('   ❌ Server discovery failed:', error.message);
                resolve();
            });
        });
    }

    async testFileOperations() {
        console.log('\n3. Testing File System Operations...');

        // Test file reading capability
        const testFile = path.join(__dirname, 'README.md');

        if (fs.existsSync(testFile)) {
            try {
                const content = fs.readFileSync(testFile, 'utf8');
                const preview = content.substring(0, 100) + '...';

                this.addResult('File Operations', true, `Successfully read ${testFile}`);
                console.log(`   ✅ File reading works`);
                console.log(`      Preview: ${preview}`);
            } catch (error) {
                this.addResult('File Operations', false, `File read error: ${error.message}`);
                console.log('   ❌ File reading failed:', error.message);
            }
        } else {
            this.addResult('File Operations', false, 'Test file not found');
            console.log('   ⚠️  Test file not found');
        }
    }

    async testPromptInjection() {
        console.log('\n4. Testing Prompt Injection Logic...');

        // Test the prompt enhancement keywords
        const testPrompts = [
            'read the latest file in my project',
            'search for recent changes in the repository',
            'analyze the current code structure',
            'just a regular question without keywords'
        ];

        // Simple keyword detection logic (mirrors content script)
        const mcpKeywords = [
            'file', 'read', 'write', 'directory', 'folder', 'path',
            'api', 'data', 'search', 'database', 'github', 'git',
            'code', 'repository', 'analysis', 'recent', 'latest',
            'current', 'real-time', 'live', 'browse', 'fetch'
        ];

        let enhancementCount = 0;

        testPrompts.forEach((prompt, index) => {
            const shouldEnhance = mcpKeywords.some(keyword =>
                prompt.toLowerCase().includes(keyword)
            );

            if (shouldEnhance) {
                enhancementCount++;
                console.log(`   ✅ Prompt ${index + 1}: "${prompt}" - Will be enhanced`);
            } else {
                console.log(`   ⏭️  Prompt ${index + 1}: "${prompt}" - No enhancement needed`);
            }
        });

        this.addResult('Prompt Injection Logic', true,
            `Enhanced ${enhancementCount}/${testPrompts.length} test prompts`);
    }

    addResult(test, passed, details) {
        this.testResults.push({
            test,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('========================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;

        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            console.log(`${icon} ${result.test}: ${result.details}`);
        });

        console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);

        if (passed === total) {
            console.log('\n🎉 All tests passed! The Perplexity Web MCP system is ready to use.');
            console.log('\nNext steps:');
            console.log('1. Load the browser extension in Chrome/Edge');
            console.log('2. Navigate to https://www.perplexity.ai');
            console.log('3. Look for the MCP toggle button (🔧) in the top-right');
            console.log('4. Test prompt injection with keywords like "read file" or "search code"');
        } else {
            console.log('\n⚠️  Some tests failed. Please check the issues above.');
        }
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    const tester = new McpSystemTester();
    tester.runTests().catch(console.error);
}

module.exports = McpSystemTester;
