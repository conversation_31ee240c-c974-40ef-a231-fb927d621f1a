// Settings page functionality for Perplexity MCP Bridge
class SettingsManager {
    constructor() {
        this.settings = this.getDefaultSettings();
        this.currentSection = 'general';
        this.init();
    }

    getDefaultSettings() {
        return {
            // General settings
            bridgeEnabled: true,
            autoConnect: true,
            bridgeUrl: 'ws://localhost:3001',
            alwaysInject: false,
            smartDetection: true,

            // Bridge settings
            reconnectAttempts: 5,
            connectionTimeout: 5000,
            responseMonitoring: true,
            autoExecute: true,
            executionTimeout: 30000,

            // Server settings
            autoDiscoverServers: true,
            serverSettings: {},

            // UI settings
            showToolsPanel: true,
            panelPosition: 'right',
            showDebugPanel: false,
            showToolResults: true,
            resultStyle: 'inline',

            // Advanced settings
            debugLogging: false,
            verboseLogging: false,
            betaFeatures: false
        };
    }

    async init() {
        await this.loadSettings();
        this.bindEvents();
        this.loadServers();
        this.updateUI();
    }

    async loadSettings() {
        try {
            const stored = await this.getStoredSettings();
            this.settings = { ...this.getDefaultSettings(), ...stored };
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    async getStoredSettings() {
        return new Promise((resolve) => {
            chrome.storage.sync.get(['mcpSettings'], (result) => {
                resolve(result.mcpSettings || {});
            });
        });
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({ mcpSettings: this.settings });
            this.showNotification('Settings saved successfully', 'success');
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('Failed to save settings', 'error');
        }
    }

    bindEvents() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Setting inputs
        document.querySelectorAll('input, select').forEach(input => {
            const settingKey = input.id;
            if (settingKey && this.settings.hasOwnProperty(settingKey)) {
                input.addEventListener('change', (e) => {
                    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
                    this.updateSetting(settingKey, value);
                });
            }
        });

        // Header actions
        document.getElementById('exportBtn').addEventListener('click', () => this.exportSettings());
        document.getElementById('importBtn').addEventListener('click', () => this.importSettings());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetSettings());

        // Server management
        document.getElementById('refreshServers').addEventListener('click', () => this.loadServers());

        // File import
        document.getElementById('settingsFileInput').addEventListener('change', (e) => {
            this.handleFileImport(e.target.files[0]);
        });

        // Advanced section
        document.getElementById('exportSettings').addEventListener('click', () => this.exportSettings());
        document.getElementById('importFile').addEventListener('change', (e) => {
            this.handleFileImport(e.target.files[0]);
        });
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.settings-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(sectionName).classList.add('active');

        // Update title
        const titles = {
            general: 'General Settings',
            bridge: 'Bridge Settings',
            servers: 'MCP Servers',
            ui: 'Interface Settings',
            advanced: 'Advanced Settings',
            marketplace: 'Server Marketplace',
            about: 'About'
        };
        document.getElementById('sectionTitle').textContent = titles[sectionName] || 'Settings';

        this.currentSection = sectionName;
    }

    updateSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();

        // Send update to content script if needed
        this.notifyContentScript(key, value);
    }

    async notifyContentScript(key, value) {
        try {
            const tabs = await chrome.tabs.query({ url: '*://perplexity.ai/*' });
            for (const tab of tabs) {
                chrome.tabs.sendMessage(tab.id, {
                    type: 'setting_update',
                    key: key,
                    value: value
                });
            }
        } catch (error) {
            console.error('Failed to notify content script:', error);
        }
    }

    updateUI() {
        // Update all form inputs with current settings
        Object.keys(this.settings).forEach(key => {
            const input = document.getElementById(key);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = this.settings[key];
                } else {
                    input.value = this.settings[key];
                }
            }
        });
    }

    async loadServers() {
        const serversList = document.getElementById('serversList');
        serversList.innerHTML = '<div class="loading">Loading servers...</div>';

        try {
            // Get servers from background script
            const response = await this.sendMessage({ type: 'get_servers' });
            
            if (response.success && response.servers) {
                this.renderServers(response.servers);
            } else {
                serversList.innerHTML = '<div class="loading">No servers connected</div>';
            }
        } catch (error) {
            console.error('Failed to load servers:', error);
            serversList.innerHTML = '<div class="loading">Failed to load servers</div>';
        }
    }

    renderServers(servers) {
        const serversList = document.getElementById('serversList');
        
        if (servers.length === 0) {
            serversList.innerHTML = '<div class="loading">No servers connected</div>';
            return;
        }

        const serversHtml = servers.map(server => {
            const isEnabled = this.settings.serverSettings[server.id]?.enabled !== false;
            const toolsCount = server.tools ? server.tools.length : 0;
            
            return `
                <div class="server-item">
                    <div class="server-header">
                        <div class="server-name">${server.name || server.id}</div>
                        <div class="server-status">
                            <div class="status-dot ${server.connected ? '' : 'disconnected'}"></div>
                            <span>${server.connected ? 'Connected' : 'Disconnected'}</span>
                            <label class="toggle">
                                <input type="checkbox" ${isEnabled ? 'checked' : ''} 
                                       onchange="settingsManager.toggleServer('${server.id}', this.checked)">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="server-info">
                        ${server.description || 'No description available'}
                    </div>
                    <div class="server-tools">
                        ${server.tools ? server.tools.slice(0, 5).map(tool => 
                            `<span class="tool-tag">${tool.name}</span>`
                        ).join('') : ''}
                        ${toolsCount > 5 ? `<span class="tool-tag">+${toolsCount - 5} more</span>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        serversList.innerHTML = serversHtml;
    }

    toggleServer(serverId, enabled) {
        if (!this.settings.serverSettings[serverId]) {
            this.settings.serverSettings[serverId] = {};
        }
        this.settings.serverSettings[serverId].enabled = enabled;
        this.saveSettings();
        this.notifyContentScript('serverSettings', this.settings.serverSettings);
    }

    async sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    exportSettings() {
        const settingsData = {
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            settings: this.settings
        };

        const dataStr = JSON.stringify(settingsData, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
        
        const exportFileDefaultName = `mcp-bridge-settings-${new Date().toISOString().split('T')[0]}.json`;
        
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();

        this.showNotification('Settings exported successfully', 'success');
    }

    importSettings() {
        document.getElementById('settingsFileInput').click();
    }

    handleFileImport(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);
                
                if (importedData.settings) {
                    // Merge with defaults to ensure all required settings exist
                    this.settings = { ...this.getDefaultSettings(), ...importedData.settings };
                    this.saveSettings();
                    this.updateUI();
                    this.showNotification('Settings imported successfully', 'success');
                } else {
                    throw new Error('Invalid settings file format');
                }
            } catch (error) {
                console.error('Failed to import settings:', error);
                this.showNotification('Failed to import settings: Invalid file format', 'error');
            }
        };
        reader.readAsText(file);
    }

    async resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
            this.settings = this.getDefaultSettings();
            await this.saveSettings();
            this.updateUI();
            this.showNotification('Settings reset to defaults', 'success');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '16px 24px',
            borderRadius: '8px',
            color: '#fff',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '1000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });

        // Set background color based on type
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#20b2aa'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize settings manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.settingsManager = new SettingsManager();
});

// Make it globally available for inline event handlers
window.settingsManager = null;
